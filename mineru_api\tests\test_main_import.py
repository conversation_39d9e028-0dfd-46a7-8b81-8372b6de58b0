#!/usr/bin/env python3
"""
测试主程序导入
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🧪 测试主程序导入")

try:
    print("1️⃣ 导入主程序...")

    # 设置工作目录到mineru_api
    import os
    os.chdir("..")

    # 导入主程序
    import main

    print("   ✅ 主程序导入成功")
    print("   ✅ 认证系统初始化成功")

    print("\n🎉 测试通过！现在可以正常启动服务了")

except Exception as e:
    print(f"\n❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
