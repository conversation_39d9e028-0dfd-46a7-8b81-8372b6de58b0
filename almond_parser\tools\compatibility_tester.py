#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
解析模式兼容性测试工具
"""
import asyncio
import sys
from typing import List, Dict, Any
import click
from loguru import logger
from tabulate import tabulate

from almond_parser.db.database import get_async_session, init_database
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus, ServiceType, ParseMode
from almond_parser.utils.node_selector import NodeSelector
from almond_parser.utils.parse_mode_compatibility import ParseModeCompatibility


@click.group()
def cli():
    """解析模式兼容性测试工具"""
    pass


@cli.command()
def show_compatibility():
    """显示兼容性矩阵"""
    click.echo("📋 解析模式兼容性矩阵:")
    click.echo("=" * 80)
    
    compatibility_info = ParseModeCompatibility.get_compatibility_info()
    
    headers = ["节点模式", "优先级", "描述", "可处理的请求模式"]
    rows = []
    
    for node_mode, info in compatibility_info.items():
        rows.append([
            node_mode,
            info["priority"],
            info["description"][:40] + "..." if len(info["description"]) > 40 else info["description"],
            ", ".join(info["can_handle"][:3]) + ("..." if len(info["can_handle"]) > 3 else "")
        ])
    
    click.echo(tabulate(rows, headers=headers, tablefmt="grid"))
    
    click.echo(f"\n🔍 详细兼容性规则:")
    for node_mode, info in compatibility_info.items():
        click.echo(f"\n{node_mode}:")
        click.echo(f"  - VLM能力: {'✓' if info['is_vlm_capable'] else '✗'}")
        click.echo(f"  - Pipeline能力: {'✓' if info['is_pipeline_capable'] else '✗'}")
        click.echo(f"  - 可处理请求: {', '.join(info['can_handle'])}")


@cli.command()
@click.option('--request-mode', required=True, help='请求的解析模式')
async def find_compatible_nodes(request_mode):
    """查找可以处理指定请求模式的节点"""
    await init_database()
    
    click.echo(f"🔍 查找可以处理 '{request_mode}' 请求的节点...")
    
    # 获取兼容的节点模式
    compatible_modes = ParseModeCompatibility.get_compatible_nodes_modes(request_mode)
    
    if not compatible_modes:
        click.echo(f"❌ 没有节点可以处理 '{request_mode}' 请求")
        return
    
    click.echo(f"📋 兼容的节点模式 (按优先级排序): {', '.join(compatible_modes)}")
    
    async with get_async_session() as db:
        try:
            # 查找实际的节点
            node_selector = NodeSelector(db)
            
            # 测试不同服务类型
            service_types = ["document", "knowledge_base", "universal"]
            
            for service_type in service_types:
                click.echo(f"\n🎯 {service_type.upper()} 服务类型:")
                
                node = await node_selector.select_node(
                    service_type=service_type,
                    parse_mode=request_mode,
                    use_compatibility=True
                )
                
                if node:
                    execution_mode = getattr(node, '_execution_mode', request_mode)
                    click.echo(f"  ✅ 找到节点: {node.name}")
                    click.echo(f"     节点模式: {node.parse_mode.value}")
                    click.echo(f"     执行模式: {execution_mode}")
                    click.echo(f"     当前任务: {node.current_tasks}/{node.max_concurrent_tasks}")
                    click.echo(f"     优先级: {node.priority}")
                else:
                    click.echo(f"  ❌ 未找到可用节点")
                    
        except Exception as e:
            logger.error(f"查找节点失败: {e}")
            click.echo(f"错误: {e}")


@cli.command()
async def test_all_combinations():
    """测试所有请求模式和节点模式的组合"""
    await init_database()
    
    click.echo("🧪 测试所有解析模式组合...")
    
    # 所有可能的模式
    all_modes = ["pipeline", "sglang", "vlm", "vlm-sglang-client", "vlm-transformers", "auto"]
    
    # 测试矩阵
    test_results = []
    
    for request_mode in all_modes:
        for node_mode in all_modes:
            compatible = ParseModeCompatibility.can_handle_request(node_mode, request_mode)
            execution_mode = ParseModeCompatibility.get_best_execution_mode(node_mode, request_mode)
            
            test_results.append({
                "request_mode": request_mode,
                "node_mode": node_mode,
                "compatible": compatible,
                "execution_mode": execution_mode
            })
    
    # 按兼容性分组显示
    compatible_results = [r for r in test_results if r["compatible"]]
    incompatible_results = [r for r in test_results if not r["compatible"]]
    
    click.echo(f"\n✅ 兼容组合 ({len(compatible_results)} 个):")
    headers = ["请求模式", "节点模式", "执行模式"]
    rows = [[r["request_mode"], r["node_mode"], r["execution_mode"]] for r in compatible_results]
    click.echo(tabulate(rows, headers=headers, tablefmt="grid"))
    
    click.echo(f"\n❌ 不兼容组合 ({len(incompatible_results)} 个):")
    rows = [[r["request_mode"], r["node_mode"], "N/A"] for r in incompatible_results]
    click.echo(tabulate(rows, headers=headers, tablefmt="grid"))


@cli.command()
async def list_nodes():
    """列出所有节点及其配置"""
    await init_database()
    
    async with get_async_session() as db:
        try:
            from sqlalchemy import select
            
            result = await db.execute(
                select(MinerUNode)
                .where(MinerUNode.is_enabled == True)
                .order_by(MinerUNode.service_type, MinerUNode.priority.desc())
            )
            nodes = result.scalars().all()
            
            if not nodes:
                click.echo("❌ 未找到启用的节点")
                return
            
            click.echo(f"📋 节点列表 ({len(nodes)} 个):")
            
            headers = [
                "ID", "名称", "状态", "服务类型", "解析模式", 
                "任务数", "优先级", "URL"
            ]
            
            rows = []
            for node in nodes:
                rows.append([
                    node.id,
                    node.name[:15] + "..." if len(node.name) > 15 else node.name,
                    node.status.value,
                    node.service_type.value,
                    node.parse_mode.value,
                    f"{node.current_tasks}/{node.max_concurrent_tasks}",
                    node.priority,
                    node.base_url[:30] + "..." if len(node.base_url) > 30 else node.base_url
                ])
            
            click.echo(tabulate(rows, headers=headers, tablefmt="grid"))
            
            # 统计信息
            click.echo(f"\n📊 统计信息:")
            
            # 按服务类型统计
            service_stats = {}
            for node in nodes:
                service_type = node.service_type.value
                service_stats[service_type] = service_stats.get(service_type, 0) + 1
            
            click.echo("  服务类型分布:")
            for service_type, count in service_stats.items():
                click.echo(f"    {service_type}: {count}")
            
            # 按解析模式统计
            mode_stats = {}
            for node in nodes:
                parse_mode = node.parse_mode.value
                mode_stats[parse_mode] = mode_stats.get(parse_mode, 0) + 1
            
            click.echo("  解析模式分布:")
            for parse_mode, count in mode_stats.items():
                click.echo(f"    {parse_mode}: {count}")
                
        except Exception as e:
            logger.error(f"查询节点失败: {e}")
            click.echo(f"错误: {e}")


@cli.command()
@click.option('--request-mode', required=True, help='请求的解析模式')
@click.option('--service-type', default='document', help='服务类型')
async def simulate_selection(request_mode, service_type):
    """模拟节点选择过程"""
    await init_database()
    
    click.echo(f"🎯 模拟节点选择:")
    click.echo(f"  请求模式: {request_mode}")
    click.echo(f"  服务类型: {service_type}")
    click.echo("-" * 50)
    
    async with get_async_session() as db:
        try:
            node_selector = NodeSelector(db)
            
            # 显示兼容性分析
            compatible_modes = ParseModeCompatibility.get_compatible_nodes_modes(request_mode)
            click.echo(f"🔍 兼容的节点模式: {', '.join(compatible_modes)}")
            
            # 执行选择
            node = await node_selector.select_node(
                service_type=service_type,
                parse_mode=request_mode,
                use_compatibility=True
            )
            
            if node:
                execution_mode = getattr(node, '_execution_mode', request_mode)
                click.echo(f"\n✅ 选择结果:")
                click.echo(f"  节点: {node.name} (ID: {node.id})")
                click.echo(f"  节点模式: {node.parse_mode.value}")
                click.echo(f"  执行模式: {execution_mode}")
                click.echo(f"  服务类型: {node.service_type.value}")
                click.echo(f"  当前负载: {node.current_tasks}/{node.max_concurrent_tasks}")
                click.echo(f"  优先级: {node.priority}")
                
                # 验证兼容性
                is_compatible = ParseModeCompatibility.can_handle_request(
                    node.parse_mode.value, request_mode
                )
                click.echo(f"  兼容性验证: {'✅ 通过' if is_compatible else '❌ 失败'}")
                
            else:
                click.echo(f"\n❌ 未找到可用节点")
                
                # 分析原因
                click.echo(f"\n🔍 失败原因分析:")
                
                # 检查是否有兼容的节点模式
                if not compatible_modes:
                    click.echo(f"  - 没有节点模式可以处理 '{request_mode}' 请求")
                else:
                    click.echo(f"  - 存在兼容模式，但可能:")
                    click.echo(f"    * 所有节点都离线")
                    click.echo(f"    * 所有节点都达到最大并发数")
                    click.echo(f"    * 没有匹配的服务类型")
                    
        except Exception as e:
            logger.error(f"模拟选择失败: {e}")
            click.echo(f"错误: {e}")


if __name__ == "__main__":
    # 设置异步支持
    def async_command(f):
        def wrapper(*args, **kwargs):
            return asyncio.run(f(*args, **kwargs))
        return wrapper
    
    # 为所有异步命令添加异步支持
    for command in cli.commands.values():
        if asyncio.iscoroutinefunction(command.callback):
            command.callback = async_command(command.callback)
    
    cli()
