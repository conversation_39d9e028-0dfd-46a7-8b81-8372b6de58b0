# -*- encoding: utf-8 -*-
"""
SGLang 服务告警服务
"""
import asyncio
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from loguru import logger

from almond_parser.db.models import MinerUNode, SglangStatus, ParseMode
from almond_parser.config import settings


class SglangAlertService:
    """SGLang 服务告警服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        # 告警频率控制：同一节点1小时内最多告警1次
        self._alert_cache: Dict[int, datetime] = {}
        self._alert_cooldown = timedelta(hours=1)
    
    def send_wecom_message(self, content: str, webhook_url: str = None):
        """
        推送消息到企业微信机器人（默认使用 env 中配置）
        """
        try:
            webhook_url = webhook_url or settings.wecom_webhook_url
            payload = {"msgtype": "markdown", "markdown": {"content": content}}
            resp = requests.post(webhook_url, json=payload, timeout=5)
            logger.info(f"企业微信推送成功: {resp.json()}")
            if resp.status_code != 200:
                logger.warning(f"企业微信推送失败: {resp.text}")
        except Exception as e:
            logger.exception(f"企业微信推送异常: {e}")
    
    def send_monitor_event(self, title: str, content: str, tags: List[str] = None):
        """发送监控事件"""
        try:
            tag_str = " | ".join(tags) if tags else ""
            msg = f"""### 🚨 小杏仁监控告警  
**标题**：{title}  
**标签**：{tag_str}  
**时间**：<font color="comment">{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</font>  

{content}
"""
            self.send_wecom_message(msg)
        except Exception as e:
            logger.error(f"监控上报失败: {e}")
    
    def should_send_alert(self, node_id: int) -> bool:
        """检查是否应该发送告警（频率控制）"""
        now = datetime.now()
        last_alert = self._alert_cache.get(node_id)
        
        if last_alert is None:
            # 第一次告警
            self._alert_cache[node_id] = now
            return True
        
        if now - last_alert >= self._alert_cooldown:
            # 超过冷却时间
            self._alert_cache[node_id] = now
            return True
        
        # 在冷却时间内
        return False
    
    async def send_sglang_offline_alert(self, node: MinerUNode) -> bool:
        """发送SGLang服务离线告警"""
        try:
            if not self.should_send_alert(node.id):
                logger.debug(f"节点 {node.id} 告警频率限制，跳过发送")
                return False
            
            title = f"SGLang服务离线告警 - {node.name}"
            content = f"""
**节点信息**：
- 节点ID: {node.id}
- 节点名称: {node.name}
- MinerU-API地址: {node.base_url}
- SGLang地址: {node.sglang_url or '未配置'}

**状态信息**：
- SGLang状态: {node.sglang_status.value}
- 连续失败次数: {node.sglang_consecutive_failures}
- 最后检查时间: {node.sglang_last_check.strftime('%Y-%m-%d %H:%M:%S') if node.sglang_last_check else '未知'}
- 重启次数: {node.sglang_restart_count}

**建议操作**：
1. 检查MinerU-API服务是否正常运行
2. 通过MinerU-API查看SGLang服务状态
3. 查看MinerU-API和SGLang服务日志
4. 检查GPU资源使用情况
5. 考虑通过MinerU-API重启SGLang服务

请及时处理以确保解析服务正常运行！
"""
            
            tags = ["SGLang", "服务离线", f"节点{node.id}", node.name]
            self.send_monitor_event(title, content, tags)
            
            logger.info(f"已发送SGLang离线告警: 节点 {node.id} ({node.name})")
            return True
            
        except Exception as e:
            logger.error(f"发送SGLang离线告警失败: {e}")
            return False
    
    async def send_sglang_restart_failed_alert(self, node: MinerUNode, error: str) -> bool:
        """发送SGLang服务重启失败告警"""
        try:
            if not self.should_send_alert(node.id):
                logger.debug(f"节点 {node.id} 告警频率限制，跳过发送")
                return False
            
            title = f"SGLang服务重启失败告警 - {node.name}"
            cycle_start_str = node.sglang_restart_cycle_start.strftime('%Y-%m-%d %H:%M:%S') if node.sglang_restart_cycle_start else '未知'

            content = f"""
**节点信息**：
- 节点ID: {node.id}
- 节点名称: {node.name}
- 节点地址: {node.base_url}
- SGLang地址: {node.sglang_url}

**重启周期信息**：
- 重启失败原因: {error}
- 当前周期重启次数: {node.sglang_current_cycle_restarts}/2
- 周期开始时间: {cycle_start_str}
- 最后重启时间: {node.sglang_last_restart.strftime('%Y-%m-%d %H:%M:%S') if node.sglang_last_restart else '未知'}
- 历史总重启次数: {node.sglang_restart_count}

**状态信息**：
- SGLang状态: {node.sglang_status.value}
- 连续失败次数: {node.sglang_consecutive_failures}

**处理说明**：
系统已按照重启策略自动重启2次，但SGLang服务仍无法恢复正常。

**紧急处理**：
1. 立即检查节点服务器状态
2. 查看SGLang和MinerU-API日志
3. 检查GPU和系统资源状态
4. 检查网络连接和端口占用
5. 考虑手动登录服务器处理

⚠️ **自动重启已达上限，需要人工介入处理！**
"""
            
            tags = ["SGLang", "重启失败", f"节点{node.id}", node.name, "紧急"]
            self.send_monitor_event(title, content, tags)
            
            logger.warning(f"已发送SGLang重启失败告警: 节点 {node.id} ({node.name})")
            return True
            
        except Exception as e:
            logger.error(f"发送SGLang重启失败告警失败: {e}")
            return False
    
    async def send_batch_alert_summary(self, failed_nodes: List[Dict[str, Any]]) -> bool:
        """发送批量告警汇总"""
        try:
            if not failed_nodes:
                return False
            
            title = f"SGLang服务批量异常告警 ({len(failed_nodes)}个节点)"
            
            # 构建节点列表
            node_list = []
            for node_info in failed_nodes:
                node_list.append(
                    f"- 节点{node_info['node_id']}: {node_info.get('name', '未知')} "
                    f"({node_info.get('status', '未知')}, 失败{node_info.get('failures', 0)}次)"
                )
            
            content = f"""
**异常节点汇总** ({len(failed_nodes)} 个)：
{chr(10).join(node_list)}

**统计信息**：
- 离线节点: {sum(1 for n in failed_nodes if n.get('status') == 'offline')}
- 错误节点: {sum(1 for n in failed_nodes if n.get('status') == 'error')}
- 重启中节点: {sum(1 for n in failed_nodes if n.get('status') == 'restarting')}

**建议操作**：
1. 优先处理连续失败次数多的节点
2. 检查是否存在系统性问题
3. 考虑批量重启或维护

请及时处理以确保解析服务稳定运行！
"""
            
            tags = ["SGLang", "批量异常", f"{len(failed_nodes)}节点"]
            self.send_monitor_event(title, content, tags)
            
            logger.warning(f"已发送SGLang批量异常告警: {len(failed_nodes)} 个节点")
            return True
            
        except Exception as e:
            logger.error(f"发送批量告警汇总失败: {e}")
            return False
    
    async def check_and_send_alerts(self) -> Dict[str, Any]:
        """检查并发送告警"""
        try:
            # 导入重启服务来检查告警需求
            from .sglang_restart_service import SglangRestartService
            restart_service = SglangRestartService(self.db)

            # 获取所有启用的节点，但排除Pipeline模式的节点（Pipeline模式不需要SGLang服务）
            result = await self.db.execute(
                select(MinerUNode).where(
                    and_(
                        MinerUNode.is_enabled == True,
                        MinerUNode.parse_mode != ParseMode.PIPELINE  # 排除Pipeline模式节点
                    )
                )
            )
            nodes = result.scalars().all()

            alert_count = 0
            failed_nodes = []

            for node in nodes:
                # 检查是否需要发送告警（已重启2次且仍然失败）
                if await restart_service.check_alert_needed(node):
                    # 收集失败节点信息
                    failed_nodes.append({
                        "node_id": node.id,
                        "name": node.name,
                        "status": node.sglang_status.value,
                        "failures": node.sglang_consecutive_failures,
                        "cycle_restarts": node.sglang_current_cycle_restarts,
                        "url": node.sglang_url
                    })

                    # 发送重启失败告警
                    if await self.send_sglang_restart_failed_alert(
                        node,
                        f"已重启{node.sglang_current_cycle_restarts}次仍无法恢复"
                    ):
                        alert_count += 1
                        # 标记告警已发送
                        await restart_service.mark_alert_sent(node)

            if not failed_nodes:
                return {
                    "success": True,
                    "message": "没有需要告警的节点",
                    "alert_count": 0
                }

            logger.info(f"发现 {len(failed_nodes)} 个节点需要告警")

            # 如果有多个节点异常，发送汇总告警
            if len(failed_nodes) >= 3:
                await self.send_batch_alert_summary(failed_nodes)
                alert_count += 1

            return {
                "success": True,
                "total_nodes": len(failed_nodes),
                "alert_count": alert_count,
                "failed_nodes": failed_nodes,
                "message": f"检查了{len(nodes)}个节点，发现{len(failed_nodes)}个需要告警，发送了{alert_count}个告警"
            }

        except Exception as e:
            logger.error(f"检查并发送告警失败: {e}")
            return {"success": False, "error": str(e)}
