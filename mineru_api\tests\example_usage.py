"""
使用示例 - 演示如何使用 MineruAPI 服务
"""
import asyncio
import json
from pathlib import Path
from test_client import MineruAPIClient
from loguru import logger


async def example_basic_usage():
    """基础使用示例"""
    logger.info("=== 基础使用示例 ===")
    
    client = MineruAPIClient("http://localhost:8000")
    
    try:
        # 1. 健康检查
        health = await client.health_check()
        logger.info(f"服务状态: {health}")
        
        # 2. 查找测试文件
        demo_dir = Path(__file__).parent.parent / "demo" / "pdfs"
        test_files = list(demo_dir.glob("*.pdf"))
        
        if not test_files:
            logger.error("未找到测试PDF文件")
            return
        
        test_file = test_files[0]
        logger.info(f"使用测试文件: {test_file.name}")
        
        # 3. 提交解析任务
        result = await client.parse_file_upload(
            test_file,
            lang="ch",
            backend="pipeline",
            method="auto",
            dump_md=True,
            dump_content_list=True
        )
        
        task_id = result["task_id"]
        logger.info(f"任务已提交: {task_id}")
        
        # 4. 轮询任务状态
        final_status = await client.wait_for_completion(task_id, timeout=300)
        
        if final_status and final_status["status"] == "completed":
            logger.success("任务完成!")
            result_data = final_status.get("result", {})
            files = result_data.get("files", {})
            
            logger.info("生成的文件:")
            for file_type, file_path in files.items():
                logger.info(f"  {file_type}: {file_path}")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
    finally:
        await client.close()


async def example_with_callback():
    """带回调的使用示例"""
    logger.info("=== 带回调的使用示例 ===")
    logger.info("请先启动回调测试服务器: python callback_test_server.py")
    
    client = MineruAPIClient("http://localhost:8000")
    
    try:
        # 查找测试文件
        demo_dir = Path(__file__).parent.parent / "demo" / "pdfs"
        test_files = list(demo_dir.glob("*.pdf"))
        
        if not test_files:
            logger.error("未找到测试PDF文件")
            return
        
        test_file = test_files[0]
        
        # 提交带回调的任务
        result = await client.parse_file_upload(
            test_file,
            lang="ch",
            backend="pipeline",
            callback_url="http://localhost:9000/webhook"  # 回调测试服务器
        )
        
        task_id = result["task_id"]
        logger.info(f"带回调的任务已提交: {task_id}")
        logger.info("请查看回调测试服务器的日志以确认回调通知")
        
        # 等待一段时间让任务完成
        await asyncio.sleep(10)
        
        # 检查最终状态
        status = await client.get_task_status(task_id)
        logger.info(f"最终状态: {status['status']}")
        
    except Exception as e:
        logger.error(f"回调示例执行失败: {e}")
    finally:
        await client.close()


async def example_batch_processing():
    """批量处理示例"""
    logger.info("=== 批量处理示例 ===")
    
    client = MineruAPIClient("http://localhost:8000")
    
    try:
        # 查找所有测试文件
        demo_dir = Path(__file__).parent.parent / "demo" / "pdfs"
        test_files = list(demo_dir.glob("*.pdf"))[:3]  # 限制为3个文件
        
        if not test_files:
            logger.error("未找到测试PDF文件")
            return
        
        # 提交多个任务
        task_ids = []
        for test_file in test_files:
            result = await client.parse_file_upload(
                test_file,
                lang="ch",
                backend="pipeline"
            )
            task_ids.append(result["task_id"])
            logger.info(f"提交任务: {result['task_id']} ({test_file.name})")
        
        # 等待所有任务完成
        logger.info("等待所有任务完成...")
        completed_tasks = []
        
        for task_id in task_ids:
            status = await client.wait_for_completion(task_id, timeout=300)
            if status:
                completed_tasks.append(status)
        
        # 统计结果
        success_count = sum(1 for task in completed_tasks if task["status"] == "completed")
        failed_count = len(completed_tasks) - success_count
        
        logger.info(f"批量处理完成: 成功 {success_count} 个，失败 {failed_count} 个")
        
    except Exception as e:
        logger.error(f"批量处理示例执行失败: {e}")
    finally:
        await client.close()


async def main():
    """主函数"""
    logger.info("MineruAPI 使用示例")
    logger.info("请确保 MineruAPI 服务已启动 (python start_server.py)")
    
    # 等待用户确认
    input("按 Enter 键开始基础使用示例...")
    await example_basic_usage()
    
    input("\n按 Enter 键开始带回调的示例 (需要先启动回调测试服务器)...")
    await example_with_callback()
    
    input("\n按 Enter 键开始批量处理示例...")
    await example_batch_processing()
    
    logger.info("所有示例执行完成!")


if __name__ == "__main__":
    asyncio.run(main())
