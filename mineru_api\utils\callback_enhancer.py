"""
回调增强器 - 在不修改现有代码的情况下增强回调功能
通过猴子补丁的方式增强现有的回调处理
"""
import functools
from typing import Optional, Dict
from loguru import logger

from ..services.callback_tracker import callback_tracker


def enhance_callback_handler():
    """增强现有的回调处理器"""
    from ..utils.callback_utils import CallbackHandler
    
    # 保存原始方法
    original_send_callback_sync = CallbackHandler.send_callback_sync
    original_run_async_callback = CallbackHandler._run_async_callback
    
    def enhanced_send_callback_sync(self, task_id: str, callback_url: str, 
                                   headers: Optional[Dict[str, str]] = None):
        """增强的同步回调发送方法"""
        # 开始跟踪回调
        callback_tracker.track_callback(task_id, callback_url, headers)
        
        # 调用原始方法
        return original_send_callback_sync(self, task_id, callback_url, headers)
    
    def enhanced_run_async_callback(self, task_id: str, callback_url: str, 
                                   headers: Optional[Dict[str, str]] = None):
        """增强的异步回调执行方法"""
        try:
            # 调用原始方法
            result = original_run_async_callback(self, task_id, callback_url, headers)
            
            # 标记回调成功（这里假设没有异常就是成功）
            callback_tracker.mark_callback_success(task_id)
            
            return result
            
        except Exception as e:
            # 标记回调失败
            callback_tracker.mark_callback_failed(task_id, str(e))
            raise
    
    # 应用增强
    CallbackHandler.send_callback_sync = enhanced_send_callback_sync
    CallbackHandler._run_async_callback = enhanced_run_async_callback
    
    logger.info("回调处理器已增强")


def enhance_callback_service():
    """增强回调服务"""
    from ..services.callback_service import CallbackService
    
    # 保存原始方法
    original_send_callback = CallbackService.send_callback
    
    async def enhanced_send_callback(self, task_id: str, callback_url: str,
                                   headers: Optional[Dict[str, str]] = None):
        """增强的回调发送方法"""
        try:
            # 调用原始方法
            result = await original_send_callback(self, task_id, callback_url, headers)
            
            # 检查日志来判断是否成功
            # 这是一个简化的实现，实际可能需要更复杂的逻辑
            callback_tracker.mark_callback_success(task_id)
            
            return result
            
        except Exception as e:
            # 标记回调失败
            callback_tracker.mark_callback_failed(task_id, str(e))
            raise
    
    # 应用增强
    CallbackService.send_callback = enhanced_send_callback
    
    logger.info("回调服务已增强")


def init_callback_enhancements():
    """初始化回调增强功能"""
    try:
        # 增强回调处理器
        enhance_callback_handler()
        
        # 增强回调服务
        enhance_callback_service()
        
        # 启动回调跟踪器的重试工作线程
        callback_tracker.start_retry_worker()
        
        logger.info("回调增强功能已初始化")
        
    except Exception as e:
        logger.error(f"初始化回调增强功能失败: {e}")


def cleanup_callback_enhancements():
    """清理回调增强功能"""
    try:
        # 停止回调跟踪器的重试工作线程
        callback_tracker.stop_retry_worker()
        
        logger.info("回调增强功能已清理")
        
    except Exception as e:
        logger.error(f"清理回调增强功能失败: {e}")


# 装饰器：用于增强任务管理器的方法
def track_callback_execution(func):
    """装饰器：跟踪回调执行"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 提取任务ID和回调URL
        task_id = None
        callback_url = None
        
        # 从参数中提取信息
        if len(args) >= 4:  # submit_task方法的参数
            task_id = args[1]
            request = args[3]
            if hasattr(request, 'callback_url'):
                callback_url = request.callback_url
        
        # 如果有回调URL，开始跟踪
        if task_id and callback_url:
            callback_tracker.track_callback(task_id, callback_url)
        
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            # 如果任务执行失败，也要确保回调被跟踪
            if task_id and callback_url:
                logger.warning(f"任务执行失败，但回调跟踪继续: {task_id}")
            raise
    
    return wrapper
