# -*- encoding: utf-8 -*-
"""
节点并发控制测试
"""
import asyncio
import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus, ServiceType, ParseMode
from almond_parser.utils.node_concurrency_manager import node_concurrency_manager


@pytest.fixture
async def test_node(db_session: AsyncSession):
    """创建测试节点"""
    node = MinerUNode(
        name="test_node",
        host="localhost",
        port=8000,
        base_url="http://localhost:8000",
        parse_mode=ParseMode.PIPELINE,
        service_type=ServiceType.DOCUMENT,
        max_concurrent_tasks=3,
        priority=1,
        status=NodeStatus.ONLINE,
        is_enabled=True,
        current_tasks=0,
        reserved_tasks=0
    )
    
    db_session.add(node)
    await db_session.commit()
    await db_session.refresh(node)
    
    return node


@pytest.mark.asyncio
async def test_concurrent_allocation(db_session: AsyncSession, test_node: MinerUNode):
    """测试并发分配是否正确控制"""
    
    # 模拟6个并发任务同时请求节点（节点最大并发为3）
    async def allocate_task(task_id: int):
        return await node_concurrency_manager.allocate_node_with_lock(
            db=db_session,
            service_type=ServiceType.DOCUMENT,
            parse_mode="pipeline",
            document_id=f"doc_{task_id}"
        )
    
    # 并发执行6个分配请求
    tasks = [allocate_task(i) for i in range(6)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 统计成功分配的数量
    successful_allocations = [r for r in results if r is not None and not isinstance(r, Exception)]
    
    # 应该只有3个成功分配（节点最大并发数）
    assert len(successful_allocations) == 3
    
    # 检查节点状态
    await db_session.refresh(test_node)
    assert test_node.reserved_tasks == 3
    assert test_node.current_tasks == 0  # 还未确认开始


@pytest.mark.asyncio
async def test_task_lifecycle(db_session: AsyncSession, test_node: MinerUNode):
    """测试完整的任务生命周期"""
    
    # 1. 分配节点
    allocated_node = await node_concurrency_manager.allocate_node_with_lock(
        db=db_session,
        service_type=ServiceType.DOCUMENT,
        parse_mode="pipeline",
        document_id="test_doc"
    )
    
    assert allocated_node is not None
    assert allocated_node.id == test_node.id
    
    # 2. 确认任务开始
    confirmed = await node_concurrency_manager.confirm_task_start(
        db=db_session,
        node_id=test_node.id,
        task_id="test_task",
        document_id="test_doc"
    )
    
    assert confirmed is True
    
    # 检查状态变化
    await db_session.refresh(test_node)
    assert test_node.reserved_tasks == 0
    assert test_node.current_tasks == 1
    
    # 3. 释放任务槽位
    released = await node_concurrency_manager.release_task_slot(
        db=db_session,
        node_id=test_node.id,
        task_id="test_task",
        success=True,
        reason="任务完成"
    )
    
    assert released is True
    
    # 检查最终状态
    await db_session.refresh(test_node)
    assert test_node.reserved_tasks == 0
    assert test_node.current_tasks == 0
    assert test_node.success_tasks == 1


@pytest.mark.asyncio
async def test_cleanup_expired_reservations(db_session: AsyncSession, test_node: MinerUNode):
    """测试清理过期预留"""
    
    # 设置过期的预留
    test_node.reserved_tasks = 2
    test_node.last_task_assigned_at = datetime.now() - timedelta(minutes=10)  # 10分钟前
    await db_session.commit()
    
    # 执行清理
    cleanup_stats = await node_concurrency_manager.cleanup_expired_reservations(db_session)
    
    # 检查清理结果
    assert cleanup_stats["cleaned_nodes"] == 1
    assert cleanup_stats["released_reservations"] == 2
    
    # 检查节点状态
    await db_session.refresh(test_node)
    assert test_node.reserved_tasks == 0


@pytest.mark.asyncio
async def test_timeout_task_cleanup(db_session: AsyncSession, test_node: MinerUNode):
    """测试超时任务清理"""
    
    from almond_parser.db.models.document import Document, DocumentStatus
    
    # 创建超时的解析任务
    document = Document(
        document_id="timeout_doc",
        file_name="test.pdf",
        file_path="/tmp/test.pdf",
        file_type="pdf",
        file_size=1024,
        user_id="test_user",
        status=DocumentStatus.PARSING,
        node_id=test_node.id,
        task_id="timeout_task",
        started_at=datetime.now() - timedelta(minutes=35)  # 35分钟前开始
    )
    
    # 设置节点有当前任务
    test_node.current_tasks = 1
    
    db_session.add(document)
    await db_session.commit()
    
    # 执行超时清理
    cleanup_stats = await node_concurrency_manager.cleanup_timeout_tasks(db_session)
    
    # 检查清理结果
    assert cleanup_stats["timeout_tasks"] == 1
    assert cleanup_stats["released_slots"] == 1
    
    # 检查文档状态
    await db_session.refresh(document)
    assert document.status == DocumentStatus.FAILED
    assert "任务超时" in document.error_message
    
    # 检查节点状态
    await db_session.refresh(test_node)
    assert test_node.current_tasks == 0
    assert test_node.failed_tasks == 1


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
