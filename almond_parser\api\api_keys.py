from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from almond_parser.db import get_db
from almond_parser.schemas.api_key import ApiKeyC<PERSON>, ApiKeyUpdate, ApiKeyResponse
from almond_parser.schemas.base import BaseResponse
from almond_parser.services.api_key_service import ApiKeyService
from almond_parser.utils.auth import get_current_user

router = APIRouter(prefix="/api-keys", tags=["API 密钥管理"])


@router.post("/", response_model=ApiKeyResponse)
async def create_api_key(
    api_key_data: ApiKeyCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建新的API密钥"""
    try:
        service = ApiKeyService(db)
        api_key = await service.create_api_key(current_user["user_id"], api_key_data)
        logger.info(f"用户 {current_user['username']} 创建API密钥: {api_key.name}")
        return api_key
    except Exception as e:
        logger.error(f"创建API密钥失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建API密钥失败: {str(e)}"
        )


@router.get("/", response_model=List[ApiKeyResponse])
async def get_api_keys(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取当前用户的所有API密钥"""
    try:
        service = ApiKeyService(db)
        api_keys = await service.get_user_api_keys(current_user["user_id"])
        return api_keys
    except Exception as e:
        logger.error(f"获取API密钥列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取API密钥列表失败"
        )


@router.put("/{api_key_id}", response_model=ApiKeyResponse)
async def update_api_key(
    api_key_id: int,
    api_key_data: ApiKeyUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """更新API密钥"""
    try:
        service = ApiKeyService(db)
        api_key = await service.update_api_key(api_key_id, current_user["user_id"], api_key_data)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API密钥不存在"
            )
        logger.info(f"用户 {current_user['username']} 更新API密钥: {api_key.name}")
        return api_key
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新API密钥失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"更新API密钥失败: {str(e)}"
        )


@router.delete("/{api_key_id}", response_model=BaseResponse)
async def delete_api_key(
    api_key_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除API密钥"""
    try:
        service = ApiKeyService(db)
        success = await service.delete_api_key(api_key_id, current_user["user_id"])
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API密钥不存在"
            )
        logger.info(f"用户 {current_user['username']} 删除API密钥: {api_key_id}")
        return BaseResponse(message="API密钥删除成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除API密钥失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"删除API密钥失败: {str(e)}"
        )


@router.get("/default", response_model=ApiKeyResponse)
async def get_default_api_key(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取默认的API密钥（如果不存在则创建）"""
    try:
        service = ApiKeyService(db)
        api_key = await service.get_or_create_default_key(current_user["user_id"])
        logger.info(f"用户 {current_user['username']} 获取默认API密钥")
        return api_key
    except Exception as e:
        logger.error(f"获取默认API密钥失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"获取默认API密钥失败: {str(e)}"
        )
