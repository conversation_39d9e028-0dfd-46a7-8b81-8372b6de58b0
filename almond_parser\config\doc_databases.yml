# DOC数据库配置文件
# 支持配置多个数据库，每个数据库对应一个Tab标签

databases:
  - name: "internal"
    label: "AI中心内部"
    host: "localhost"
    port: 3306
    user: "root"
    password: "123456"
    database: "bdo_xxr2"
    charset: "utf8mb4"
    # 下载配置
    download:
      base_url: "http://127.0.0.1"
      docs_prefix: "/download-docs/"
      kb_prefix: "/download-kb/"
      kb_path_prefix: "/app/python/files/"

  - name: "external"
    label: "AI中心外部"
    host: "localhost"
    port: 3306
    user: "root"
    password: "123456"
    database: "bdo_xxr2"
    charset: "utf8mb4"
    # 下载配置
    download:
      base_url: "http://127.0.0.1"
      docs_prefix: "/download-docs/"
      kb_prefix: "/download-kb/"
      kb_path_prefix: "/app/python/files/"

  - name: "external2"
    label: "AI中心uat"
    host: "************"
    port: 5188
    user: "dev"
    password: "dev@2022"
    database: "bdo_xxr2"
    charset: "utf8mb4"
    # 下载配置
    download:
      base_url: "http://************"
      docs_prefix: "/download-docs/"
      kb_prefix: "/download-kb/"
      kb_path_prefix: "/app/python/files/"

# 配置说明:
# - name: 数据库标识名，用于API调用时指定数据库
# - label: 显示名称，用于前端Tab标签显示
# - host: 数据库主机地址
# - port: 数据库端口，默认3306
# - user: 数据库用户名
# - password: 数据库密码
# - database: 数据库名
# - charset: 字符集，默认utf8mb4
# - download: 下载配置（可选）
#   - base_url: 下载基础URL
#   - docs_prefix: 多文档下载路径前缀
#   - kb_prefix: 知识库下载路径前缀
#   - kb_path_prefix: 知识库文件路径前缀（用于截断）
