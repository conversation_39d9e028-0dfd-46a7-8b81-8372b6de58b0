#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试容量控制的任务分配
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))


async def test_capacity_control():
    """测试容量控制"""
    try:
        print("🧪 测试容量控制的任务分配...")
        
        from almond_parser.services.task_allocation_service import task_allocation_service
        
        # 测试新的容量控制分配
        print("📋 测试容量控制分配...")
        stats = await task_allocation_service.allocate_pending_tasks_with_capacity_control(
            max_allocations=5,
            available_capacity=3
        )
        
        print(f"✅ 分配结果: {stats}")
        
        if stats.get("allocated", 0) > 0:
            print("🎉 成功分配任务！")
        elif stats.get("reason") == "no_capacity":
            print("ℹ️  没有可用容量")
        elif stats.get("total_pending", 0) == 0:
            print("ℹ️  没有等待任务")
        else:
            print("⚠️  有等待任务但未能分配")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_capacity_control())
