version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: parserflow-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-parserflow123}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-parserflow}
      MYSQL_USER: ${MYSQL_USER:-parserflow}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-parserflow123}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - parserflow-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-parserflow123}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: parserflow-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - parserflow-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 杏仁解析服务
  almond-parser:
    image: almond-parser:latest
    container_name: parserflow-almond-parser
    restart: unless-stopped
    environment:
      DATABASE_URL: mysql+aiomysql://${MYSQL_USER:-parserflow}:${MYSQL_PASSWORD:-parserflow123}@mysql:3306/${MYSQL_DATABASE:-parserflow}
      REDIS_URL: redis://redis:6379/0
      HOST: 0.0.0.0
      PORT: 8000
      DEBUG: ${DEBUG:-false}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100MB}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
      MAX_CONCURRENT_TASKS: ${MAX_CONCURRENT_TASKS:-10}
      TASK_TIMEOUT: ${TASK_TIMEOUT:-300}
      NODE_HEALTH_CHECK_INTERVAL: ${NODE_HEALTH_CHECK_INTERVAL:-30}
      NODE_RETRY_INTERVAL: ${NODE_RETRY_INTERVAL:-60}
    ports:
      - "${ALMOND_PARSER_PORT:-8000}:8000"
    volumes:
      - uploads_data:/app/uploads
      - almond_logs:/app/logs
      - almond_output:/app/output
    networks:
      - parserflow-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # ARQ Worker
  almond-worker:
    image: almond-parser:latest
    container_name: parserflow-almond-worker
    restart: unless-stopped
    environment:
      DATABASE_URL: mysql+aiomysql://${MYSQL_USER:-parserflow}:${MYSQL_PASSWORD:-parserflow123}@mysql:3306/${MYSQL_DATABASE:-parserflow}
      REDIS_URL: redis://redis:6379/0
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
    volumes:
      - uploads_data:/app/uploads
      - almond_logs:/app/logs
      - almond_output:/app/output
    networks:
      - parserflow-network
    depends_on:
      - almond-parser
    command: ["uv", "run", "python", "worker.py"]

  # Web 前端
  web-frontend:
    image: web-frontend:latest
    container_name: parserflow-web
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: ${API_BASE_URL:-http://localhost/api}
      VITE_APP_TITLE: ${APP_TITLE:-杏仁解析管理平台}
      VITE_APP_VERSION: ${APP_VERSION:-1.0.0}
      VITE_ENABLE_UPLOAD: ${ENABLE_UPLOAD:-true}
      VITE_ENABLE_BATCH: ${ENABLE_BATCH:-true}
      VITE_ENABLE_MONITORING: ${ENABLE_MONITORING:-true}
      VITE_MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100}
      VITE_ALLOWED_FILE_TYPES: ${ALLOWED_FILE_TYPES:-pdf,doc,docx,ppt,pptx,jpg,png}
      VITE_THEME: ${THEME:-light}
      VITE_LANGUAGE: ${LANGUAGE:-zh-CN}
    volumes:
      - web_static:/usr/share/nginx/html
    networks:
      - parserflow-network
    depends_on:
      - almond-parser

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: parserflow-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
      - web_static:/usr/share/nginx/html:ro
    networks:
      - parserflow-network
    depends_on:
      - almond-parser
      - web-frontend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/nginx-health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  almond_logs:
    driver: local
  almond_output:
    driver: local
  nginx_logs:
    driver: local
  web_static:
    driver: local

networks:
  parserflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
