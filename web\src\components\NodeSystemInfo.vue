<template>
  <div class="node-system-info">
    <!-- 系统信息卡片 -->
    <el-card class="info-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>🖥️ {{ nodeName }} - 系统信息</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="refreshInfo" 
              :loading="loading"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button 
              size="small" 
              @click="toggleAutoRefresh"
              :type="autoRefresh ? 'success' : 'default'"
            >
              <el-icon><Timer /></el-icon>
              {{ autoRefresh ? '停止自动刷新' : '自动刷新' }}
            </el-button>
          </div>
        </div>
      </template>

      <div class="system-overview" v-if="systemInfo">
        <!-- 基本信息 -->
        <div class="info-section">
          <h4>🔧 基本信息</h4>
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">主机名:</span>
                <span class="value">{{ systemInfo.system.hostname }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">操作系统:</span>
                <span class="value">{{ systemInfo.system.platform }} {{ systemInfo.system.platform_release }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">架构:</span>
                <span class="value">{{ systemInfo.system.architecture }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- CPU信息 -->
        <div class="info-section">
          <h4>⚡ CPU信息</h4>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ systemInfo.cpu.physical_cores }}</div>
                <div class="metric-label">物理核心</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ systemInfo.cpu.total_cores }}</div>
                <div class="metric-label">逻辑核心</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ formatFrequency(systemInfo.cpu.current_frequency) }}</div>
                <div class="metric-label">当前频率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card cpu-usage" :class="getCpuUsageClass(resources?.cpu.usage_percent || 0)">
                <div class="metric-value">{{ (resources?.cpu.usage_percent || 0).toFixed(1) }}%</div>
                <div class="metric-label">CPU使用率</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 内存信息 -->
        <div class="info-section">
          <h4>💾 内存信息</h4>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ systemInfo.memory.total.toFixed(1) }}GB</div>
                <div class="metric-label">总内存</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ (resources?.memory.used_gb || 0).toFixed(1) }}GB</div>
                <div class="metric-label">已使用</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ (resources?.memory.available_gb || 0).toFixed(1) }}GB</div>
                <div class="metric-label">可用</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card memory-usage" :class="getMemoryUsageClass(resources?.memory.usage_percent || 0)">
                <div class="metric-value">{{ (resources?.memory.usage_percent || 0).toFixed(1) }}%</div>
                <div class="metric-label">内存使用率</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- GPU信息 -->
        <div class="info-section" v-if="systemInfo.gpu && systemInfo.gpu.length > 0">
          <h4><img src="/GPU.png" alt="GPU" class="section-icon" /><span class="title-text">GPU信息</span></h4>
          <el-row :gutter="16">
            <el-col :span="12" v-for="gpu in systemInfo.gpu" :key="gpu.index">
              <div class="gpu-card">
                <div class="gpu-header">
                  <span class="gpu-name">GPU{{ gpu.index }}: {{ gpu.name }}</span>
                  <el-tag :type="getGpuMemoryUsageTagType(gpu)" size="small">
                    {{ getGpuMemoryUsageForGpu(gpu) }}% 显存使用率
                  </el-tag>
                </div>
                <div class="gpu-metrics">
                  <div class="gpu-metric">
                    <span class="label">显存:</span>
                    <span class="value">{{ (gpu.memory_used_mb / 1024).toFixed(1) }}GB / {{ (gpu.memory_total_mb / 1024).toFixed(1) }}GB</span>
                  </div>
                  <div class="gpu-metric">
                    <span class="label">温度:</span>
                    <span class="value">{{ gpu.temperature || 'N/A' }}°C</span>
                  </div>
                  <div class="gpu-metric" v-if="gpu.power_draw">
                    <span class="label">功耗:</span>
                    <span class="value">{{ gpu.power_draw.toFixed(1) }}W / {{ gpu.power_limit?.toFixed(1) || 'N/A' }}W</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 磁盘信息 -->
        <div class="info-section" v-if="systemInfo.disk && systemInfo.disk.length > 0">
          <h4>💿 磁盘信息</h4>
          <el-table :data="systemInfo.disk" size="small" style="width: 100%">
            <el-table-column prop="device" label="设备" width="120" />
            <el-table-column prop="mountpoint" label="挂载点" width="120" />
            <el-table-column prop="file_system" label="文件系统" width="100" />
            <el-table-column label="容量" width="120">
              <template #default="{ row }">
                {{ row.total.toFixed(1) }}GB
              </template>
            </el-table-column>
            <el-table-column label="已使用" width="120">
              <template #default="{ row }">
                {{ row.used.toFixed(1) }}GB
              </template>
            </el-table-column>
            <el-table-column label="可用" width="120">
              <template #default="{ row }">
                {{ row.free.toFixed(1) }}GB
              </template>
            </el-table-column>
            <el-table-column label="使用率" width="100">
              <template #default="{ row }">
                <el-tag :type="getDiskUsageTagType(row.percentage)" size="small">
                  {{ row.percentage.toFixed(1) }}%
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 最后更新时间 -->
        <div class="update-time">
          <el-text type="info" size="small">
            最后更新: {{ formatTime(lastUpdateTime) }}
          </el-text>
        </div>
      </div>

      <div v-else-if="props.isEnabled === false" class="disabled-node">
        <el-empty description="节点已禁用">
          <template #image>
            <el-icon size="60" color="#C0C4CC"><Warning /></el-icon>
          </template>
          <template #description>
            <p>该节点已被禁用，无法获取系统信息</p>
            <p>请先启用节点后再查看系统信息</p>
          </template>
        </el-empty>
      </div>

      <div v-else-if="!loading" class="no-data">
        <el-empty description="暂无系统信息" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Timer, Warning } from '@element-plus/icons-vue'
import { mineruNodeApi } from '@/api/mineruNode'
import type { NodeSystemInfo, SystemResources } from '@/types/systemInfo'

const props = defineProps<{
  nodeId: number
  nodeName: string
  isEnabled?: boolean
}>()

const loading = ref(false)
const systemInfo = ref<NodeSystemInfo | null>(null)
const resources = ref<SystemResources | null>(null)
const lastUpdateTime = ref<Date | null>(null)
const autoRefresh = ref(false)
const refreshTimer = ref<number | null>(null)

// 获取系统信息
const getSystemInfo = async () => {
  // 如果节点被禁用，不获取系统信息
  if (props.isEnabled === false) {
    ElMessage.warning('节点已禁用，无法获取系统信息')
    return
  }

  try {
    const response = await mineruNodeApi.getNodeSystemInfo(props.nodeId)
    if (response.success) {
      systemInfo.value = response.system_info
    }
  } catch (error) {
    console.error('获取系统信息失败:', error)
    ElMessage.error('获取系统信息失败')
  }
}

// 获取系统资源
const getSystemResources = async () => {
  // 如果节点被禁用，不获取系统资源
  if (props.isEnabled === false) {
    return
  }

  try {
    const response = await mineruNodeApi.getNodeSystemResources(props.nodeId)
    if (response.success) {
      resources.value = response.resources
      lastUpdateTime.value = new Date()
    }
  } catch (error) {
    console.error('获取系统资源失败:', error)
    ElMessage.error('获取系统资源失败')
  }
}

// 刷新信息
const refreshInfo = async () => {
  loading.value = true
  try {
    await Promise.all([getSystemInfo(), getSystemResources()])
  } finally {
    loading.value = false
  }
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  // 如果节点被禁用，不允许自动刷新
  if (props.isEnabled === false) {
    ElMessage.warning('节点已禁用，无法开启自动刷新')
    return
  }

  autoRefresh.value = !autoRefresh.value

  if (autoRefresh.value) {
    refreshTimer.value = window.setInterval(() => {
      getSystemResources()
    }, 10000) // 每10秒刷新资源信息
    ElMessage.success('已开启自动刷新')
  } else {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
    ElMessage.info('已停止自动刷新')
  }
}

// 格式化频率
const formatFrequency = (freq: number | null) => {
  if (!freq) return 'N/A'
  if (freq >= 1000) {
    return `${(freq / 1000).toFixed(1)}GHz`
  }
  return `${freq.toFixed(0)}MHz`
}

// 格式化时间
const formatTime = (time: Date | null) => {
  if (!time) return 'N/A'
  return time.toLocaleString()
}

// 获取CPU使用率样式类
const getCpuUsageClass = (usage: number) => {
  if (usage >= 80) return 'usage-high'
  if (usage >= 60) return 'usage-medium'
  return 'usage-low'
}

// 获取内存使用率样式类
const getMemoryUsageClass = (usage: number) => {
  if (usage >= 85) return 'usage-high'
  if (usage >= 70) return 'usage-medium'
  return 'usage-low'
}

// 获取GPU使用率
const getGpuUsage = (gpuIndex: number) => {
  const gpuResource = resources.value?.gpu.find(g => g.index === gpuIndex)
  return gpuResource?.utilization_gpu || 0
}

// 获取GPU使用率标签类型
const getGpuUsageTagType = (usage: number) => {
  if (usage >= 80) return 'danger'
  if (usage >= 50) return 'warning'
  return 'success'
}

// 获取单个GPU内存使用率
const getGpuMemoryUsageForGpu = (gpu: any) => {
  if (!gpu || gpu.memory_total_mb <= 0) return '0.0'
  const usage = (gpu.memory_used_mb / gpu.memory_total_mb) * 100
  return usage.toFixed(1)
}

// 获取单个GPU内存使用率标签类型
const getGpuMemoryUsageTagType = (gpu: any) => {
  const usage = parseFloat(getGpuMemoryUsageForGpu(gpu))
  if (usage >= 85) return 'danger'
  if (usage >= 70) return 'warning'
  return 'success'
}

// 获取磁盘使用率标签类型
const getDiskUsageTagType = (usage: number) => {
  if (usage >= 90) return 'danger'
  if (usage >= 80) return 'warning'
  return 'success'
}

onMounted(() => {
  refreshInfo()
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style scoped>
.node-system-info {
  width: 100%;
}

.info-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.system-overview {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 16px;
}

.info-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.info-section h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
}

.info-section h4 .title-text {
  margin-left: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 80px;
}

.info-item .value {
  color: var(--el-text-color-primary);
}

.metric-card {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s;
}

.metric-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.cpu-usage.usage-high .metric-value {
  color: var(--el-color-danger);
}

.cpu-usage.usage-medium .metric-value {
  color: var(--el-color-warning);
}

.cpu-usage.usage-low .metric-value {
  color: var(--el-color-success);
}

.memory-usage.usage-high .metric-value {
  color: var(--el-color-danger);
}

.memory-usage.usage-medium .metric-value {
  color: var(--el-color-warning);
}

.memory-usage.usage-low .metric-value {
  color: var(--el-color-success);
}

.gpu-card {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.gpu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.gpu-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.gpu-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.gpu-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gpu-metric .label {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.gpu-metric .value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.update-time {
  text-align: right;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.section-icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
  opacity: 0.8;
}
</style>
