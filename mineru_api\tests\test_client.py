"""
测试客户端 - 用于测试 MineruAPI 服务
"""
import asyncio
import base64
import json
import time
from pathlib import Path
import httpx
from loguru import logger


class MineruAPIClient:
    """MineruAPI 客户端"""

    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url
        self.api_key = api_key

        # 设置请求头
        headers = {}
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        self.client = httpx.AsyncClient(timeout=30.0, headers=headers)
    
    async def health_check(self):
        """健康检查"""
        response = await self.client.get(f"{self.base_url}/health")
        return response.json()
    
    async def parse_file(self, file_path: Path, **kwargs):
        """解析文件"""
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        # Base64 编码
        file_content_b64 = base64.b64encode(file_content).decode()
        
        request_data = {
            "file_name": file_path.name,
            "file_content": file_content_b64,
            **kwargs
        }
        
        response = await self.client.post(
            f"{self.base_url}/predict",
            json=request_data
        )
        return response.json()
    
    async def parse_file_upload(self, file_path: Path, **kwargs):
        """通过文件上传接口解析"""
        with open(file_path, 'rb') as f:
            files = {"file": (file_path.name, f, "application/pdf")}
            data = kwargs
            
            response = await self.client.post(
                f"{self.base_url}/parse/upload",
                files=files,
                data=data
            )
        return response.json()
    
    async def get_task_status(self, task_id: str):
        """获取任务状态"""
        response = await self.client.get(f"{self.base_url}/status/{task_id}")
        return response.json()
    
    async def list_tasks(self):
        """列出所有任务"""
        response = await self.client.get(f"{self.base_url}/tasks")
        return response.json()
    
    async def wait_for_completion(self, task_id: str, timeout: int = 300):
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = await self.get_task_status(task_id)
            
            if status["status"] == "completed":
                logger.info(f"任务 {task_id} 完成")
                return status
            elif status["status"] == "failed":
                logger.error(f"任务 {task_id} 失败: {status.get('error', '未知错误')}")
                return status
            
            logger.info(f"任务 {task_id} 状态: {status['status']}")
            await asyncio.sleep(5)
        
        logger.error(f"任务 {task_id} 等待超时")
        return None
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def test_basic_functionality():
    """测试基本功能"""
    client = MineruAPIClient()
    
    try:
        # 健康检查
        logger.info("执行健康检查...")
        health = await client.health_check()
        logger.info(f"健康检查结果: {health}")
        
        # 查找测试文件
        demo_dir = Path(__file__).parent.parent / "demo" / "pdfs"
        test_files = list(demo_dir.glob("*.pdf"))
        
        if not test_files:
            logger.error("未找到测试PDF文件")
            return
        
        test_file = test_files[0]
        logger.info(f"使用测试文件: {test_file}")
        
        # 提交解析任务
        logger.info("提交解析任务...")
        result = await client.parse_file_upload(
            test_file,
            lang="ch",
            backend="pipeline",
            method="auto"
        )
        
        task_id = result["task_id"]
        logger.info(f"任务已提交，ID: {task_id}")
        
        # 等待任务完成
        logger.info("等待任务完成...")
        final_status = await client.wait_for_completion(task_id)
        
        if final_status:
            logger.info(f"最终状态: {json.dumps(final_status, indent=2, ensure_ascii=False)}")
        
        # 列出所有任务
        tasks = await client.list_tasks()
        logger.info(f"所有任务: {len(tasks['tasks'])} 个")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
    finally:
        await client.close()


if __name__ == "__main__":
    logger.info("开始测试 MineruAPI 服务...")
    asyncio.run(test_basic_functionality())
