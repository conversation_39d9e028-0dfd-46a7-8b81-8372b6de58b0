#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
调试节点状态和任务分配
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
from almond_parser.schemas import DocumentStatus
from sqlalchemy import select, func


async def debug_node_and_task_status():
    """调试节点状态和任务分配"""
    print("=" * 60)
    print("调试节点状态和任务分配")
    print("=" * 60)
    
    async with get_async_session() as db:
        # 1. 检查所有节点状态
        print("\n1. 节点状态详情:")
        nodes_result = await db.execute(
            select(MinerUNode).order_by(MinerUNode.id)
        )
        nodes = nodes_result.scalars().all()
        
        print(f"{'节点ID':<10} {'名称':<20} {'状态':<10} {'启用':<6} {'当前任务':<10} {'最大任务':<10} {'预留':<6}")
        print("-" * 80)
        
        for node in nodes:
            print(f"{node.id:<10} {node.name:<20} {node.status.value:<10} {node.is_enabled:<6} "
                  f"{node.current_tasks:<10} {node.max_concurrent_tasks:<10} {node.reserved_tasks or 0:<6}")
        
        # 2. 检查系统容量计算
        print("\n2. 系统容量计算:")
        capacity_result = await db.execute(
            select(
                func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                func.sum(MinerUNode.current_tasks).label("current_load"),
                func.sum(MinerUNode.reserved_tasks).label("reserved_load"),
                func.count(MinerUNode.id).label("active_nodes")
            ).where(
                MinerUNode.is_enabled == True,
                MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])
            )
        )
        capacity_row = capacity_result.first()
        total_capacity = capacity_row.total_capacity or 0
        current_load = capacity_row.current_load or 0
        reserved_load = capacity_row.reserved_load or 0
        active_nodes = capacity_row.active_nodes or 0
        available_capacity = total_capacity - current_load - reserved_load
        
        print(f"  活跃节点数: {active_nodes}")
        print(f"  总容量: {total_capacity}")
        print(f"  当前负载: {current_load}")
        print(f"  预留负载: {reserved_load}")
        print(f"  可用容量: {available_capacity}")
        
        # 3. 检查正在处理的任务
        print("\n3. 正在处理的任务:")
        processing_result = await db.execute(
            select(Document).where(
                Document.status.in_([
                    DocumentStatus.PENDING,
                    DocumentStatus.PARSING,
                    DocumentStatus.RETRY_PENDING
                ]),
                Document.task_id.isnot(None)
            ).order_by(Document.updated_at.desc())
        )
        processing_tasks = processing_result.scalars().all()
        
        print(f"{'文档ID':<35} {'状态':<15} {'节点ID':<8} {'任务ID':<35} {'更新时间'}")
        print("-" * 120)
        
        for task in processing_tasks:
            print(f"{task.document_id:<35} {task.status.value:<15} {task.node_id or 'None':<8} "
                  f"{task.task_id or 'None':<35} {task.updated_at}")
        
        # 4. 按节点统计任务
        print("\n4. 按节点统计任务:")
        for node in nodes:
            if node.is_enabled:
                node_tasks_result = await db.execute(
                    select(func.count(Document.id)).where(
                        Document.node_id == node.id,
                        Document.status.in_([
                            DocumentStatus.PENDING,
                            DocumentStatus.PARSING
                        ])
                    )
                )
                actual_task_count = node_tasks_result.scalar() or 0
                
                print(f"  节点 {node.id} ({node.name}):")
                print(f"    数据库中 current_tasks: {node.current_tasks}")
                print(f"    实际正在处理任务数: {actual_task_count}")
                print(f"    差异: {node.current_tasks - actual_task_count}")
                
                if node.current_tasks != actual_task_count:
                    print(f"    ⚠️  发现不一致！")
        
        # 5. 检查等待分配的任务
        print("\n5. 等待分配的任务:")
        pending_result = await db.execute(
            select(func.count(Document.id)).where(
                Document.status.in_([DocumentStatus.UPLOADED, DocumentStatus.RETRY_PENDING])
            )
        )
        pending_count = pending_result.scalar() or 0
        
        pending_no_task_id_result = await db.execute(
            select(func.count(Document.id)).where(
                Document.status.in_([DocumentStatus.UPLOADED, DocumentStatus.RETRY_PENDING]),
                Document.task_id.is_(None)
            )
        )
        pending_no_task_id_count = pending_no_task_id_result.scalar() or 0
        
        print(f"  总等待任务数: {pending_count}")
        print(f"  没有task_id的等待任务数: {pending_no_task_id_count}")
        print(f"  有task_id的等待任务数: {pending_count - pending_no_task_id_count}")
        
        # 6. 显示具体的等待任务
        if pending_count > 0:
            print("\n6. 等待任务详情:")
            waiting_tasks_result = await db.execute(
                select(Document).where(
                    Document.status.in_([DocumentStatus.UPLOADED, DocumentStatus.RETRY_PENDING])
                ).order_by(Document.created_at.asc()).limit(10)
            )
            waiting_tasks = waiting_tasks_result.scalars().all()
            
            print(f"{'文档ID':<35} {'状态':<15} {'任务ID':<35} {'节点ID':<8}")
            print("-" * 100)
            
            for task in waiting_tasks:
                print(f"{task.document_id:<35} {task.status.value:<15} "
                      f"{task.task_id or 'None':<35} {task.node_id or 'None':<8}")


if __name__ == "__main__":
    asyncio.run(debug_node_and_task_status())
