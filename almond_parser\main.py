# -*- encoding: utf-8 -*-
"""
杏仁解析服务主应用
"""
import sys
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import asyncio
import time
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
import uvicorn

from almond_parser.config import settings
from almond_parser.db import init_database, close_database
from almond_parser.db.redis_client import close_redis
from almond_parser.tasks import startup_arq, shutdown_arq
from almond_parser.api import api_router
from almond_parser.utils.logger import setup_logger
from almond_parser.utils.performance import performance_monitor, add_performance_headers


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动杏仁解析服务...")

    try:
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库初始化完成")

        # 初始化DOC数据库连接
        from almond_parser.db.doc_database_manager import init_doc_databases
        await init_doc_databases()
        logger.info("✅ DOC数据库连接初始化完成")

        # 启动 ARQ 任务队列
        await startup_arq()
        logger.info("✅ ARQ 任务队列启动完成")

        logger.info("🎉 杏仁解析服务启动成功")

    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise

    yield

    # 关闭时执行
    logger.info("🛑 关闭杏仁解析服务...")

    try:
        # 关闭 ARQ 任务队列
        await shutdown_arq()
        logger.info("✅ ARQ 任务队列关闭完成")

        # 关闭数据库连接
        await close_database()
        logger.info("✅ 数据库连接关闭完成")

        # 关闭DOC数据库连接
        from almond_parser.db.doc_database_manager import close_doc_databases
        await close_doc_databases()
        logger.info("✅ DOC数据库连接关闭完成")

        # 关闭 Redis 连接
        await close_redis()
        logger.info("✅ Redis 连接关闭完成")

        logger.info("👋 杏仁解析服务关闭完成")

    except Exception as e:
        logger.error(f"❌ 服务关闭异常: {e}")


def create_app() -> FastAPI:
    """创建 FastAPI 应用"""

    # 初始化日志系统
    setup_logger()

    # 创建应用实例
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description="基于 FastAPI + ARQ 的轻量级文档解析和节点管理服务",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # 添加中间件
    setup_middleware(app)

    # 注册路由
    app.include_router(api_router)

    # 注册异常处理器
    setup_exception_handlers(app)

    # 添加根路径
    @app.get("/", tags=["系统"])
    async def root():
        """根路径 - 服务状态"""
        return {
            "service": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "status": "running",
            "docs_url": "/docs"
        }

    @app.get("/health", tags=["系统"])
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "service": settings.APP_NAME,
            "version": settings.APP_VERSION
        }

    @app.get("/performance", tags=["系统"])
    async def performance_stats():
        """性能统计"""
        return {
            "slow_requests": performance_monitor.get_slow_requests(10),
            "request_stats": performance_monitor.get_request_stats()
        }

    return app


def setup_middleware(app: FastAPI):
    """设置中间件"""

    # 请求日志中间件（最外层，最先执行）
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """记录请求日志"""
        start_time = time.time()

        # 记录请求开始（可选，用于调试）
        if settings.DEBUG:
            logger.bind(access=True).debug(
                f"🔄 {request.method} {request.url.path} - 开始处理"
            )

        try:
            response = await call_next(request)
            process_time = time.time() - start_time

            # 获取客户端IP
            client_ip = request.client.host if request.client else "unknown"

            # 构建基础日志信息
            log_msg = (
                f"{request.method} {request.url.path} - "
                f"状态: {response.status_code}, 耗时: {process_time:.3f}s, "
                f"IP: {client_ip}"
            )

            # 根据响应时间和状态码选择日志级别和图标
            if process_time > 1.0:
                # 超过1秒的慢请求用警告级别
                logger.bind(access=True).warning(f"🐌 {log_msg}")
            elif response.status_code >= 500:
                # 服务器错误
                logger.bind(access=True).error(f"💥 {log_msg}")
            elif response.status_code >= 400:
                # 客户端错误（包括404）
                logger.bind(access=True).warning(f"⚠️ {log_msg}")
            else:
                # 正常请求用信息级别
                logger.bind(access=True).info(f"✅ {log_msg}")

            # 添加性能响应头
            await add_performance_headers(request, response, process_time)
            return response

        except Exception as e:
            process_time = time.time() - start_time
            client_ip = request.client.host if request.client else "unknown"

            logger.bind(access=True).error(
                f"❌ {request.method} {request.url.path} - "
                f"异常: {str(e)}, 耗时: {process_time:.3f}s, IP: {client_ip}"
            )
            raise

    # 性能监控中间件
    @app.middleware("http")
    async def performance_middleware(request: Request, call_next):
        """性能监控中间件"""
        return await performance_monitor.monitor_request(request, call_next)

    # CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        # allow_origin_regex=r"^http://172\.\d{1,3}\.\d{1,3}\.\d{1,3}$",
        # allow_origins=["*"] if settings.DEBUG else ["http://localhost:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 信任主机中间件
    # if not settings.DEBUG:
    #     app.add_middleware(
    #         TrustedHostMiddleware,
    #         allowed_hosts=["localhost", "127.0.0.1", settings.HOST]
    #     )


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""

    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc: HTTPException):
        """404 未找到处理"""
        logger.bind(access=True).warning(
            f"🔍 {request.method} {request.url.path} - "
            f"路由未找到, IP: {request.client.host if request.client else 'unknown'}"
        )
        return JSONResponse(
            status_code=404,
            content={
                "success": False,
                "message": f"路由未找到: {request.url.path}",
                "code": 404
            }
        )

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP 异常处理"""
        logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "message": exc.detail,
                "code": exc.status_code
            }
        )

    @app.exception_handler(ValueError)
    async def value_error_handler(request: Request, exc: ValueError):
        """值错误处理"""
        logger.warning(f"参数错误: {str(exc)}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "success": False,
                "message": f"参数错误: {str(exc)}",
                "code": 400
            }
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理"""
        logger.error(f"未处理异常: {type(exc).__name__}: {str(exc)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": "服务器内部错误" if not settings.DEBUG else str(exc),
                "code": 500
            }
        )


# 创建应用实例
app = create_app()


def main():
    """主函数 - 启动服务"""
    logger.info(f"🌟 启动 {settings.APP_NAME} v{settings.APP_VERSION}")
    logger.info(f"📍 服务地址: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"🔧 调试模式: {'开启' if settings.DEBUG else '关闭'}")

    uvicorn.run(
        "almond_parser.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=False,
        log_level="info" if settings.DEBUG else "warning",
        access_log=False,  # 使用自定义日志中间件
    )


if __name__ == "__main__":
    main()
