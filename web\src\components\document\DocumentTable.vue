<template>
  <div class="table-container">
    <div class="table-wrapper">
      <el-table
        v-loading="loading"
        :data="documents"
        border
        style="width: 100%"
        @sort-change="handleSortChange"
        height="100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: 600,
          fontSize: '14px'
        }"
        stripe
        highlight-current-row
      >
        <!-- 文件名 -->
        <el-table-column prop="file_name" label="文件名" :min-width="260" fixed="left" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="filename-cell">
              <el-icon><Document /></el-icon>
              <span>{{ row.file_name }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 文件类型 -->
        <el-table-column prop="file_type" label="文件类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getFileTypeColor(row.file_type)" size="small">
              {{ row.file_type?.toUpperCase() || '-' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 文件大小 -->
        <el-table-column prop="file_size" label="文件大小" width="120" align="right" sortable="custom">
          <template #default="{ row }">
            {{ formatFileSize(row.file_size) }}
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 备注 -->
        <el-table-column prop="remarks" label="备注" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.remarks" class="remarks-text">{{ row.remarks }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>

        <!-- 扩展信息 -->
        <el-table-column prop="extra_info" label="扩展信息" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.extra_info" class="extra-info-cell">
              <el-tooltip
                :content="formatExtraInfoTooltip(row.extra_info)"
                placement="top"
                :show-after="500"
                raw-content
              >
                <div class="extra-info-summary">
                  {{ formatExtraInfoSummary(row.extra_info) }}
                </div>
              </el-tooltip>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column prop="created_at" label="创建时间" width="160" show-overflow-tooltip sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <!-- 更新时间 -->
        <el-table-column prop="updated_at" label="更新时间" width="160" show-overflow-tooltip sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>

        <!-- 任务耗时 -->
        <el-table-column label="耗时" width="100" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatDuration(row.created_at, row.updated_at, row.status) || '-' }}
          </template>
        </el-table-column>

        <!-- 批次ID -->
        <el-table-column prop="batch_id" label="批次ID" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="copy-wrapper">
              <span class="id-text">{{ row.batch_id }}</span>
              <el-button
                type="primary"
                link
                size="small"
                class="copy-btn"
                @click="copyDocumentId(row.batch_id)"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>

        <!-- 文档ID -->
        <el-table-column prop="document_id" label="文档ID" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="copy-wrapper">
              <span class="id-text">{{ row.document_id }}</span>
              <el-button
                type="primary"
                link
                size="small"
                class="copy-btn"
                @click="copyDocumentId(row.document_id)"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>

        <!-- 处理节点 -->
        <el-table-column label="处理节点" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.node_info && row.node_info.name" class="node-info">
              <el-tag size="small" :type="getNodeStatusType(row.node_info.status || 'unknown')">
                {{ row.node_info.name || '未知节点' }}
              </el-tag>
              <div class="node-details">
                ID:{{ row.node_info.id || 'N/A' }} | {{ row.node_info.service_type || '未知' }}
              </div>
            </div>
            <span v-else-if="row.node_id" class="text-orange-500">
              节点ID: {{ row.node_id }}
            </span>
            <span v-else class="text-gray-400">未分配</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right" align="center">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button type="primary" link @click="handleViewDetails(row)">
                <el-icon><View /></el-icon>
                <span class="button-text">详情</span>
              </el-button>
              <el-button type="primary" link @click="handleViewLogs(row)">
                <el-icon><Document /></el-icon>
                <span class="button-text">日志</span>
              </el-button>
              <el-dropdown @command="(command: string) => handleDropdownCommand(command, row)" trigger="click">
                <el-button type="primary" link>
                  <el-icon><More /></el-icon>
                  <span class="button-text">更多</span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="canRetry(row)" command="retry">
                      <el-icon><RefreshRight /></el-icon>重试
                    </el-dropdown-item>
                    <el-dropdown-item command="download">
                      <el-icon><Download /></el-icon>下载原始文件
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @update:current-page="$emit('update:current-page', $event)"
        @update:page-size="$emit('update:page-size', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { DocumentCopy, Document, RefreshRight, Download, More, View } from '@element-plus/icons-vue'
import { Document as DocumentType } from '@/types/document'
import { formatDateTime, formatFileSize, formatDuration } from '@/utils/format'
import { getStatusType, getStatusText } from '@/utils/status'
import { copyToClipboard } from '@/utils/clipboard'

const props = defineProps<{
  loading: boolean
  documents: DocumentType[]
  total: number
  currentPage: number
  pageSize: number
}>()

const emit = defineEmits<{
  (e: 'retry', document: DocumentType): void
  (e: 'view-details', document: DocumentType): void
  (e: 'view-logs', document: DocumentType): void
  (e: 'download', document: DocumentType): void
  (e: 'update:current-page', value: number): void
  (e: 'update:page-size', value: number): void
  (e: 'sort-change', data: { prop: string, order: string | null }): void
}>()

const currentPage = computed(() => props.currentPage)
const pageSize = computed(() => props.pageSize)

const canRetry = (document: DocumentType) => {
  return document.status === 'FAILED' || document.status === 'PARSING'
}

const getNodeStatusType = (status: string) => {
  if (!status) return 'info'

  switch (status.toLowerCase()) {
    case 'online':
      return 'success'
    case 'busy':
      return 'warning'
    case 'offline':
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const handleRetry = (document: DocumentType) => {
  emit('retry', document)
}

const handleViewDetails = (document: DocumentType) => {
  emit('view-details', document)
}

const handleViewLogs = (document: DocumentType) => {
  emit('view-logs', document)
}

const handleDownload = (document: DocumentType) => {
  emit('download', document)
}

const handleDropdownCommand = (command: string, document: DocumentType) => {
  switch (command) {
    case 'retry':
      handleRetry(document)
      break
    case 'download':
      handleDownload(document)
      break
  }
}

const handleSizeChange = (val: number) => {
  emit('update:page-size', val)
}

const handleCurrentChange = (val: number) => {
  emit('update:current-page', val)
}

const handleSortChange = ({ prop, order }: { prop: string, order: string | null }) => {
  emit('sort-change', { prop, order })
}

const copyDocumentId = async (text: string) => {
  await copyToClipboard(text, {
    successMessage: '文档ID已复制到剪贴板'
  })
}

// 获取文件类型颜色
const getFileTypeColor = (fileType: string) => {
  const typeMap: Record<string, string> = {
    pdf: 'danger',
    doc: 'primary',
    docx: 'primary',
    ppt: 'warning',
    pptx: 'warning',
    jpg: 'success',
    jpeg: 'success',
    png: 'success',
    gif: 'success',
    bmp: 'success',
    tiff: 'success',
    txt: 'info',
    md: 'info'
  }
  return typeMap[fileType?.toLowerCase()] || 'info'
}

// 格式化扩展信息摘要显示
const formatExtraInfoSummary = (extraInfo: Record<string, any>): string => {
  if (!extraInfo || typeof extraInfo !== 'object') {
    return '-'
  }

  // 优先显示的字段顺序
  const priorityFields = ['account', 'user_name', 'module', 'department', 'project']
  const parts: string[] = []

  // 按优先级提取字段
  for (const field of priorityFields) {
    if (extraInfo[field] && typeof extraInfo[field] === 'string') {
      parts.push(extraInfo[field])
      if (parts.length >= 3) break // 最多显示3个字段
    }
  }

  // 如果没有找到优先字段，显示前几个可用字段
  if (parts.length === 0) {
    const keys = Object.keys(extraInfo).slice(0, 3)
    for (const key of keys) {
      const value = extraInfo[key]
      if (value !== null && value !== undefined) {
        parts.push(typeof value === 'string' ? value : String(value))
      }
    }
  }

  return parts.length > 0 ? parts.join(' - ') : '-'
}

// 格式化扩展信息详细提示
const formatExtraInfoTooltip = (extraInfo: Record<string, any>): string => {
  if (!extraInfo || typeof extraInfo !== 'object') {
    return '无扩展信息'
  }

  const lines: string[] = []
  for (const [key, value] of Object.entries(extraInfo)) {
    if (value !== null && value !== undefined) {
      const displayValue = typeof value === 'object'
        ? JSON.stringify(value, null, 2)
        : String(value)
      lines.push(`<strong>${key}:</strong> ${displayValue}`)
    }
  }

  return lines.length > 0 ? lines.join('<br/>') : '无扩展信息'
}
</script>

<style scoped>
.table-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* 使用父容器的完整高度 */
  overflow: hidden; /* 禁止整个容器滚动 */
}

.table-wrapper {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-table__fixed-right) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.el-table__fixed-left) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.el-table__header-wrapper) {
  background: #f8fafc;
}

:deep(.el-table th.el-table__cell) {
  background: #f8fafc !important;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-weight: 600;
  font-size: 13px;
  padding: 16px 12px;
}

:deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid #f3f4f6;
  padding: 14px 12px;
  color: #374151;
}

:deep(.el-table__body tr:hover > td) {
  background-color: #f9fafb !important;
}

:deep(.el-table__border-left-patch) {
  background: #f8fafc;
}

:deep(.el-table__border-bottom-patch) {
  background: #f8fafc;
}

.copy-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 0;
}

.id-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  color: #4b5563;
  font-size: 13px;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.copy-btn {
  opacity: 0;
  transition: all 0.2s ease;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
}

.copy-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.copy-wrapper:hover .copy-btn {
  opacity: 1;
}

.filename-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #374151;
}

.filename-cell .el-icon {
  color: #6b7280;
  font-size: 16px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 6px;
}

.operation-buttons .el-button {
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.button-text {
  font-size: 12px;
}

.operation-buttons .el-button--primary.is-link {
  color: #3b82f6;
}

.operation-buttons .el-button--primary.is-link:hover {
  color: #1d4ed8;
  background: #eff6ff;
}

/* 状态标签样式优化 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  padding: 4px 8px;
  border: none;
}

:deep(.el-tag--success) {
  background: #dcfce7;
  color: #166534;
}

:deep(.el-tag--warning) {
  background: #fef3c7;
  color: #92400e;
}

:deep(.el-tag--danger) {
  background: #fee2e2;
  color: #dc2626;
}

:deep(.el-tag--info) {
  background: #e5e7eb;
  color: #374151;
}

.pagination-container {
  flex-shrink: 0;
  height: 60px;
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: #fafafa;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.02);
}

/* 分页组件样式优化 */
.pagination-container :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
}

.pagination-container :deep(.el-pagination .btn-prev),
.pagination-container :deep(.el-pagination .btn-next) {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  color: #606266;
  transition: all 0.2s;
}

.pagination-container :deep(.el-pagination .btn-prev:hover),
.pagination-container :deep(.el-pagination .btn-next:hover) {
  color: #409eff;
  border-color: #409eff;
}

.pagination-container :deep(.el-pagination .el-pager li) {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.2s;
}

.pagination-container :deep(.el-pagination .el-pager li:hover) {
  color: #409eff;
  border-color: #409eff;
}

.pagination-container :deep(.el-pagination .el-pager li.is-active) {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.pagination-container :deep(.el-pagination .el-pagination__total) {
  color: #909399;
  font-weight: 400;
}

.pagination-container :deep(.el-pagination .el-pagination__sizes) {
  margin-right: 16px;
}

.pagination-container :deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
  color: #909399;
}

/* 响应式布局 */
@media (max-width: 1400px) {
  /* 在中等屏幕上缩小扩展信息列 */
  :deep(.el-table__header th:nth-child(7)) {
    width: 200px !important;
  }

  .extra-info-summary {
    font-size: 11px;
    padding: 3px 6px;
  }
}

@media (max-width: 1200px) {
  .id-text {
    font-size: 12px;
    padding: 3px 6px;
  }

  .operation-buttons {
    gap: 4px;
  }

  .operation-buttons .el-button {
    padding: 4px 8px;
    font-size: 12px;
  }

  /* 在较小屏幕上隐藏扩展信息列 */
  :deep(.el-table__header th:nth-child(7)),
  :deep(.el-table__body td:nth-child(7)) {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .table-container {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table th.el-table__cell) {
    padding: 12px 8px;
    font-size: 12px;
  }

  :deep(.el-table td.el-table__cell) {
    padding: 10px 8px;
  }

  .pagination-container {
    padding: 8px;
    height: auto;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .pagination-container :deep(.el-pagination) {
    justify-content: center;
  }

  .pagination-container :deep(.el-pagination .el-pagination__sizes) {
    margin-right: 8px;
  }

  .pagination-container :deep(.el-pagination .el-pagination__jump) {
    margin-left: 8px;
  }

  .copy-wrapper {
    gap: 4px;
  }

  .id-text {
    font-size: 11px;
    padding: 2px 4px;
  }

  .operation-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .operation-buttons .el-button {
    padding: 3px 6px;
    font-size: 11px;
  }

  .operation-buttons .button-text {
    display: none;
  }
}

.node-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.node-details {
  font-size: 11px;
  color: #666;
  line-height: 1.2;
}

.remarks-text {
  color: #374151;
  font-size: 13px;
  line-height: 1.4;
}

.extra-info-cell {
  width: 100%;
}

.extra-info-summary {
  color: #374151;
  font-size: 12px;
  line-height: 1.4;
  padding: 4px 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: help;
  transition: all 0.2s ease;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.extra-info-summary:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}
</style>