# -*- encoding: utf-8 -*-
"""
简化的节点管理器 - 去掉复杂的预留机制
"""
from datetime import datetime
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from loguru import logger

from almond_parser.db.models.mineru_node import MinerUNode, ServiceType, ParseMode, NodeStatus


class SimpleNodeManager:
    """简化的节点管理器"""

    def __init__(self):
        self.task_timeout_minutes = 30  # 任务超时时间：30分钟

    def _map_api_mode_to_db_enum(self, api_mode: str) -> str:
        """
        将API层面的解析模式映射回数据库枚举值

        Args:
            api_mode: API层面的解析模式 (如 'vlm-sglang-client', 'vlm', 'sglang' 等)

        Returns:
            数据库枚举对应的模式值
        """
        if not api_mode:
            return "auto"

        api_mode = api_mode.lower().strip()

        # API模式到数据库枚举的映射
        mode_mapping = {
            "vlm-sglang-client": "sglang",
            "vlm": "sglang",
            "sglang": "sglang",
            "pipeline": "pipeline",
            "auto": "auto"
        }

        mapped_mode = mode_mapping.get(api_mode, "auto")
        logger.debug(f"模式映射: {api_mode} -> {mapped_mode}")

        return mapped_mode

    async def allocate_node(
        self,
        db: AsyncSession,
        service_type: ServiceType,
        parse_mode: Optional[str] = None,
        document_id: Optional[str] = None
    ) -> tuple[Optional[MinerUNode], str]:
        """
        分配节点（简化版本）

        Args:
            db: 数据库会话
            service_type: 服务类型
            parse_mode: 解析模式
            document_id: 文档ID（用于日志）

        Returns:
            tuple[节点或None, 失败原因]
            失败原因: "success", "no_available_nodes", "nodes_at_capacity", "nodes_offline"
        """
        try:
            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行操作
                node, reason = await self._find_available_node_with_reason(
                    db, service_type, parse_mode
                )

                if not node:
                    logger.warning(f"节点分配失败: {reason} (服务类型: {service_type.value}, 文档: {document_id})")
                    return None, reason

                # 直接占用任务槽位
                node.current_tasks += 1
                node.last_task_assigned_at = datetime.now()
                await db.flush()

                logger.info(
                    f"✅ 节点分配成功: {node.name} "
                    f"(当前: {node.current_tasks}/{node.max_concurrent_tasks}) "
                    f"文档: {document_id}"
                )

                return node, "success"
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    node, reason = await self._find_available_node_with_reason(
                        db, service_type, parse_mode
                    )

                    if not node:
                        logger.warning(f"节点分配失败: {reason} (服务类型: {service_type.value}, 文档: {document_id})")
                        return None, reason

                    # 直接占用任务槽位
                    node.current_tasks += 1
                    node.last_task_assigned_at = datetime.now()
                    await db.flush()

                    logger.info(
                        f"✅ 节点分配成功: {node.name} "
                        f"(当前: {node.current_tasks}/{node.max_concurrent_tasks}) "
                        f"文档: {document_id}"
                    )

                    return node, "success"

        except Exception as e:
            logger.error(f"节点分配失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return None, f"exception: {str(e)}"

    async def _find_available_node_with_reason(
        self,
        db: AsyncSession,
        service_type: ServiceType,
        parse_mode: Optional[str] = None
    ) -> tuple[Optional[MinerUNode], str]:
        """查找可用节点并返回详细的失败原因（原子操作，使用行锁）"""
        try:
            # 构建基础查询条件
            conditions = [
                MinerUNode.is_enabled == True,
                or_(
                    MinerUNode.service_type == service_type,
                    MinerUNode.service_type == ServiceType.UNIVERSAL
                )
            ]

            # 添加解析模式条件
            if parse_mode:
                # 将API模式映射为数据库枚举值
                db_mode = self._map_api_mode_to_db_enum(parse_mode)
                try:
                    parse_mode_enum = getattr(ParseMode, db_mode.upper())
                    conditions.append(MinerUNode.parse_mode == parse_mode_enum)
                except AttributeError:
                    logger.warning(f"未知的解析模式: {parse_mode} (映射后: {db_mode})")

            # 🔑 关键修复：使用行锁查询所有符合条件的节点，确保原子性
            # 这样可以防止多个任务同时看到相同的容量状态
            all_nodes_result = await db.execute(
                select(MinerUNode)
                .where(and_(*conditions))
                .with_for_update()  # 🔒 行锁确保并发安全
                .order_by(
                    MinerUNode.current_tasks.asc(),
                    MinerUNode.priority.desc()
                )
            )
            all_nodes = all_nodes_result.scalars().all()

            if not all_nodes:
                return None, "no_available_nodes"

            # 检查节点状态
            online_nodes = [node for node in all_nodes if node.status == NodeStatus.ONLINE]
            if not online_nodes:
                return None, "nodes_offline"

            # 检查是否有容量可用的节点（在锁保护下进行）
            available_nodes = [node for node in online_nodes if node.current_tasks < node.max_concurrent_tasks]
            if not available_nodes:
                # 记录详细的容量信息用于调试
                total_capacity = sum(node.max_concurrent_tasks for node in online_nodes)
                current_load = sum(node.current_tasks for node in online_nodes)
                logger.info(f"📊 容量检查（行锁保护）: 总容量={total_capacity}, 当前负载={current_load}")
                return None, "nodes_at_capacity"

            # 返回第一个可用节点（已在行锁保护下）
            selected_node = available_nodes[0]

            # 记录分配前的状态
            total_capacity = sum(node.max_concurrent_tasks for node in online_nodes)
            current_load = sum(node.current_tasks for node in online_nodes)
            logger.info(f"📊 分配前容量状态（行锁保护）: 总容量={total_capacity}, 当前负载={current_load}")

            return selected_node, "success"

        except Exception as e:
            logger.error(f"查找可用节点失败: {e}")
            return None, f"exception: {str(e)}"

    async def _find_available_node(
        self,
        db: AsyncSession,
        service_type: ServiceType,
        parse_mode: Optional[str] = None
    ) -> Optional[MinerUNode]:
        """查找可用节点（兼容性方法）"""
        try:
            # 构建查询条件
            conditions = [
                MinerUNode.is_enabled == True,
                MinerUNode.status == NodeStatus.ONLINE,
                # 简化：只检查当前任务数
                MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks
            ]

            # 添加服务类型条件
            if service_type == ServiceType.UNIVERSAL:
                # 如果请求通用类型，可以使用任何类型的节点
                pass  # 不添加服务类型限制
            else:
                # 否则查找专用节点或通用节点
                conditions.append(
                    or_(
                        MinerUNode.service_type == service_type,
                        MinerUNode.service_type == ServiceType.UNIVERSAL
                    )
                )

            # 添加解析模式条件
            if parse_mode:
                # 将API模式映射为数据库枚举值
                db_mode = self._map_api_mode_to_db_enum(parse_mode)
                try:
                    parse_mode_enum = getattr(ParseMode, db_mode.upper())
                    conditions.append(MinerUNode.parse_mode == parse_mode_enum)
                except AttributeError:
                    logger.warning(f"未知的解析模式: {parse_mode} (映射后: {db_mode})")

            # 执行查询（使用行锁防止并发）
            result = await db.execute(
                select(MinerUNode)
                .where(and_(*conditions))
                .with_for_update()
                .order_by(
                    MinerUNode.current_tasks.asc(),  # 优先选择任务少的节点
                    MinerUNode.priority.desc()       # 然后按优先级
                )
                .limit(1)
            )

            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"查找可用节点失败: {e}")
            return None

    async def release_node(
        self,
        db: AsyncSession,
        node_id: int,
        task_id: str,
        success: bool = True,
        reason: str = "task_completed"
    ) -> bool:
        """
        释放节点任务槽位
        
        Args:
            db: 数据库会话
            node_id: 节点ID
            task_id: 任务ID
            success: 任务是否成功
            reason: 释放原因
            
        Returns:
            是否成功
        """
        try:
            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行更新
                result = await db.execute(
                    select(MinerUNode)
                    .where(MinerUNode.id == node_id)
                    .with_for_update()
                )
                node = result.scalar_one_or_none()

                if not node:
                    logger.error(f"节点不存在: {node_id}")
                    return False

                # 释放任务槽位
                if node.current_tasks > 0:
                    node.current_tasks -= 1
                    if success:
                        node.success_tasks += 1
                    else:
                        node.failed_tasks += 1

                    await db.flush()

                    logger.info(
                        f"✅ 任务槽位释放: {node.name} - {task_id} "
                        f"(原因: {reason}, 当前: {node.current_tasks})"
                    )
                    return True
                else:
                    # 即使 current_tasks 为 0，也要更新统计信息
                    if success:
                        node.success_tasks += 1
                    else:
                        node.failed_tasks += 1

                    await db.flush()

                    logger.warning(
                        f"⚠️  节点 {node.name} 当前任务数为0，但仍记录任务完成 - {task_id} "
                        f"(原因: {reason}, 成功: {success})"
                    )
                    return True  # 改为返回 True，避免上层认为释放失败
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    result = await db.execute(
                        select(MinerUNode)
                        .where(MinerUNode.id == node_id)
                        .with_for_update()
                    )
                    node = result.scalar_one_or_none()

                    if not node:
                        logger.error(f"节点不存在: {node_id}")
                        return False

                    # 释放任务槽位
                    if node.current_tasks > 0:
                        node.current_tasks -= 1
                        if success:
                            node.success_tasks += 1
                        else:
                            node.failed_tasks += 1
                        
                        await db.flush()
                        
                        logger.info(
                            f"✅ 任务槽位释放: {node.name} - {task_id} "
                            f"(原因: {reason}, 当前: {node.current_tasks})"
                        )
                        return True
                    else:
                        logger.warning(f"节点 {node.name} 当前任务数为0，无需释放")
                        return False
                    
        except Exception as e:
            logger.error(f"释放任务槽位失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return False


# 创建全局实例
simple_node_manager = SimpleNodeManager()
