#!/usr/bin/env python3
"""
SGLang 服务管理 CLI 工具
"""
import asyncio
import argparse
import sys
from pathlib import Path
from typing import Optional
import subprocess
import time

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from mineru_api.services.sglang_manager import sglang_manager
from mineru_api.services.sglang_health_monitor import sglang_health_monitor
from mineru_api.utils.logger import logger


class SglangCLI:
    """SGLang CLI 管理器"""

    def __init__(self, instance_id: Optional[int] = None, port: Optional[int] = None):
        # 支持指定实例ID或端口
        if port is not None:
            # 通过端口反推实例ID
            base_port = 30000  # 默认基础端口
            instance_id = port - base_port

        if instance_id is not None:
            from mineru_api.services.sglang_manager import SglangManager
            from mineru_api.services.sglang_health_monitor import SglangHealthMonitor
            self.manager = SglangManager(instance_id=instance_id)
            self.monitor = SglangHealthMonitor()  # 需要更新以支持实例ID
        else:
            self.manager = sglang_manager
            self.monitor = sglang_health_monitor
    
    async def status(self) -> None:
        """显示 sglang 服务状态"""
        print("🔍 检查 SGLang 服务状态...")
        print("-" * 50)
        
        # 基本信息
        print(f"服务地址: {self.manager.url}")
        print(f"PID文件: {self.manager.pid_file}")
        print(f"日志文件: {self.manager.log_file}")
        
        # 运行状态检查
        is_running = await self.manager.is_running()
        print(f"运行状态: {'🟢 运行中' if is_running else '🔴 未运行'}")
        
        # 端口占用检查
        port_in_use = self.manager.is_port_in_use()
        print(f"端口状态: {'🟡 被占用' if port_in_use else '🟢 可用'}")
        
        # PID 信息
        pid = self.manager.get_pid()
        if pid:
            print(f"进程PID: {pid}")
            # 检查进程是否真的存在
            try:
                import psutil
                if psutil.pid_exists(pid):
                    proc = psutil.Process(pid)
                    print(f"进程状态: {proc.status()}")
                    print(f"CPU使用: {proc.cpu_percent():.1f}%")
                    print(f"内存使用: {proc.memory_info().rss / 1024 / 1024:.1f} MB")
                else:
                    print("⚠️ PID文件存在但进程不存在")
            except ImportError:
                print("提示: 安装 psutil 可显示更多进程信息")
            except Exception as e:
                print(f"获取进程信息失败: {e}")
        else:
            print("进程PID: 无")
        
        # 健康检查
        if is_running:
            print("\n🏥 执行健康检查...")
            health_result = await self.monitor.health_check()
            if health_result["healthy"]:
                print(f"✅ 健康检查通过 (响应时间: {health_result['response_time']:.2f}s)")
            else:
                print(f"❌ 健康检查失败: {health_result.get('error', '未知错误')}")
    
    async def logs(self, follow: bool = False, lines: int = 50) -> None:
        """查看 sglang 服务日志"""
        log_file = self.manager.log_file
        
        if not log_file.exists():
            print(f"❌ 日志文件不存在: {log_file}")
            return
        
        print(f"📋 SGLang 服务日志 ({log_file})")
        print("-" * 50)
        
        if follow:
            # 跟踪日志
            print("🔄 跟踪日志中... (按 Ctrl+C 退出)")
            try:
                if sys.platform == "win32":
                    # Windows 使用 PowerShell Get-Content
                    cmd = ["powershell", "-Command", f"Get-Content -Path '{log_file}' -Tail {lines} -Wait"]
                else:
                    # Linux/Mac 使用 tail
                    cmd = ["tail", "-f", "-n", str(lines), str(log_file)]
                
                subprocess.run(cmd)
            except KeyboardInterrupt:
                print("\n👋 停止跟踪日志")
        else:
            # 显示最后N行
            try:
                if sys.platform == "win32":
                    # Windows 使用 PowerShell
                    cmd = ["powershell", "-Command", f"Get-Content -Path '{log_file}' -Tail {lines}"]
                    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
                    if result.returncode == 0:
                        print(result.stdout)
                    else:
                        print(f"❌ 读取日志失败: {result.stderr}")
                else:
                    # Linux/Mac 使用 tail
                    cmd = ["tail", "-n", str(lines), str(log_file)]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        print(result.stdout)
                    else:
                        print(f"❌ 读取日志失败: {result.stderr}")
            except Exception as e:
                print(f"❌ 读取日志失败: {e}")
                # 备用方案：直接读取文件
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines_list = f.readlines()
                        for line in lines_list[-lines:]:
                            print(line.rstrip())
                except Exception as e2:
                    print(f"❌ 备用读取也失败: {e2}")
    
    async def start(self, force_restart: bool = False) -> None:
        """启动 sglang 服务"""
        print("🚀 启动 SGLang 服务...")
        
        success = await self.manager.start_server(force_restart=force_restart)
        
        if success:
            print("✅ SGLang 服务启动成功")
            await self.status()
        else:
            print("❌ SGLang 服务启动失败")
            print("💡 建议检查日志: mineru-cli sglang logs")
    
    async def stop(self, force_kill: bool = False) -> None:
        """停止 sglang 服务"""
        print("🛑 停止 SGLang 服务...")
        
        success = await self.manager.stop_server(force_kill=force_kill)
        
        if success:
            print("✅ SGLang 服务已停止")
        else:
            print("❌ SGLang 服务停止失败")
            if not force_kill:
                print("💡 尝试强制停止: mineru-cli sglang stop --force")
    
    async def restart(self) -> None:
        """重启 sglang 服务"""
        print("🔄 重启 SGLang 服务...")
        await self.stop()
        await asyncio.sleep(2)
        await self.start()
    
    async def health(self) -> None:
        """执行健康检查"""
        print("🏥 执行 SGLang 健康检查...")
        
        health_result = await self.monitor.health_check()
        
        print("-" * 50)
        print(f"时间戳: {health_result['timestamp']}")
        print(f"健康状态: {'✅ 健康' if health_result['healthy'] else '❌ 不健康'}")
        
        if health_result['response_time']:
            print(f"响应时间: {health_result['response_time']:.2f}s")
        
        if health_result['error']:
            print(f"错误信息: {health_result['error']}")
        
        print(f"端口状态: {health_result['port_status']}")
        
        if health_result['process_info']:
            print("进程信息:")
            for key, value in health_result['process_info'].items():
                print(f"  {key}: {value}")

    def status_all(self) -> None:
        """显示所有实例状态"""
        print("🔍 检查所有 SGLang 实例状态...")
        print("-" * 80)
        print(f"{'端口':<8} {'状态':<8} {'PID':<8} {'日志文件'}")
        print("-" * 80)

        # 检查常见端口范围 30000-30007
        for port in range(30000, 30008):
            instance_id = port - 30000
            try:
                from mineru_api.services.sglang_manager import SglangManager
                manager = SglangManager(instance_id=instance_id)

                # 检查运行状态
                import asyncio
                is_running = asyncio.run(manager.is_running())
                status = "🟢 运行" if is_running else "🔴 停止"

                # 获取PID
                pid = manager.get_pid()
                pid_str = str(pid) if pid else "无"

                # 日志文件状态
                log_exists = "✅" if manager.log_file.exists() else "❌"
                log_info = f"{log_exists} {manager.log_file}"

                print(f"{port:<8} {status:<8} {pid_str:<8} {log_info}")

            except Exception as e:
                print(f"{port:<8} ❌ 错误   无      检查失败: {e}")

        print("-" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SGLang 服务管理工具")
    parser.add_argument("--port", type=int, help="指定SGLang端口")
    parser.add_argument("--instance-id", type=int, help="指定实例ID")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # status 命令
    status_parser = subparsers.add_parser("status", help="显示服务状态")
    status_parser.add_argument("--all", action="store_true", help="显示所有实例状态")
    
    # logs 命令
    logs_parser = subparsers.add_parser("logs", help="查看服务日志")
    logs_parser.add_argument("-f", "--follow", action="store_true", help="跟踪日志")
    logs_parser.add_argument("-n", "--lines", type=int, default=50, help="显示行数")
    
    # start 命令
    start_parser = subparsers.add_parser("start", help="启动服务")
    start_parser.add_argument("--force", action="store_true", help="强制重启")
    
    # stop 命令
    stop_parser = subparsers.add_parser("stop", help="停止服务")
    stop_parser.add_argument("--force", action="store_true", help="强制停止")
    
    # restart 命令
    subparsers.add_parser("restart", help="重启服务")
    
    # health 命令
    subparsers.add_parser("health", help="健康检查")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return

    # 创建CLI实例，支持指定端口或实例ID
    cli = SglangCLI(instance_id=args.instance_id, port=args.port)

    try:
        if args.command == "status":
            if hasattr(args, 'all') and args.all:
                cli.status_all()
            else:
                asyncio.run(cli.status())
        elif args.command == "logs":
            asyncio.run(cli.logs(follow=args.follow, lines=args.lines))
        elif args.command == "start":
            asyncio.run(cli.start(force_restart=args.force))
        elif args.command == "stop":
            asyncio.run(cli.stop(force_kill=args.force))
        elif args.command == "restart":
            asyncio.run(cli.restart())
        elif args.command == "health":
            asyncio.run(cli.health())
    except KeyboardInterrupt:
        print("\n👋 操作已取消")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
