"""add_pdf_pages_field

Revision ID: add_pdf_pages_field
Revises: 329d154bf4e2
Create Date: 2025-08-27 10:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_pdf_pages_field'
down_revision: Union[str, Sequence[str], None] = '329d154bf4e2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('pdf_page_count', sa.Integer(), nullable=True, comment='PDF页数'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('documents', 'pdf_page_count')
    # ### end Alembic commands ###
