import request from '@/utils/request'

export interface ApiKey {
  id: number
  name: string
  key: string
  user_id: number
  is_enabled: boolean
  created_at: string
  last_used_at: string | null
  expires_at: string | null
}

export interface CreateApiKeyRequest {
  name: string
}

// 获取API密钥列表
export function getApiKeys() {
  return request({
    url: '/api-keys/',
    method: 'get'
  })
}

// 创建API密钥
export function createApiKey(data: CreateApiKeyRequest) {
  return request({
    url: '/api-keys/',
    method: 'post',
    data
  })
}

// 删除API密钥
export function deleteApiKey(id: number) {
  return request({
    url: `/api-keys/${id}`,
    method: 'delete'
  })
}

// 获取默认API密钥
export function getDefaultApiKey() {
  return request({
    url: '/api-keys/default',
    method: 'get'
  })
}
