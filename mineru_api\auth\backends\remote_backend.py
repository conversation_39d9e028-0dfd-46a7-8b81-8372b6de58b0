# -*- encoding: utf-8 -*-
"""
远程认证后端 - 通过HTTP API调用远程认证服务
"""

import httpx
from typing import Optional, List
from loguru import logger

from .base import AuthBackend
from ..models import <PERSON>Key, APIKeyStatus, CreateAPIKeyRequest


class RemoteAuthBackend(AuthBackend):
    """远程认证后端"""
    
    def __init__(self, config):
        super().__init__(config)
        self.remote_url = config.remote_url
        self.timeout = config.remote_timeout
        self.api_key = config.remote_api_key
        
        if not self.remote_url:
            raise ValueError("远程认证后端需要配置 remote_url")
    
    def _get_headers(self) -> dict:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        return headers
    
    async def verify_api_key(self, api_key: str, client_ip: Optional[str] = None) -> Optional[APIKey]:
        """验证API key"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.remote_url}/auth/verify",
                    json={
                        "api_key": api_key,
                        "client_ip": client_ip
                    },
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        api_key_data = data.get("api_key")
                        if api_key_data:
                            return APIKey(**api_key_data)
                
                return None
                
        except Exception as e:
            logger.error(f"远程验证API key失败: {e}")
            return None
    
    async def create_api_key(self, request: CreateAPIKeyRequest) -> APIKey:
        """创建API key"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.remote_url}/auth/create",
                    json=request.dict(),
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return APIKey(**data)
                else:
                    error_msg = response.json().get("detail", "创建API key失败")
                    raise Exception(error_msg)
                    
        except Exception as e:
            logger.error(f"远程创建API key失败: {e}")
            raise
    
    async def revoke_api_key(self, api_key: str) -> bool:
        """撤销API key"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.remote_url}/auth/revoke",
                    json={"api_key": api_key},
                    headers=self._get_headers()
                )
                
                return response.status_code == 200
                
        except Exception as e:
            logger.error(f"远程撤销API key失败: {e}")
            return False
    
    async def list_api_keys(self) -> List[APIKey]:
        """列出所有API key"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.remote_url}/auth/keys",
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return [APIKey(**key_data) for key_data in data.get("keys", [])]
                
                return []
                
        except Exception as e:
            logger.error(f"远程列出API keys失败: {e}")
            return []
    
    async def update_usage(self, api_key: str) -> None:
        """更新使用统计"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                await client.post(
                    f"{self.remote_url}/auth/usage",
                    json={"api_key": api_key},
                    headers=self._get_headers()
                )
                
        except Exception as e:
            logger.error(f"远程更新使用统计失败: {e}")
    
    async def get_api_key_info(self, api_key: str) -> Optional[APIKey]:
        """获取API key详细信息"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.remote_url}/auth/info/{api_key}",
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return APIKey(**data)
                
                return None
                
        except Exception as e:
            logger.error(f"远程获取API key信息失败: {e}")
            return None
