#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
ARQ 定时任务诊断工具
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.tasks.arq_app import WorkerSettings
from almond_parser.config import settings
from almond_parser.utils.logger import setup_logger


def check_cron_configuration():
    """检查定时任务配置"""
    logger.info("🔍 检查定时任务配置...")
    
    # 检查 WorkerSettings
    if hasattr(WorkerSettings, 'cron_jobs'):
        cron_jobs = WorkerSettings.cron_jobs
        logger.info(f"✅ 找到 {len(cron_jobs)} 个定时任务")
        
        for i, cron_job in enumerate(cron_jobs):
            logger.info(f"  任务 {i+1}:")
            logger.info(f"    函数: {cron_job.coroutine.__name__}")
            
            # 检查时间配置
            if hasattr(cron_job, 'second'):
                logger.info(f"    间隔: 每 {cron_job.second} 秒")
            elif hasattr(cron_job, 'minute'):
                logger.info(f"    间隔: 每 {cron_job.minute} 分钟")
            elif hasattr(cron_job, 'hour'):
                logger.info(f"    间隔: 每 {cron_job.hour} 小时")
            else:
                logger.warning(f"    ⚠️  未找到时间间隔配置")
            
            logger.info(f"    启动时运行: {getattr(cron_job, 'run_at_startup', False)}")
        
        return True
    else:
        logger.error("❌ WorkerSettings 中没有 cron_jobs 配置")
        return False


def check_interval_setting():
    """检查间隔设置"""
    logger.info("🔍 检查间隔设置...")
    
    interval = settings.NODE_HEALTH_CHECK_INTERVAL
    logger.info(f"  NODE_HEALTH_CHECK_INTERVAL: {interval} 秒")
    
    if interval <= 0:
        logger.error("❌ 间隔设置无效，必须大于 0")
        return False
    elif interval < 10:
        logger.warning("⚠️  间隔设置过短，可能导致频繁执行")
    
    return True


def check_arq_cron_syntax():
    """检查 ARQ cron 语法"""
    logger.info("🔍 检查 ARQ cron 语法...")
    
    try:
        from arq import cron
        from almond_parser.tasks import node_tasks
        
        # 尝试创建 cron 任务
        test_cron = cron(
            coroutine=node_tasks.health_check_all_nodes,
            second=60,
            run_at_startup=True,
        )
        
        logger.info("✅ ARQ cron 语法正确")
        logger.info(f"  测试任务: {test_cron.coroutine.__name__}")
        return True
        
    except Exception as e:
        logger.error(f"❌ ARQ cron 语法错误: {e}")
        return False


async def test_task_function():
    """测试任务函数"""
    logger.info("🧪 测试任务函数...")
    
    try:
        from almond_parser.tasks.node_tasks import health_check_all_nodes
        from almond_parser.db import init_database
        
        # 初始化数据库
        await init_database()
        
        # 模拟 ARQ 上下文
        ctx = {"job_id": "test_cron", "job_try": 1}
        
        # 执行任务
        result = await health_check_all_nodes(ctx)
        
        logger.info(f"✅ 任务函数执行成功: {result}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 任务函数执行失败: {e}")
        return False


def check_worker_startup_logs():
    """检查 Worker 启动日志建议"""
    logger.info("📋 Worker 启动日志检查建议:")
    logger.info("  1. 启动 Worker 时应该看到:")
    logger.info("     - 'Starting worker for...'")
    logger.info("     - 'Registering cron job...'")
    logger.info("     - 'Starting cron job...'")
    logger.info("  2. 如果没有看到 cron 相关日志，说明定时任务没有注册")
    logger.info("  3. 检查 Worker 是否正确加载了 WorkerSettings")


def show_debug_worker_command():
    """显示调试 Worker 命令"""
    logger.info("🔧 调试建议:")
    logger.info("  1. 启动 Worker 时添加详细日志:")
    logger.info("     export ARQ_LOG_LEVEL=DEBUG")
    logger.info("     python almond_parser/worker.py")
    logger.info("  2. 或者使用 arq 命令直接启动:")
    logger.info("     arq almond_parser.tasks.arq_app.WorkerSettings")
    logger.info("  3. 检查 Redis 连接:")
    logger.info("     redis-cli ping")


async def main():
    """主函数"""
    setup_logger()
    
    logger.info("🔧 ARQ 定时任务诊断工具")
    logger.info("=" * 50)
    
    checks = []
    
    # 检查定时任务配置
    config_ok = check_cron_configuration()
    checks.append(("定时任务配置", config_ok))
    
    # 检查间隔设置
    interval_ok = check_interval_setting()
    checks.append(("间隔设置", interval_ok))
    
    # 检查 ARQ cron 语法
    syntax_ok = check_arq_cron_syntax()
    checks.append(("ARQ cron 语法", syntax_ok))
    
    # 测试任务函数
    task_ok = await test_task_function()
    checks.append(("任务函数", task_ok))
    
    # 显示检查结果
    logger.info("\n📊 诊断结果:")
    all_ok = True
    for name, status in checks:
        status_text = "✅ 正常" if status else "❌ 异常"
        logger.info(f"  {name}: {status_text}")
        if not status:
            all_ok = False
    
    # 显示建议
    check_worker_startup_logs()
    show_debug_worker_command()
    
    if all_ok:
        logger.info("\n🎉 配置检查通过！")
        logger.info("如果定时任务仍未执行，请检查 Worker 启动日志")
    else:
        logger.info("\n⚠️  发现配置问题，请根据上述信息修复")


if __name__ == "__main__":
    asyncio.run(main())
