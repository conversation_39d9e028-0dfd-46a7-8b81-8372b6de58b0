"""
运维工具 - 提供便捷的运维操作
"""
import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
import httpx
import argparse
from typing import Optional


class OpsClient:
    """运维客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def get_health(self):
        """健康检查"""
        response = await self.client.get(f"{self.base_url}/health")
        return response.json()
    
    async def get_current_tasks(self):
        """获取当前任务"""
        response = await self.client.get(f"{self.base_url}/tasks")
        return response.json()
    
    async def get_task_history(self, status: Optional[str] = None, days: int = 7):
        """获取任务历史"""
        params = {"days": days}
        if status:
            params["status"] = status
        
        response = await self.client.get(f"{self.base_url}/history", params=params)
        return response.json()
    
    async def get_statistics(self, days: int = 7):
        """获取统计信息"""
        response = await self.client.get(f"{self.base_url}/statistics", params={"days": days})
        return response.json()
    
    async def cleanup_history(self, days: int = 30):
        """清理历史记录"""
        response = await self.client.post(f"{self.base_url}/admin/cleanup", params={"days": days})
        return response.json()
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def health_check():
    """健康检查"""
    client = OpsClient()
    try:
        health = await client.get_health()
        print("🟢 服务健康状态:")
        print(json.dumps(health, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"🔴 服务不可用: {e}")
    finally:
        await client.close()


async def show_current_status():
    """显示当前状态"""
    client = OpsClient()
    try:
        # 当前任务
        tasks = await client.get_current_tasks()
        print("📋 当前任务状态:")
        
        if not tasks["tasks"]:
            print("  无正在进行的任务")
        else:
            for task in tasks["tasks"]:
                status_emoji = {
                    "pending": "⏳",
                    "processing": "🔄",
                    "completed": "✅",
                    "failed": "❌"
                }.get(task["status"], "❓")
                
                print(f"  {status_emoji} {task['task_id'][:8]} - {task['status']} - {task['message']}")
        
        # 统计信息
        stats = await client.get_statistics(days=1)
        if stats:
            summary = stats.get("summary", {})
            print(f"\n📊 今日统计:")
            print(f"  总任务数: {summary.get('total_tasks', 0)}")
            print(f"  成功任务: {summary.get('completed_tasks', 0)}")
            print(f"  失败任务: {summary.get('failed_tasks', 0)}")
            
            avg_duration = summary.get('avg_duration')
            if avg_duration:
                print(f"  平均处理时间: {avg_duration:.2f}秒")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")
    finally:
        await client.close()


async def show_error_summary(days: int = 7):
    """显示错误摘要"""
    client = OpsClient()
    try:
        history = await client.get_task_history(status="failed", days=days)
        
        print(f"🚨 最近 {days} 天的错误摘要:")
        
        if not history["tasks"]:
            print("  无失败任务")
            return
        
        # 按错误类型分组
        error_types = {}
        for task in history["tasks"]:
            error_msg = task.get("error_message", "未知错误")
            # 简化错误消息
            error_key = error_msg.split(":")[0] if ":" in error_msg else error_msg
            error_types[error_key] = error_types.get(error_key, 0) + 1
        
        print(f"  总失败任务: {len(history['tasks'])}")
        print("  错误类型分布:")
        for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
            print(f"    {error_type}: {count} 次")
        
        # 显示最近的几个错误
        print("\n  最近的错误:")
        for task in history["tasks"][:5]:
            created_at = datetime.fromisoformat(task["created_at"]).strftime("%m-%d %H:%M")
            file_name = task.get("file_name", "未知文件")
            error_msg = task.get("error_message", "未知错误")[:50]
            print(f"    {created_at} - {file_name} - {error_msg}...")
        
    except Exception as e:
        print(f"❌ 获取错误摘要失败: {e}")
    finally:
        await client.close()


async def show_performance_report(days: int = 7):
    """显示性能报告"""
    client = OpsClient()
    try:
        stats = await client.get_statistics(days=days)
        
        print(f"⚡ 最近 {days} 天的性能报告:")
        
        summary = stats.get("summary", {})
        total_tasks = summary.get("total_tasks", 0)
        
        if total_tasks == 0:
            print("  无任务数据")
            return
        
        completed_tasks = summary.get("completed_tasks", 0)
        failed_tasks = summary.get("failed_tasks", 0)
        success_rate = completed_tasks / total_tasks * 100 if total_tasks > 0 else 0
        
        print(f"  总任务数: {total_tasks}")
        print(f"  成功率: {success_rate:.1f}%")
        
        avg_duration = summary.get("avg_duration")
        max_duration = summary.get("max_duration")
        
        if avg_duration:
            print(f"  平均处理时间: {avg_duration:.2f}秒")
        if max_duration:
            print(f"  最长处理时间: {max_duration:.2f}秒")
        
        # 每日趋势
        daily_stats = stats.get("daily_breakdown", [])
        if daily_stats:
            print("\n  每日趋势:")
            for day_stat in daily_stats[:7]:  # 最近7天
                date = day_stat["date"]
                count = day_stat["count"]
                completed = day_stat["completed"]
                failed = day_stat["failed"]
                day_success_rate = completed / count * 100 if count > 0 else 0
                print(f"    {date}: {count} 任务, 成功率 {day_success_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ 获取性能报告失败: {e}")
    finally:
        await client.close()


async def cleanup_old_data(days: int = 30):
    """清理旧数据"""
    client = OpsClient()
    try:
        print(f"🧹 清理超过 {days} 天的历史数据...")
        result = await client.cleanup_history(days)
        print(f"✅ {result['message']}")
    except Exception as e:
        print(f"❌ 清理失败: {e}")
    finally:
        await client.close()


def main():
    """命令行工具"""
    parser = argparse.ArgumentParser(description="MineruAPI 运维工具")
    parser.add_argument("--url", type=str, default="http://localhost:8000",
                       help="API服务地址")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 健康检查
    subparsers.add_parser("health", help="健康检查")
    
    # 当前状态
    subparsers.add_parser("status", help="显示当前状态")
    
    # 错误摘要
    error_parser = subparsers.add_parser("errors", help="显示错误摘要")
    error_parser.add_argument("--days", type=int, default=7, help="查询天数")
    
    # 性能报告
    perf_parser = subparsers.add_parser("performance", help="显示性能报告")
    perf_parser.add_argument("--days", type=int, default=7, help="查询天数")
    
    # 清理数据
    cleanup_parser = subparsers.add_parser("cleanup", help="清理历史数据")
    cleanup_parser.add_argument("--days", type=int, default=30, help="保留天数")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 设置全局URL
    global_url = args.url
    
    if args.command == "health":
        asyncio.run(health_check())
    elif args.command == "status":
        asyncio.run(show_current_status())
    elif args.command == "errors":
        asyncio.run(show_error_summary(args.days))
    elif args.command == "performance":
        asyncio.run(show_performance_report(args.days))
    elif args.command == "cleanup":
        asyncio.run(cleanup_old_data(args.days))


if __name__ == "__main__":
    main()
