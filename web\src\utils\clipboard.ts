/**
 * 通用剪贴板复制工具
 * 提供跨平台兼容的复制功能
 */

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param options 配置选项
 * @returns Promise<boolean> 复制是否成功
 */
export async function copyToClipboard(
  text: string,
  options: {
    showMessage?: boolean
    successMessage?: string
    errorMessage?: string
  } = {}
): Promise<boolean> {
  const {
    showMessage = true,
    successMessage = '内容已复制到剪贴板',
    errorMessage = '复制失败'
  } = options

  try {
    // 优先尝试现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      try {
        await navigator.clipboard.writeText(text)
        if (showMessage) {
          // 动态导入 ElMessage 避免循环依赖
          const { ElMessage } = await import('element-plus')
          ElMessage.success(successMessage)
        }
        return true
      } catch (clipboardError) {
        console.warn('现代 Clipboard API 失败，回退到传统方法:', clipboardError)
        // 回退到传统方法
        return fallbackCopy(text, { showMessage, successMessage, errorMessage })
      }
    } else {
      // 直接使用传统方法
      return fallbackCopy(text, { showMessage, successMessage, errorMessage })
    }
  } catch (error) {
    console.error('复制失败:', error)
    if (showMessage) {
      const { ElMessage } = await import('element-plus')
      ElMessage.error(errorMessage)
    }
    return false
  }
}

/**
 * 传统复制方法（使用 document.execCommand）
 * @param text 要复制的文本
 * @param options 配置选项
 * @returns boolean 复制是否成功
 */
function fallbackCopy(
  text: string,
  options: {
    showMessage?: boolean
    successMessage?: string
    errorMessage?: string
  } = {}
): boolean {
  const {
    showMessage = true,
    successMessage = '内容已复制到剪贴板',
    errorMessage = '复制失败'
  } = options

  try {
    // 创建临时 textarea 元素
    const textArea = document.createElement('textarea')
    textArea.value = text
    
    // 设置样式使其不可见
    textArea.style.position = 'fixed'
    textArea.style.left = '-9999px'
    textArea.style.top = '0'
    textArea.style.opacity = '0'
    textArea.style.pointerEvents = 'none'
    textArea.style.zIndex = '-1'
    
    // 添加到 DOM
    document.body.appendChild(textArea)
    
    // 选择文本
    textArea.select()
    textArea.setSelectionRange(0, textArea.value.length)
    
    // 执行复制命令
    const successful = document.execCommand('copy')
    
    // 清理 DOM
    document.body.removeChild(textArea)
    
    if (successful && showMessage) {
      // 异步导入避免循环依赖
      import('element-plus').then(({ ElMessage }) => {
        ElMessage.success(successMessage)
      })
    } else if (!successful && showMessage) {
      import('element-plus').then(({ ElMessage }) => {
        ElMessage.error(errorMessage)
      })
    }
    
    return successful
  } catch (error) {
    console.error('传统复制方法失败:', error)
    if (showMessage) {
      import('element-plus').then(({ ElMessage }) => {
        ElMessage.error(errorMessage)
      })
    }
    return false
  }
}

/**
 * 检查剪贴板 API 是否可用
 * @returns boolean 是否支持剪贴板操作
 */
export function isClipboardSupported(): boolean {
  return !!(navigator.clipboard || document.execCommand)
}

/**
 * 检查是否支持现代 Clipboard API
 * @returns boolean 是否支持现代 API
 */
export function isModernClipboardSupported(): boolean {
  return !!(navigator.clipboard && window.isSecureContext)
}

/**
 * 获取剪贴板内容（仅现代 API 支持）
 * @returns Promise<string | null> 剪贴板内容，失败时返回 null
 */
export async function readFromClipboard(): Promise<string | null> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      return await navigator.clipboard.readText()
    }
    console.warn('读取剪贴板需要现代 Clipboard API 和安全上下文')
    return null
  } catch (error) {
    console.error('读取剪贴板失败:', error)
    return null
  }
}
