# 节点系统信息功能使用指南

## 🎯 功能概述

杏仁解析中心现已集成节点系统信息监控功能，可以实时查看每个MinerU节点的硬件信息和资源使用情况：

- 💻 **系统基本信息**：主机名、操作系统、架构等
- ⚡ **CPU信息**：核心数、频率、实时使用率
- 💾 **内存信息**：总量、使用量、使用率
- 🎮 **GPU信息**：显卡型号、显存、使用率、温度
- 💿 **磁盘信息**：容量、使用情况、文件系统
- 🌐 **网络信息**：网络接口、IP地址

## 🖥️ 界面设计

### 1. 节点列表中的系统资源摘要

在节点管理页面的表格中新增了"系统资源"列，显示：

```
系统资源
┌─────────────┐
│ ⚡ 15.2%    │ ← CPU使用率
│ 💾 68.5%    │ ← 内存使用率  
│ 🎮 2GPU     │ ← GPU数量
│ ✅          │ ← 状态指示器
└─────────────┘
```

**特点**：
- 🎨 **颜色编码**：绿色(正常) / 橙色(中等) / 红色(高负载)
- 🖱️ **悬浮详情**：鼠标悬停显示详细信息
- 🔄 **自动刷新**：每30秒自动更新数据
- 📱 **响应式设计**：适配不同屏幕尺寸

### 2. 悬浮详情面板

鼠标悬停在系统资源摘要上时，显示详细信息：

```
💻 系统信息
主机: gpu-server-01
系统: Linux

⚡ CPU  
核心: 16物理 / 32逻辑
使用率: 15.2%

💾 内存
总量: 64.0GB
使用: 43.8GB (68.5%)

🎮 GPU
GPU0: NVIDIA GeForce RTX 4090...
  25% | 8.2GB/24.0GB
GPU1: NVIDIA GeForce RTX 4090...
  0% | 0.1GB/24.0GB

更新: 14:30:25
```

### 3. 详细系统信息对话框

点击节点操作菜单中的"系统信息"，弹出完整的系统信息对话框：

**功能特点**：
- 📊 **完整信息展示**：所有硬件信息和实时数据
- 🔄 **手动/自动刷新**：支持手动刷新和自动刷新模式
- 📈 **实时监控**：CPU、内存、GPU使用率实时更新
- 📋 **详细表格**：磁盘信息以表格形式展示

## 🔧 技术实现

### MinerU-API新增接口

为MinerU-API添加了系统信息接口：

```python
# 获取系统基本信息
GET /system/info

# 获取系统资源使用情况  
GET /system/resources
```

**接口响应示例**：

```json
{
  "system": {
    "platform": "Linux",
    "hostname": "gpu-server-01",
    "architecture": "x86_64"
  },
  "cpu": {
    "physical_cores": 16,
    "total_cores": 32,
    "cpu_usage": 15.2
  },
  "memory": {
    "total": 64.0,
    "used": 43.8,
    "percentage": 68.5
  },
  "gpu": [
    {
      "index": 0,
      "name": "NVIDIA GeForce RTX 4090",
      "memory_total_mb": 24576,
      "memory_used_mb": 8192,
      "utilization_gpu": 25,
      "temperature": 65
    }
  ]
}
```

### 杏仁解析中心API

为杏仁解析中心添加了代理接口：

```typescript
// 获取节点系统信息
GET /api/mineru-nodes/{node_id}/system-info

// 获取节点系统资源
GET /api/mineru-nodes/{node_id}/system-resources
```

### 前端组件架构

```
MinerUNodes.vue (节点管理页面)
├── NodeSystemSummary.vue (系统资源摘要)
│   ├── 显示CPU/内存/GPU使用率
│   ├── 悬浮详情面板
│   └── 自动刷新机制
└── NodeSystemInfo.vue (详细系统信息)
    ├── 完整系统信息展示
    ├── 实时资源监控
    └── 手动/自动刷新
```

## 📊 监控指标

### CPU监控
- **物理核心数** / **逻辑核心数**
- **当前频率** / **最大频率**
- **实时使用率** (总体 + 每核心)
- **负载平均值** (Linux)

### 内存监控
- **总内存容量**
- **已使用内存**
- **可用内存**
- **使用率百分比**

### GPU监控
- **GPU型号和索引**
- **显存总量/使用量**
- **GPU使用率**
- **显存使用率**
- **温度监控**
- **功耗监控**

### 磁盘监控
- **设备名称**
- **挂载点**
- **文件系统类型**
- **总容量/已使用/可用空间**
- **使用率百分比**

## 🎨 UI设计特点

### 1. 颜色编码系统

| 资源类型 | 正常 | 中等负载 | 高负载 |
|----------|------|----------|--------|
| CPU | < 60% (绿色) | 60-80% (橙色) | > 80% (红色) |
| 内存 | < 70% (绿色) | 70-85% (橙色) | > 85% (红色) |
| GPU | < 50% (绿色) | 50-80% (橙色) | > 80% (红色) |
| 磁盘 | < 80% (绿色) | 80-90% (橙色) | > 90% (红色) |

### 2. 响应式布局

- **大屏幕**：完整显示所有信息
- **中等屏幕**：紧凑布局，保持可读性
- **小屏幕**：垂直堆叠，优化触摸操作

### 3. 交互设计

- **悬浮提示**：鼠标悬停显示详情
- **点击展开**：点击查看完整信息
- **自动刷新**：可开启/关闭自动刷新
- **加载状态**：清晰的加载和错误状态

## 🔄 刷新机制

### 自动刷新策略

1. **摘要组件**：每30秒刷新一次资源使用情况
2. **详情对话框**：可选择10秒间隔自动刷新
3. **智能刷新**：加载中时跳过刷新，避免重复请求

### 手动刷新

- 摘要组件：点击即可刷新
- 详情对话框：专门的刷新按钮
- 错误重试：失败时提供重试按钮

## 📱 移动端适配

- **紧凑显示**：在小屏幕上优化布局
- **触摸友好**：增大点击区域
- **滑动查看**：支持横向滑动查看完整表格

## 🚀 使用建议

### 日常监控

1. **关注颜色变化**：红色指标需要重点关注
2. **查看趋势**：通过自动刷新观察资源使用趋势
3. **GPU监控**：重点关注GPU使用率和温度

### 性能优化

1. **CPU高负载**：检查是否有异常进程
2. **内存不足**：考虑增加内存或优化程序
3. **GPU过热**：检查散热和环境温度
4. **磁盘空间**：及时清理不必要的文件

### 故障排查

1. **系统信息异常**：检查MinerU-API服务状态
2. **数据不更新**：检查网络连接和API接口
3. **GPU信息缺失**：确认nvidia-smi命令可用

## 🔮 未来规划

- [ ] **历史数据图表**：显示资源使用趋势
- [ ] **告警阈值设置**：自定义告警条件
- [ ] **性能对比**：多节点性能对比
- [ ] **导出功能**：导出系统信息报告
- [ ] **移动端优化**：专门的移动端界面

---

这个系统信息功能为节点管理提供了全面的硬件监控能力，帮助管理员更好地了解和管理服务器资源！
