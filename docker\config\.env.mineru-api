# MinerU API 解析引擎配置文件

# 服务配置
MINERU_API_PORT=8001
DEBUG=false

# GPU 配置
CUDA_VISIBLE_DEVICES=0
NVIDIA_VISIBLE_DEVICES=all
GPU_COUNT=1

# VLM 配置
VLM_MODEL_PATH=/app/models
VLM_SERVER_URL=http://127.0.0.1:30000

# 解析模式配置
PIPELINE_MODE=auto

# LibreOffice 配置
ENABLE_DOC_CONVERSION=true

# 文件处理配置
MAX_FILE_SIZE=100MB

# 日志配置
LOG_LEVEL=INFO

# 性能配置
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=300

# 认证配置
API_KEY_REQUIRED=false
API_KEY=

# 集群配置（用于多节点部署）
MINERU_API_PORT_1=8001
MINERU_API_PORT_2=8002
MINERU_API_PORT_3=8003
VLM_SERVER_URL_1=http://127.0.0.1:30000
VLM_SERVER_URL_2=http://127.0.0.1:30001
