# -*- encoding: utf-8 -*-
"""
MinerU 节点管理 API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status, Body
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from almond_parser.db import get_db
from almond_parser.db.models import NodeStatus, ParseMode, ServiceType, SglangStatus
from almond_parser.schemas.mineru_node import (
    MinerUNodeCreate, MinerUNodeUpdate, MinerUNodeResponse, MinerUNodeStats,
    NodeHealthStatus
)
from almond_parser.services.sglang_monitor_service import SglangMonitorService
from almond_parser.services.sglang_restart_service import SglangRestartService
from almond_parser.schemas.base import BaseResponse
from almond_parser.services.mineru_node_service import MinerUNodeService
from almond_parser.utils.auth import get_current_user
from almond_parser.tasks.arq_app import arq_manager

router = APIRouter(prefix="/mineru-nodes", tags=["MinerU 节点管理"])


@router.post("/", response_model=MinerUNodeResponse)
async def create_node(
    node_data: MinerUNodeCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建新的 MinerU 节点"""
    try:
        service = MinerUNodeService(db)
        node = await service.create_node(node_data)
        logger.info(f"用户 {current_user['username']} 创建节点: {node.name}")
        return node
    except Exception as e:
        logger.error(f"创建节点失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建节点失败: {str(e)}"
        )


@router.get("/", response_model=List[MinerUNodeResponse])
async def get_nodes(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status_filter: Optional[NodeStatus] = Query(None, alias="status", description="节点状态过滤"),
    parse_mode: Optional[ParseMode] = Query(None, description="解析模式过滤"),
    service_type: Optional[ServiceType] = Query(None, description="服务类型过滤"),
    is_enabled: Optional[bool] = Query(None, description="是否启用过滤"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取节点列表"""
    try:
        service = MinerUNodeService(db)
        nodes = await service.get_nodes(
            skip=skip,
            limit=limit,
            status=status_filter,
            parse_mode=parse_mode,
            service_type=service_type,
            is_enabled=is_enabled
        )
        return nodes
    except Exception as e:
        logger.error(f"获取节点列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取节点列表失败"
        )


@router.get("/stats", response_model=MinerUNodeStats)
async def get_node_stats(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取节点统计信息"""
    try:
        service = MinerUNodeService(db)
        stats = await service.get_node_stats()
        return stats
    except Exception as e:
        logger.error(f"获取节点统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取节点统计失败"
        )


@router.get("/{node_id}", response_model=MinerUNodeResponse)
async def get_node(
    node_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取单个节点信息"""
    try:
        service = MinerUNodeService(db)
        node = await service.get_node(node_id)
        if not node:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="节点不存在"
            )
        return node
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取节点信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取节点信息失败"
        )


@router.put("/{node_id}", response_model=MinerUNodeResponse)
async def update_node(
    node_id: int,
    node_data: MinerUNodeUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """更新节点信息"""
    try:
        service = MinerUNodeService(db)
        node = await service.update_node(node_id, node_data)
        if not node:
            raise HTTPException(status_code=404, detail="节点不存在")

        logger.info(f"用户 {current_user['username']} 更新节点: {node.name}")
        return node
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新节点失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{node_id}")
async def delete_node(
    node_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除节点"""
    try:
        service = MinerUNodeService(db)
        success = await service.delete_node(node_id)
        if not success:
            raise HTTPException(status_code=404, detail="节点不存在")

        logger.info(f"用户 {current_user['username']} 删除节点: {node_id}")
        return {"message": "节点删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除节点失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/health-check", response_model=BaseResponse)
async def trigger_health_check_all(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """手动触发所有节点健康检查"""
    try:
        # 提交健康检查任务
        job_id = await arq_manager.enqueue_task("health_check_all_nodes")

        logger.info(f"用户 {current_user['username']} 触发所有节点健康检查, 任务ID: {job_id}")
        return BaseResponse(message=f"健康检查任务已提交，任务ID: {job_id}")

    except Exception as e:
        logger.error(f"触发健康检查失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="触发健康检查失败"
        )


@router.get("/{node_id}/health", response_model=NodeHealthStatus)
async def get_node_health(
    node_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取节点健康状态"""
    try:
        service = MinerUNodeService(db)
        health_status = await service.get_node_health_status(node_id)

        if not health_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="节点不存在或无健康检查记录"
            )

        return health_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取节点健康状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取节点健康状态失败"
        )


@router.post("/{node_id}/health-check", response_model=BaseResponse)
async def trigger_single_health_check(
    node_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """手动触发单个节点健康检查"""
    try:
        # 检查节点是否存在
        service = MinerUNodeService(db)
        node = await service.get_node(node_id)

        if not node:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="节点不存在"
            )

        # 提交健康检查任务
        job_id = await arq_manager.enqueue_task("health_check_node", node_id=node_id)

        logger.info(f"用户 {current_user['username']} 触发节点 {node_id} 健康检查, 任务ID: {job_id}")
        return BaseResponse(message=f"节点健康检查任务已提交，任务ID: {job_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"触发单个节点健康检查失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="触发健康检查失败"
        )


@router.get("/service-type-stats", summary="获取服务类型统计")
async def get_service_type_stats(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取各服务类型的节点统计信息"""
    try:
        from almond_parser.utils.node_selector import NodeSelector

        selector = NodeSelector(db)
        stats = await selector.get_service_type_stats()

        return {
            "message": "获取服务类型统计成功",
            "data": stats
        }

    except Exception as e:
        logger.error(f"获取服务类型统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


@router.put("/{node_id}/toggle-status", summary="切换节点启用状态")
async def toggle_node_status(
        node_id: int,
        status_update: dict = Body(...),
        current_user: dict = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
):
    """切换节点的启用/禁用状态"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        node_service = MinerUNodeService(db)
        node = await node_service.toggle_node_status(node_id, status_update.get("is_enabled", False))
        return node

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"切换节点状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"切换状态失败: {str(e)}")


# SGLang 服务监控相关接口

@router.get("/{node_id}/sglang/status", summary="获取节点SGLang服务状态")
async def get_node_sglang_status(
    node_id: int,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定节点的SGLang服务状态"""
    try:
        sglang_service = SglangMonitorService(db)
        result = await sglang_service.monitor_node_sglang(node_id)

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["error"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"获取节点SGLang状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取SGLang状态失败: {str(e)}")


@router.post("/{node_id}/sglang/restart", summary="重启节点SGLang服务")
async def restart_node_sglang(
    node_id: int,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """重启指定节点的SGLang服务"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        # 获取节点信息
        node_service = MinerUNodeService(db)
        node = await node_service.get_node(node_id)

        if not node:
            raise HTTPException(status_code=404, detail="节点不存在")

        # 重启SGLang服务
        restart_service = SglangRestartService(db)
        result = await restart_service.restart_node_sglang(node)

        if result["success"]:
            logger.info(f"用户 {current_user['username']} 重启节点 {node_id} SGLang服务成功")
            return result
        else:
            raise HTTPException(status_code=500, detail=result["error"])

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"重启节点SGLang服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重启SGLang服务失败: {str(e)}")


@router.post("/sglang/monitor-all", summary="监控所有节点SGLang服务")
async def monitor_all_sglang_services(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """监控所有节点的SGLang服务状态"""
    try:
        sglang_service = SglangMonitorService(db)
        result = await sglang_service.monitor_all_nodes_sglang()

        logger.info(f"用户 {current_user['username']} 触发SGLang批量监控")
        return result

    except Exception as e:
        logger.exception(f"监控所有SGLang服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"监控SGLang服务失败: {str(e)}")


@router.post("/sglang/restart-failed", summary="重启失败的SGLang服务")
async def restart_failed_sglang_services(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """重启所有失败的SGLang服务"""
    if not current_user.get("is_admin"):
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        restart_service = SglangRestartService(db)
        result = await restart_service.auto_restart_failed_nodes()

        logger.info(f"用户 {current_user['username']} 触发SGLang批量重启")
        return result

    except Exception as e:
        logger.exception(f"重启失败的SGLang服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重启SGLang服务失败: {str(e)}")


# 节点系统信息相关接口

@router.get("/{node_id}/system-info", summary="获取节点系统信息")
async def get_node_system_info(
    node_id: int,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定节点的系统信息"""
    try:
        # 获取节点信息
        node_service = MinerUNodeService(db)
        node = await node_service.get_node(node_id)

        if not node:
            raise HTTPException(status_code=404, detail="节点不存在")

        if not node.is_enabled:
            raise HTTPException(status_code=400, detail="节点已禁用，无法获取系统信息")

        # 调用MinerU-API获取系统信息
        import aiohttp
        timeout = aiohttp.ClientTimeout(total=30)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(f"{node.base_url}/system/info") as response:
                if response.status == 200:
                    system_info = await response.json()
                    logger.info(f"用户 {current_user['username']} 获取节点 {node_id} 系统信息成功")
                    return {
                        "success": True,
                        "node_id": node_id,
                        "node_name": node.name,
                        "system_info": system_info
                    }
                else:
                    error_msg = f"获取系统信息失败: HTTP {response.status}"
                    logger.error(f"节点 {node_id} {error_msg}")
                    raise HTTPException(status_code=500, detail=error_msg)

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"获取节点系统信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


@router.get("/{node_id}/system-resources", summary="获取节点系统资源使用情况")
async def get_node_system_resources(
    node_id: int,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定节点的系统资源使用情况"""
    try:
        # 获取节点信息
        node_service = MinerUNodeService(db)
        node = await node_service.get_node(node_id)

        if not node:
            raise HTTPException(status_code=404, detail="节点不存在")

        if not node.is_enabled:
            raise HTTPException(status_code=400, detail="节点已禁用，无法获取系统资源")

        # 调用MinerU-API获取系统资源信息
        import aiohttp
        timeout = aiohttp.ClientTimeout(total=15)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(f"{node.base_url}/system/resources") as response:
                if response.status == 200:
                    resources_info = await response.json()
                    logger.debug(f"用户 {current_user['username']} 获取节点 {node_id} 资源信息成功")
                    return {
                        "success": True,
                        "node_id": node_id,
                        "node_name": node.name,
                        "resources": resources_info
                    }
                else:
                    error_msg = f"获取资源信息失败: HTTP {response.status}"
                    logger.error(f"节点 {node_id} {error_msg}")
                    raise HTTPException(status_code=500, detail=error_msg)

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"获取节点系统资源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统资源失败: {str(e)}")

