#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
快速测试修复后的查询
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))


async def quick_test():
    """快速测试"""
    try:
        print("🧪 快速测试修复后的查询...")
        
        from almond_parser.services.task_allocation_service import task_allocation_service
        
        # 测试任务分配
        print("📋 测试任务分配...")
        stats = await task_allocation_service.allocate_pending_tasks(max_allocations=5)
        
        print(f"✅ 分配结果: {stats}")
        
        if stats.get("allocated", 0) > 0:
            print("🎉 成功分配任务！")
        elif stats.get("total_pending", 0) > 0:
            print("⚠️  有等待任务但未能分配")
        else:
            print("ℹ️  没有等待任务")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(quick_test())
