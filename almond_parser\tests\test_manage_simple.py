#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
简单测试 manage.py 的导入
"""

def test_basic_import():
    """测试基本导入"""
    try:
        print("测试导入 manage router...")
        from api.manage import router
        print(f"✅ 成功导入 manage router，包含 {len(router.routes)} 个路由")
        
        # 打印路由信息
        for route in router.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                methods = ', '.join(route.methods)
                print(f"  - {methods} {route.path}")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_basic_import():
        print("🎉 测试通过")
    else:
        print("❌ 测试失败")
