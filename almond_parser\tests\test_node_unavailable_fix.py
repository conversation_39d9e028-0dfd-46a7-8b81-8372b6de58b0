#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试节点不可用修复
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_import_fixes():
    """测试导入修复"""
    print("🧪 测试导入修复...")
    
    try:
        # 测试 document_tasks 导入
        from almond_parser.tasks import document_tasks
        print("  ✅ document_tasks 导入成功")
        
        # 检查 AsyncSession 导入
        import inspect
        source = inspect.getsource(document_tasks)
        if "from sqlalchemy.ext.asyncio import AsyncSession" in source:
            print("  ✅ AsyncSession 导入已添加")
        else:
            print("  ❌ AsyncSession 导入缺失")
        
        # 检查新增的函数
        if hasattr(document_tasks, '_handle_node_unavailable_legacy'):
            print("  ✅ _handle_node_unavailable_legacy 函数存在")
        else:
            print("  ❌ _handle_node_unavailable_legacy 函数不存在")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return False


def test_function_signature():
    """测试函数签名"""
    print("\n🔧 测试函数签名...")
    
    try:
        from almond_parser.tasks.document_tasks import _handle_node_unavailable_legacy, _log_document_event
        import inspect
        
        # 检查 _handle_node_unavailable_legacy 函数签名
        sig = inspect.signature(_handle_node_unavailable_legacy)
        params = list(sig.parameters.keys())
        expected_params = ['db', 'document', 'service_type', 'parse_mode']
        
        if params == expected_params:
            print("  ✅ _handle_node_unavailable_legacy 函数签名正确")
        else:
            print(f"  ❌ _handle_node_unavailable_legacy 函数签名错误: {params}")
        
        # 检查 _log_document_event 函数签名
        sig = inspect.signature(_log_document_event)
        params = list(sig.parameters.keys())
        expected_params = ['db', 'document_id', 'level', 'message', 'source']
        
        if params == expected_params:
            print("  ✅ _log_document_event 函数签名正确")
        else:
            print(f"  ❌ _log_document_event 函数签名错误: {params}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 函数签名测试失败: {e}")
        return False


def test_code_logic():
    """测试代码逻辑"""
    print("\n🎯 测试代码逻辑...")
    
    try:
        # 读取源代码
        with open("almond_parser/tasks/document_tasks.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ("节点不可用处理", "await _handle_node_unavailable_legacy"),
            ("返回重试标记", '"retry_scheduled": True'),
            ("设置重试状态", "DocumentStatus.RETRY_PENDING"),
            ("设置重试原因", 'retry_reason = "node_unavailable"'),
            ("设置重试时间", "next_retry_at = datetime.now()"),
            ("记录重试日志", "已安排系统重试"),
        ]
        
        all_passed = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"  ✅ {check_name}: 已实现")
            else:
                print(f"  ❌ {check_name}: 未找到")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"  ❌ 代码逻辑测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理逻辑"""
    print("\n🚨 测试错误处理逻辑...")
    
    try:
        # 读取源代码
        with open("almond_parser/tasks/document_tasks.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否移除了直接抛出异常的代码
        if 'raise Exception(f"没有可用的 MinerU 节点处理' in content:
            print("  ❌ 仍然存在直接抛出异常的代码")
            return False
        else:
            print("  ✅ 已移除直接抛出异常的代码")
        
        # 检查是否正确处理节点不可用
        if "if not node:" in content and "_handle_node_unavailable_legacy" in content:
            print("  ✅ 节点不可用处理逻辑正确")
        else:
            print("  ❌ 节点不可用处理逻辑有问题")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 错误处理测试失败: {e}")
        return False


def test_integration_compatibility():
    """测试集成兼容性"""
    print("\n🔗 测试集成兼容性...")
    
    try:
        # 检查是否与增强任务兼容
        from almond_parser.tasks.enhanced_document_tasks import enhanced_process_document
        from almond_parser.tasks.document_tasks import process_document
        
        print("  ✅ 原有任务和增强任务可以共存")
        
        # 检查重试任务兼容性
        from almond_parser.tasks.retry_tasks import process_retry_documents
        print("  ✅ 重试任务兼容性正常")
        
        # 检查数据库模型兼容性
        from almond_parser.db.models.document import Document, DocumentStatus
        
        # 检查新增的状态和字段
        if hasattr(DocumentStatus, 'RETRY_PENDING'):
            print("  ✅ RETRY_PENDING 状态存在")
        else:
            print("  ⚠️ RETRY_PENDING 状态不存在（可能需要数据库迁移）")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 集成兼容性测试失败: {e}")
        return False


def show_fix_summary():
    """显示修复总结"""
    print("\n" + "=" * 60)
    print("🎯 节点不可用修复总结")
    print("=" * 60)
    
    print("\n📋 已修复的问题:")
    print("  1️⃣  添加了 AsyncSession 导入")
    print("  2️⃣  修正了 _log_document_event 调用方式")
    print("  3️⃣  实现了节点不可用的重试机制")
    print("  4️⃣  移除了直接抛出异常的代码")
    
    print("\n🔧 修复内容:")
    print("  • 当没有可用节点时，不再直接失败")
    print("  • 设置文档状态为 RETRY_PENDING")
    print("  • 安排5分钟后自动重试")
    print("  • 记录详细的重试日志")
    print("  • 返回重试标记给调用方")
    
    print("\n🎯 工作流程:")
    print("  1. 检测到没有可用节点")
    print("  2. 调用 _handle_node_unavailable_legacy")
    print("  3. 设置文档为重试状态")
    print("  4. 安排定时重试")
    print("  5. 重试任务轮询处理")
    
    print("\n✅ 现在系统具备完整的容错能力！")


def main():
    """主函数"""
    print("🚀 节点不可用修复验证")
    print("=" * 50)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(test_import_fixes())
    test_results.append(test_function_signature())
    test_results.append(test_code_logic())
    test_results.append(test_error_handling())
    test_results.append(test_integration_compatibility())
    
    # 汇总结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"\n📊 测试结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！节点不可用问题已修复。")
    else:
        print("⚠️ 部分测试失败，请检查相关代码。")
    
    # 显示修复总结
    show_fix_summary()


if __name__ == "__main__":
    main()
