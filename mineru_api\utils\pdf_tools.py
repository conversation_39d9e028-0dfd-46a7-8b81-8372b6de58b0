# -*- encoding: utf-8 -*-
"""
@File   :pdf_tools.py
@Time   :2025/9/15 10:22
<AUTHOR>
"""
from loguru import logger


def check_pdf_file(file_bytes: bytes) -> bool:
    """
    检查文件字节流是否包含PDF数据，并提取它。
    这可以处理标准PDF和包含PJL头的PDF。

    参数:
        file_bytes (bytes): 文件的完整字节内容。

    返回:
        bytes: 从 '%PDF' 开始的PDF实际内容字节流。

    异常:
        ValueError: 如果在字节流中找不到 '%PDF-' 签名。
    """
    # 1. 检查是否为标准PDF文件
    if file_bytes.startswith(b'%PDF-'):
        logger.info("检测到标准PDF文件。")
        return True

    # 2. 如果不是，则搜索PDF文件头签名 b'%PDF-'
    #    这个签名比 b'%PDF' 更具唯一性，可以避免误判
    logger.info("文件不是以 '%PDF' 开头，正在尝试搜索PJL包装的PDF...")
    pdf_start_index = file_bytes.find(b'%PDF-')

    if pdf_start_index != -1:
        logger.info(f"在索引 {pdf_start_index} 处找到PDF文件头。")
        # 3. 如果找到，返回从该位置开始的所有数据
        return True

    # 4. 如果两种方法都失败，则抛出异常
    raise False
