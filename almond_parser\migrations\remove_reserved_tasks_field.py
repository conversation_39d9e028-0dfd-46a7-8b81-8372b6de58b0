#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
移除 reserved_tasks 字段的数据库迁移脚本
简化并发控制机制
"""
import asyncio
from sqlalchemy import text
from loguru import logger

from almond_parser.db.database import get_async_session


async def migrate_remove_reserved_tasks():
    """移除 reserved_tasks 字段"""
    try:
        async with get_async_session() as db:
            logger.info("开始移除 reserved_tasks 字段...")
            
            # 检查字段是否存在
            check_column_sql = """
            SELECT COUNT(*) as count
            FROM information_schema.columns 
            WHERE table_name = 'mineru_nodes' 
            AND column_name = 'reserved_tasks'
            AND table_schema = DATABASE()
            """
            
            result = await db.execute(text(check_column_sql))
            column_exists = result.scalar() > 0
            
            if not column_exists:
                logger.info("reserved_tasks 字段不存在，跳过迁移")
                return
            
            # 在删除字段前，先重置所有节点的 current_tasks 为 0
            # 确保系统重启后状态一致
            reset_sql = """
            UPDATE mineru_nodes 
            SET current_tasks = 0 
            WHERE current_tasks > 0
            """
            
            await db.execute(text(reset_sql))
            logger.info("已重置所有节点的 current_tasks 为 0")
            
            # 删除 reserved_tasks 字段
            drop_column_sql = """
            ALTER TABLE mineru_nodes 
            DROP COLUMN reserved_tasks
            """
            
            await db.execute(text(drop_column_sql))
            await db.commit()
            
            logger.info("✅ reserved_tasks 字段删除成功")
            
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        raise


async def rollback_add_reserved_tasks():
    """回滚：重新添加 reserved_tasks 字段"""
    try:
        async with get_async_session() as db:
            logger.info("开始回滚：重新添加 reserved_tasks 字段...")
            
            # 检查字段是否已存在
            check_column_sql = """
            SELECT COUNT(*) as count
            FROM information_schema.columns 
            WHERE table_name = 'mineru_nodes' 
            AND column_name = 'reserved_tasks'
            AND table_schema = DATABASE()
            """
            
            result = await db.execute(text(check_column_sql))
            column_exists = result.scalar() > 0
            
            if column_exists:
                logger.info("reserved_tasks 字段已存在，跳过回滚")
                return
            
            # 添加 reserved_tasks 字段
            add_column_sql = """
            ALTER TABLE mineru_nodes 
            ADD COLUMN reserved_tasks INT DEFAULT 0 COMMENT '预留任务数'
            """
            
            await db.execute(text(add_column_sql))
            await db.commit()
            
            logger.info("✅ reserved_tasks 字段添加成功")
            
    except Exception as e:
        logger.error(f"❌ 回滚失败: {e}")
        raise


async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        await rollback_add_reserved_tasks()
    else:
        await migrate_remove_reserved_tasks()


if __name__ == "__main__":
    asyncio.run(main())
