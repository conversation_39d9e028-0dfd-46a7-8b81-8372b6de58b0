# 节点并发控制系统

## 🎯 解决的问题

1. **并发分配竞态条件**：多个任务同时分配给同一节点，超出最大并发限制
2. **任务槽位泄漏**：任务失败或卡住时，current_tasks 不会自动减少
3. **30分钟超时控制**：确保长时间运行的任务能够及时释放资源

## 🏗️ 系统架构

### 核心组件

1. **NodeConcurrencyManager** - 并发控制管理器
2. **数据库字段扩展** - 添加 `reserved_tasks` 和 `last_task_assigned_at`
3. **任务生命周期钩子** - 在关键节点更新计数器
4. **定时清理机制** - 清理超时任务和过期预留

### 实际工作流程

```mermaid
graph TD
    A[文档上传] --> B[process_batch_documents]
    B --> C[为每个文档调用 process_document]
    C --> D[原子性节点分配]
    D --> E{节点可用?}
    E -->|是| F[预留任务槽位 reserved_tasks++]
    E -->|否| G[返回无可用节点]
    F --> H[调用 MinerU API]
    H --> I{API调用成功?}
    I -->|是| J[确认任务开始 reserved_tasks--, current_tasks++]
    I -->|否| K[释放预留 reserved_tasks--]
    J --> L[任务执行中]
    L --> M{任务完成?}
    M -->|成功| N[回调释放槽位 current_tasks--, success_tasks++]
    M -->|失败| O[回调释放槽位 current_tasks--, failed_tasks++]
    M -->|超时| P[定时清理强制释放 current_tasks--, failed_tasks++]
```

## 🔧 关键特性

### 1. 原子性分配
```python
async with db.begin():
    node = await self._find_available_node_with_lock(db, service_type, parse_mode)
    if node:
        node.reserved_tasks += 1  # 立即预留
        node.last_task_assigned_at = datetime.now()
        await db.flush()  # 确保立即生效
```

### 2. 并发检查逻辑
```python
# 检查总任务数（当前+预留）不超过最大并发数
(MinerUNode.current_tasks + MinerUNode.reserved_tasks) < MinerUNode.max_concurrent_tasks
```

### 3. 任务生命周期管理
- **分配时**：`reserved_tasks++`
- **开始时**：`reserved_tasks--`, `current_tasks++`
- **完成时**：`current_tasks--`, `success_tasks++`
- **失败时**：`current_tasks--`, `failed_tasks++`

### 4. 超时保护机制
- **预留超时**：5分钟，防止分配后未确认的任务占用槽位
- **任务超时**：30分钟，强制释放长时间运行的任务

## 📊 监控指标

### 节点状态字段
- `current_tasks`: 当前正在执行的任务数
- `reserved_tasks`: 已分配但未开始的任务数
- `total_tasks`: 总任务数
- `success_tasks`: 成功任务数
- `failed_tasks`: 失败任务数
- `last_task_assigned_at`: 最后分配任务时间

### 清理统计
```python
{
    "timeout_tasks": 2,           # 清理的超时任务数
    "released_slots": 2,          # 释放的任务槽位数
    "cleaned_nodes": 1,           # 清理的节点数
    "released_reservations": 3,   # 释放的预留数
    "errors": []                  # 错误信息
}
```

## 🚀 使用方法

### 1. 运行数据库迁移
```bash
# 添加新字段
python -m almond_parser.migrations.add_node_concurrency_fields
```

### 2. 实际集成位置

**主要修改文件**: `almond_parser/tasks/document_tasks.py`

```python
# 在 process_document 函数中的节点分配
node = await node_concurrency_manager.allocate_node_with_lock(
    db=db,
    service_type=service_type_enum,
    parse_mode=parse_mode if parse_mode != "auto" else None,
    document_id=document_id
)
```

### 3. 确认任务开始
```python
# API调用成功后确认
confirmed = await node_concurrency_manager.confirm_task_start(
    db=db,
    node_id=node.id,
    task_id="task_123",
    document_id="doc_123"
)
```

### 4. 释放任务槽位
```python
# 任务完成时释放
released = await node_concurrency_manager.release_task_slot(
    db=db,
    node_id=node.id,
    task_id="task_123",
    success=True,
    reason="任务完成"
)
```

## ⚠️ 重要保证

### 1. 原子性保证
- 所有计数器操作都在数据库事务中执行
- 使用 `SELECT FOR UPDATE` 防止并发冲突
- 失败时自动回滚，确保数据一致性

### 2. 超时保证
- 30分钟强制超时，确保任务不会无限占用资源
- 5分钟预留超时，防止分配后未确认的泄漏
- 定时清理机制，每10分钟执行一次

### 3. 容错保证
- 任何步骤失败都会正确释放资源
- 异常情况下的自动恢复机制
- 详细的日志记录便于问题排查

## 🧪 测试验证

运行并发控制测试：
```bash
python -m pytest almond_parser/tests/test_node_concurrency.py -v
```

测试覆盖：
- 并发分配控制
- 任务生命周期管理
- 超时清理机制
- 预留过期清理

## 📈 性能影响

- **数据库锁定时间**：极短（毫秒级），只在分配时加锁
- **额外存储开销**：每个节点增加2个字段
- **清理开销**：每10分钟执行一次，影响极小
- **并发性能**：通过行锁而非表锁，影响最小

## 🔍 故障排查

### 常见问题

1. **节点显示满载但实际空闲**
   - 检查 `reserved_tasks` 是否有残留
   - 运行清理命令：`await node_concurrency_manager.cleanup_expired_reservations(db)`

2. **任务长时间不释放**
   - 检查是否超过30分钟
   - 运行超时清理：`await node_concurrency_manager.cleanup_timeout_tasks(db)`

3. **计数器不一致**
   - 查看健康检查日志
   - 对比 mineru-api 的实际任务数

### 监控建议

- 监控 `current_tasks + reserved_tasks` 与 `max_concurrent_tasks` 的比值
- 关注清理统计中的异常数据
- 定期检查超时任务的频率和原因
