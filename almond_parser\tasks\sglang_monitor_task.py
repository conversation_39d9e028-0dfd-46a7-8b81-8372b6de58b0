# -*- encoding: utf-8 -*-
"""
SGLang 服务监控定时任务
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any
from loguru import logger

from almond_parser.db.database import get_async_session
from almond_parser.services.sglang_monitor_service import SglangMonitorService
from almond_parser.services.sglang_restart_service import SglangRestartService
from almond_parser.services.sglang_alert_service import SglangAlertService


async def monitor_sglang_services() -> Dict[str, Any]:
    """监控所有节点的SGLang服务"""
    try:
        logger.info("🔍 开始SGLang服务监控任务")

        async with get_async_session() as db:
            monitor_service = SglangMonitorService(db)

            # 监控所有节点的SGLang服务
            monitor_result = await monitor_service.monitor_all_nodes_sglang()

            if monitor_result["success"]:
                logger.info(
                    f"SGLang监控完成: 总计 {monitor_result['total_nodes']} 个节点, "
                    f"健康 {monitor_result['healthy_count']} 个, "
                    f"异常 {monitor_result['error_count']} 个"
                )
            else:
                logger.error(f"SGLang监控失败: {monitor_result.get('error')}")

            return {
                "task": "monitor_sglang_services",
                "timestamp": datetime.now().isoformat(),
                "result": monitor_result
            }

    except Exception as e:
        logger.error(f"SGLang监控任务异常: {e}")
        return {
            "task": "monitor_sglang_services",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": str(e)
        }


async def auto_restart_failed_sglang() -> Dict[str, Any]:
    """自动重启失败的SGLang服务"""
    try:
        logger.info("🔄 开始SGLang自动重启任务")

        async with get_async_session() as db:
            restart_service = SglangRestartService(db)

            # 自动重启失败的节点
            restart_result = await restart_service.auto_restart_failed_nodes()

            if restart_result["success"]:
                if restart_result["restart_count"] > 0:
                    logger.info(
                        f"SGLang自动重启完成: 检查 {restart_result['total_checked']} 个节点, "
                        f"重启 {restart_result['restart_count']} 个节点, "
                        f"成功 {restart_result['success_count']} 个"
                    )
                else:
                    logger.debug("SGLang自动重启: 没有需要重启的节点")
            else:
                logger.error(f"SGLang自动重启失败: {restart_result.get('error')}")

            return {
                "task": "auto_restart_failed_sglang",
                "timestamp": datetime.now().isoformat(),
                "result": restart_result
            }

    except Exception as e:
        logger.error(f"SGLang自动重启任务异常: {e}")
        return {
            "task": "auto_restart_failed_sglang",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": str(e)
        }


async def check_sglang_alerts() -> Dict[str, Any]:
    """检查SGLang服务告警"""
    try:
        logger.info("🚨 开始SGLang告警检查任务")

        async with get_async_session() as db:
            alert_service = SglangAlertService(db)

            # 检查并发送告警
            alert_result = await alert_service.check_and_send_alerts()

            if alert_result["success"]:
                if alert_result["alert_count"] > 0:
                    logger.warning(
                        f"SGLang告警检查完成: 发送 {alert_result['alert_count']} 条告警, "
                        f"涉及 {alert_result['total_nodes']} 个节点"
                    )
                else:
                    logger.debug("SGLang告警检查: 没有需要告警的节点")
            else:
                logger.error(f"SGLang告警检查失败: {alert_result.get('error')}")

            return {
                "task": "check_sglang_alerts",
                "timestamp": datetime.now().isoformat(),
                "result": alert_result
            }

    except Exception as e:
        logger.error(f"SGLang告警检查任务异常: {e}")
        return {
            "task": "check_sglang_alerts",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": str(e)
        }


async def sglang_comprehensive_monitor() -> Dict[str, Any]:
    """SGLang综合监控任务（监控 + 重启 + 告警）"""
    try:
        logger.info("🔍 开始SGLang综合监控任务")
        start_time = datetime.now()

        # 1. 先执行监控
        monitor_result = await monitor_sglang_services()

        # 等待2秒，让监控结果写入数据库
        await asyncio.sleep(2)

        # 2. 执行自动重启
        restart_result = await auto_restart_failed_sglang()

        # 如果有重启操作，等待5分钟后再检查告警
        if restart_result.get("result", {}).get("restart_count", 0) > 0:
            logger.info("检测到重启操作，等待5分钟后检查告警...")
            await asyncio.sleep(300)  # 等待5分钟
        else:
            # 没有重启操作，等待10秒后检查告警
            await asyncio.sleep(10)

        # 3. 检查告警
        alert_result = await check_sglang_alerts()

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 汇总结果
        summary = {
            "task": "sglang_comprehensive_monitor",
            "timestamp": start_time.isoformat(),
            "duration_seconds": duration,
            "monitor_result": monitor_result,
            "restart_result": restart_result,
            "alert_result": alert_result
        }

        # 记录汇总日志
        monitor_stats = monitor_result.get("result", {})
        restart_stats = restart_result.get("result", {})
        alert_stats = alert_result.get("result", {})

        logger.info(
            f"SGLang综合监控完成 (耗时 {duration:.1f}s): "
            f"监控 {monitor_stats.get('total_nodes', 0)} 节点, "
            f"健康 {monitor_stats.get('healthy_count', 0)} 个, "
            f"重启 {restart_stats.get('restart_count', 0)} 个, "
            f"告警 {alert_stats.get('alert_count', 0)} 条"
        )

        return summary

    except Exception as e:
        logger.error(f"SGLang综合监控任务异常: {e}")
        return {
            "task": "sglang_comprehensive_monitor",
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": str(e)
        }


# 定时任务函数（供ARQ调用）
async def sglang_monitor_cron(ctx):
    """SGLang监控定时任务（每2分钟执行）"""
    return await monitor_sglang_services()


async def sglang_restart_cron(ctx):
    """SGLang重启定时任务（每5分钟执行）"""
    return await auto_restart_failed_sglang()


async def sglang_alert_cron(ctx):
    """SGLang告警定时任务（每10分钟执行）"""
    return await check_sglang_alerts()


async def sglang_comprehensive_cron(ctx):
    """SGLang综合监控定时任务（每15分钟执行）"""
    return await sglang_comprehensive_monitor()
