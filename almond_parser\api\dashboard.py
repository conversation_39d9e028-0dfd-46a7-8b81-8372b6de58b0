# -*- encoding: utf-8 -*-
"""
Dashboard API - 仪表板数据接口
"""
from datetime import datetime, timedelta
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, case, cast, Float, text
from loguru import logger

from almond_parser.db import get_db
from almond_parser.db.models import (
    MinerUNode, ParseTask, Document, DocumentStatus,
    NodeStatus, ServiceType, TaskStatus
)
from almond_parser.utils.auth import get_current_user
from almond_parser.schemas.base import BaseResponse

router = APIRouter(prefix="/dashboard", tags=["Dashboard"])


@router.get("/system-status")
async def get_system_status(
        db: AsyncSession = Depends(get_db),
        current_user: dict = Depends(get_current_user)
):
    """获取系统运行状态"""
    try:
        today = datetime.now().date()

        # --- 节点统计 ---
        stmt_total = select(func.count(MinerUNode.id))
        total_nodes = (await db.execute(stmt_total)).scalar() or 0

        stmt_online = select(func.count(MinerUNode.id)).where(
            MinerUNode.is_enabled == True,
            MinerUNode.status == NodeStatus.ONLINE
        )
        online_nodes = (await db.execute(stmt_online)).scalar() or 0

        stmt_document = select(func.count()).where(
            MinerUNode.is_enabled == True,
            MinerUNode.status == NodeStatus.ONLINE,
            MinerUNode.service_type == ServiceType.DOCUMENT
        )
        document_nodes = (await db.execute(stmt_document)).scalar() or 0

        stmt_knowledge = select(func.count()).where(
            MinerUNode.is_enabled == True,
            MinerUNode.status == NodeStatus.ONLINE,
            MinerUNode.service_type == ServiceType.KNOWLEDGE_BASE
        )
        knowledge_nodes = (await db.execute(stmt_knowledge)).scalar() or 0

        stmt_universal = select(func.count()).where(
            MinerUNode.is_enabled == True,
            MinerUNode.status == NodeStatus.ONLINE,
            MinerUNode.service_type == ServiceType.UNIVERSAL
        )
        universal_nodes = (await db.execute(stmt_universal)).scalar() or 0

        stmt_processing = select(func.sum(MinerUNode.current_tasks)).where(
            MinerUNode.is_enabled == True
        )
        processing_tasks = (await db.execute(stmt_processing)).scalar() or 0

        stmt_avg_load = select(
            func.avg(
                case(
                    (MinerUNode.max_concurrent_tasks > 0,
                     MinerUNode.current_tasks * 100.0 / MinerUNode.max_concurrent_tasks),
                    else_=0.0
                )
            )
        ).where(MinerUNode.is_enabled == True)
        avg_load_result = (await db.execute(stmt_avg_load)).scalar()
        avg_load = round(avg_load_result or 0, 1)

        # --- 今日任务统计 ---
        stmt_task = select(
            func.count(Document.id).label('total_tasks'),
            func.sum(case((Document.status == DocumentStatus.PARSING, 1), else_=0)).label('queue_tasks'),
            func.sum(case((Document.status == DocumentStatus.COMPLETED, 1), else_=0)).label('success_tasks'),
            func.sum(case((Document.status == DocumentStatus.FAILED, 1), else_=0)).label('failed_tasks')
        ).where(func.date(Document.created_at) == today)
        task_result = (await db.execute(stmt_task)).first()

        success_tasks = int(task_result.success_tasks or 0)
        failed_tasks = int(task_result.failed_tasks or 0)
        total_completed = success_tasks + failed_tasks

        success_rate = round(success_tasks * 100.0 / total_completed, 1) if total_completed else 0

        # --- 今日提交文档数 ---
        stmt_docs = select(func.count()).where(
            func.date(Document.created_at) == today
        )
        submitted_docs = (await db.execute(stmt_docs)).scalar() or 0

        # --- 今日完成解析数 ---
        stmt_completed_tasks = select(func.count()).where(
            func.date(Document.completed_at) == today,
            ParseTask.status == TaskStatus.COMPLETED
        )
        completed_tasks_today = (await db.execute(stmt_completed_tasks)).scalar() or 0

        # --- 今日平均解析耗时（分钟） ---
        stmt_avg_duration = select(
            func.avg(
                func.timestampdiff(
                    text('SECOND'),
                    ParseTask.started_at,
                    ParseTask.completed_at
                )
            )
        ).where(
            func.date(Document.created_at) == today,
            Document.started_at.isnot(None),
            Document.completed_at.isnot(None)
        )
        avg_duration_seconds = (await db.execute(stmt_avg_duration)).scalar() or 0
        avg_duration_minutes = round(avg_duration_seconds / 60, 1)

        return {
            "onlineNodes": online_nodes,
            "totalNodes": total_nodes,
            "documentNodes": document_nodes,
            "knowledgeNodes": knowledge_nodes,
            "universalNodes": universal_nodes,
            "queueTasks": task_result.queue_tasks or 0,
            "processingTasks": processing_tasks,
            "successRate": success_rate,
            "successTasks": success_tasks,
            "failedTasks": failed_tasks,
            "avgLoad": avg_load,
            "submittedDocsToday": submitted_docs,
            "completedParsesToday": completed_tasks_today,
            "avgDuration": avg_duration_minutes
        }

    except Exception as e:
        logger.exception(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取系统状态失败")



@router.get("/today-stats")
async def get_today_stats(
        db: AsyncSession = Depends(get_db),
        current_user: dict = Depends(get_current_user)
):
    """获取今日统计数据"""
    try:
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)

        # 1. 今日提交/完成数
        stmt_today = select(
            func.count(Document.id).label('submitted_docs'),
            func.sum(case(
                (Document.status == DocumentStatus.COMPLETED, 1),
                else_=0
            )).label('completed_docs')
        ).where(func.date(Document.created_at) == today)
        today_result = (await db.execute(stmt_today)).first()

        # 2. 昨日提交/完成数
        stmt_yesterday = select(
            func.count(Document.id).label('submitted_docs'),
            func.sum(case(
                (Document.status == DocumentStatus.COMPLETED, 1),
                else_=0
            )).label('completed_docs')
        ).where(func.date(Document.created_at) == yesterday)
        yesterday_result = (await db.execute(stmt_yesterday)).first()

        # 3. 今日平均耗时（单位：秒）
        stmt_today_duration = select(
            func.avg(
                cast(func.timestampdiff(
                    text('SECOND'),
                    Document.started_at,
                    Document.completed_at
                ), Float)
            ).label('avg_seconds')
        ).where(
            func.date(Document.created_at) == today,
            Document.status == DocumentStatus.COMPLETED,
            Document.started_at.isnot(None),
            Document.completed_at.isnot(None)
        )
        avg_today_seconds = (await db.execute(stmt_today_duration)).scalar() or 0

        # 4. 昨日平均耗时（单位：秒）
        stmt_yesterday_duration = select(
            func.avg(
                cast(
                    func.timestampdiff(
                        text('SECOND'),
                        Document.started_at,
                        Document.completed_at
                    ), Float
                )
            ).label('avg_seconds')
        ).where(
            func.date(Document.created_at) == yesterday,
            Document.status == DocumentStatus.COMPLETED,
            Document.started_at.isnot(None),
            Document.completed_at.isnot(None)
        )
        avg_yesterday_seconds = (await db.execute(stmt_yesterday_duration)).scalar() or 0

        # 5. 格式化
        avg_duration = f"{round(avg_today_seconds / 60, 1)}min"
        duration_trend = round((avg_today_seconds - avg_yesterday_seconds) / 60, 1)

        return {
            "submittedDocs": today_result.submitted_docs or 0,
            "docsTrend": (today_result.submitted_docs or 0) - (yesterday_result.submitted_docs or 0),
            "completedDocs": today_result.completed_docs or 0,
            "completedTrend": (today_result.completed_docs or 0) - (yesterday_result.completed_docs or 0),
            "avgDuration": avg_duration,
            "durationTrend": duration_trend  # 单位：分钟，正值表示耗时变长
        }

    except Exception as e:
        logger.exception("获取今日统计失败")
        raise HTTPException(status_code=500, detail="获取今日统计失败")


@router.get("/recent-errors")
async def get_recent_errors(
        limit: int = Query(10, ge=1, le=50),
        db: AsyncSession = Depends(get_db),
        current_user: dict = Depends(get_current_user)
):
    """获取最近的错误任务"""
    try:
        # 获取最近的失败任务
        failed_tasks = await db.execute(
            select(ParseTask)
            .where(ParseTask.status == TaskStatus.FAILED)
            .order_by(desc(ParseTask.updated_at))
            .limit(limit)
        )

        errors = []
        for task in failed_tasks.scalars():
            # 获取对应的文档信息
            doc_result = await db.execute(
                select(Document.file_name)
                .where(Document.document_id == task.document_id)
            )
            doc = doc_result.scalar()

            # 获取节点信息
            node_name = None
            if task.node_id:
                node_result = await db.execute(
                    select(MinerUNode.name)
                    .where(MinerUNode.id == task.node_id)
                )
                node_name = node_result.scalar()

            errors.append({
                "id": task.id,
                "taskId": task.task_id,
                "filename": doc or task.filename,
                "errorType": "解析失败",  # 可以根据error_message分类
                "message": task.error_message or "未知错误",
                "nodeName": node_name,
                "timestamp": task.updated_at.isoformat(),
                "stackTrace": task.error_message  # 如果有详细堆栈信息的话
            })

        return errors

    except Exception as e:
        logger.error(f"获取错误列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取错误列表失败")


@router.get("/task-trend")
async def get_task_trend(
        days: int = Query(7, ge=1, le=30),
        db: AsyncSession = Depends(get_db),
        current_user: dict = Depends(get_current_user)
):
    """获取任务趋势数据"""
    try:
        result_data = []

        # 获取当前日期的前若干天
        for i in range(days):
            date = datetime.now().date() - timedelta(days=days - 1 - i)

            # 查询当天的文档状态统计，基于 Document 表
            day_stats = await db.execute(
                select(
                    func.sum(case((Document.status == DocumentStatus.COMPLETED, 1), else_=0)).label('success'),
                    func.sum(case((Document.status == DocumentStatus.FAILED, 1), else_=0)).label('failed')
                ).where(func.date(Document.created_at) == date)  # 根据 Document 的创建日期来筛选
            )

            # 获取查询结果
            result = day_stats.first()  # 获取查询结果，直接访问， 不需要 await

            # 确保 result 非空并获取数据
            success_count = result[0] if result and result[0] is not None else 0
            failed_count = result[1] if result and result[1] is not None else 0

            # 将结果添加到列表中
            result_data.append({
                "date": date.strftime('%Y-%m-%d'),  # 前端需要的日期格式
                "successCount": success_count,
                "failedCount": failed_count
            })

        return result_data  # 直接返回结果数据，符合前端期望的结构

    except Exception as e:
        logger.error(f"获取任务趋势失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务趋势失败")


@router.get("/node-load")
async def get_node_load(
        hours: int = Query(24, ge=1, le=168),
        db: AsyncSession = Depends(get_db),
        current_user: dict = Depends(get_current_user)
):
    """获取节点负载数据"""
    try:
        times = []
        load_data = []

        # 这里应该从实际的监控数据中获取，暂时使用模拟数据
        for i in range(hours):
            time = datetime.now() - timedelta(hours=hours - 1 - i)
            times.append(time.strftime('%H:%M'))
            # 模拟负载数据，实际应该从监控系统获取
            load_data.append(50 + (i % 10) * 5)

        return {
            "times": times,
            "loadData": load_data
        }

    except Exception as e:
        logger.error(f"获取节点负载失败: {e}")
        raise HTTPException(status_code=500, detail="获取节点负载失败")


@router.get("/error-distribution")
async def get_error_distribution(
        db: AsyncSession = Depends(get_db),
        current_user: dict = Depends(get_current_user)
):
    """获取错误分布数据"""
    try:
        # 这里应该根据实际的错误分类统计，暂时使用模拟数据
        return [
            {"value": 35, "name": "解析超时"},
            {"value": 25, "name": "格式错误"},
            {"value": 20, "name": "网络异常"},
            {"value": 12, "name": "内存不足"},
            {"value": 8, "name": "其他错误"}
        ]

    except Exception as e:
        logger.error(f"获取错误分布失败: {e}")
        raise HTTPException(status_code=500, detail="获取错误分布失败")


@router.post("/retry-task/{task_id}")
async def retry_task(
        task_id: str,
        db: AsyncSession = Depends(get_db),
        current_user: dict = Depends(get_current_user)
):
    """重试失败的任务"""
    try:
        # 查找任务
        task_result = await db.execute(
            select(ParseTask).where(ParseTask.task_id == task_id)
        )
        task = task_result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        if task.status != TaskStatus.FAILED:
            raise HTTPException(status_code=400, detail="只能重试失败的任务")

        # 重置任务状态
        task.status = TaskStatus.PENDING
        task.error_message = None
        task.retry_count += 1
        task.updated_at = datetime.now()

        await db.commit()

        logger.info(f"任务 {task_id} 已重新提交")
        return BaseResponse(message=f"任务 {task_id} 已重新提交")

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"重试任务失败: {e}")
        raise HTTPException(status_code=500, detail="重试任务失败")
