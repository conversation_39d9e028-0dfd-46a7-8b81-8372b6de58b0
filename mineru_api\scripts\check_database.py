#!/usr/bin/env python3
"""
数据库初始化检查脚本
用于验证 mineru_api 的所有数据库都能正常初始化
"""

import sys
import sqlite3
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from mineru_api.config import get_auth_config, BASE_DIR, ENABLE_AUTH
from mineru_api.services.history_service import TaskHistoryService


def check_auth_database():
    """检查认证数据库"""
    if not ENABLE_AUTH:
        logger.info("认证功能已禁用，跳过认证数据库检查")
        return True
    
    try:
        from mineru_api.auth.backends import get_auth_backend
        
        auth_config = get_auth_config()
        logger.info(f"检查认证数据库，后端类型: {auth_config.backend_type.value}")
        
        # 初始化认证后端（会自动创建数据库）
        backend = get_auth_backend(auth_config)
        
        # 如果是 SQLite 后端，验证数据库文件
        if auth_config.backend_type.value == "sqlite":
            db_path = Path(auth_config.sqlite_path)
            if db_path.exists():
                logger.info(f"✅ 认证数据库文件存在: {db_path}")
                
                # 测试连接
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'")
                    if cursor.fetchone():
                        logger.info("✅ 认证数据库表结构正常")
                    else:
                        logger.error("❌ 认证数据库缺少 api_keys 表")
                        return False
            else:
                logger.error(f"❌ 认证数据库文件不存在: {db_path}")
                return False
        else:
            logger.info(f"✅ 认证后端 {auth_config.backend_type.value} 初始化成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 认证数据库检查失败: {e}")
        return False


def check_history_database():
    """检查任务历史数据库"""
    try:
        logger.info("检查任务历史数据库")
        
        # 初始化历史服务（会自动创建数据库）
        history_service = TaskHistoryService()
        
        # 验证数据库文件
        if history_service.db_path.exists():
            logger.info(f"✅ 任务历史数据库文件存在: {history_service.db_path}")
            
            # 测试连接
            with sqlite3.connect(history_service.db_path) as conn:
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='task_history'")
                if cursor.fetchone():
                    logger.info("✅ 任务历史数据库表结构正常")
                else:
                    logger.error("❌ 任务历史数据库缺少 task_history 表")
                    return False
        else:
            logger.error(f"❌ 任务历史数据库文件不存在: {history_service.db_path}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 任务历史数据库检查失败: {e}")
        return False


def check_directories():
    """检查必要的目录"""
    try:
        logger.info("检查必要的目录结构")
        
        directories = [
            BASE_DIR / "data",
            BASE_DIR / "logs",
            BASE_DIR / "output",
            BASE_DIR / "temp"
        ]
        
        for directory in directories:
            if directory.exists():
                logger.info(f"✅ 目录存在: {directory}")
            else:
                logger.info(f"📁 创建目录: {directory}")
                directory.mkdir(parents=True, exist_ok=True)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 目录检查失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🔍 开始数据库初始化检查...")
    
    success = True
    
    # 检查目录
    if not check_directories():
        success = False
    
    # 检查认证数据库
    if not check_auth_database():
        success = False
    
    # 检查任务历史数据库
    if not check_history_database():
        success = False
    
    if success:
        logger.info("🎉 所有数据库检查通过！")
        print("\n✅ 数据库初始化检查完成，所有组件正常")
    else:
        logger.error("❌ 数据库检查失败！")
        print("\n❌ 数据库初始化检查失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
