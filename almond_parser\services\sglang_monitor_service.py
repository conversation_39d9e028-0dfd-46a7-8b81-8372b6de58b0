# -*- encoding: utf-8 -*-
"""
SGLang 服务监控服务
"""
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from loguru import logger

from almond_parser.db.models import MinerUNode, SglangStatus, NodeStatus, ParseMode
from almond_parser.config import settings


class SglangMonitorService:
    """SGLang 服务监控服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.timeout = aiohttp.ClientTimeout(total=10, connect=5)
    
    def calculate_sglang_info(self, node: MinerUNode) -> Dict[str, Any]:
        """根据节点信息计算SGLang服务信息"""
        try:
            # 从mineru-api端口推算sglang端口
            # 假设规则：mineru-api端口P，sglang端口为30000 + (P - 2233)
            base_mineru_port = 2233
            base_sglang_port = 30000
            
            sglang_port = base_sglang_port + (node.port - base_mineru_port)
            sglang_url = f"http://{node.host}:{sglang_port}"
            
            return {
                "port": sglang_port,
                "url": sglang_url
            }
        except Exception as e:
            logger.error(f"计算SGLang信息失败: {e}")
            return {"port": None, "url": None}
    
    async def check_sglang_health_via_mineru_api(self, node_base_url: str) -> Dict[str, Any]:
        """通过MinerU-API检查SGLang服务健康状态"""
        result = {
            "healthy": False,
            "response_time": None,
            "error": None,
            "status_code": None,
            "sglang_status": None
        }

        try:
            start_time = datetime.now()

            # 调用MinerU-API的SGLang健康检查接口
            health_url = f"{node_base_url}/sglang/health"

            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(health_url) as response:
                    response_time = (datetime.now() - start_time).total_seconds()
                    result["response_time"] = response_time
                    result["status_code"] = response.status

                    if response.status == 200:
                        # 解析MinerU-API返回的SGLang健康状态
                        health_data = await response.json()

                        # MinerU-API返回的健康状态结构
                        sglang_healthy = health_data.get("health_check", {}).get("healthy", False)
                        result["healthy"] = sglang_healthy
                        result["sglang_status"] = health_data

                        if sglang_healthy:
                            logger.debug(f"SGLang健康检查通过: {health_url} ({response_time:.2f}s)")
                        else:
                            result["error"] = health_data.get("health_check", {}).get("error", "SGLang服务异常")
                            logger.warning(f"SGLang健康检查失败: {health_url} - {result['error']}")
                    else:
                        result["error"] = f"MinerU-API HTTP {response.status}"
                        logger.warning(f"MinerU-API健康检查失败: {health_url} - HTTP {response.status}")

        except asyncio.TimeoutError:
            result["error"] = "MinerU-API请求超时"
            logger.warning(f"MinerU-API健康检查超时: {node_base_url}")
        except aiohttp.ClientConnectorError:
            result["error"] = "MinerU-API连接失败"
            logger.warning(f"MinerU-API连接失败: {node_base_url}")
        except Exception as e:
            result["error"] = f"MinerU-API检查异常: {str(e)}"
            logger.warning(f"MinerU-API健康检查异常: {node_base_url} - {e}")

        return result

    async def get_sglang_status_via_mineru_api(self, node_base_url: str) -> Dict[str, Any]:
        """通过MinerU-API获取SGLang服务基本状态"""
        result = {
            "success": False,
            "running": False,
            "error": None,
            "status_data": None
        }

        try:
            # 调用MinerU-API的SGLang状态接口
            status_url = f"{node_base_url}/sglang/status"

            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(status_url) as response:
                    if response.status == 200:
                        status_data = await response.json()
                        result["success"] = True
                        result["running"] = status_data.get("running", False)
                        result["status_data"] = status_data

                        logger.debug(f"SGLang状态查询成功: {node_base_url} - 运行状态: {result['running']}")
                    else:
                        result["error"] = f"MinerU-API状态查询失败: HTTP {response.status}"
                        logger.warning(f"MinerU-API状态查询失败: {status_url} - HTTP {response.status}")

        except asyncio.TimeoutError:
            result["error"] = "MinerU-API状态查询超时"
            logger.warning(f"MinerU-API状态查询超时: {node_base_url}")
        except aiohttp.ClientConnectorError:
            result["error"] = "MinerU-API连接失败"
            logger.warning(f"MinerU-API连接失败: {node_base_url}")
        except Exception as e:
            result["error"] = f"MinerU-API状态查询异常: {str(e)}"
            logger.warning(f"MinerU-API状态查询异常: {node_base_url} - {e}")

        return result
    
    async def update_node_sglang_info(self, node: MinerUNode) -> None:
        """更新节点的SGLang信息"""
        try:
            # 计算SGLang信息
            sglang_info = self.calculate_sglang_info(node)
            
            # 更新节点SGLang信息
            node.sglang_port = sglang_info["port"]
            node.sglang_url = sglang_info["url"]
            
            await self.db.commit()
            logger.debug(f"更新节点 {node.id} SGLang信息: {sglang_info['url']}")
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新节点SGLang信息失败: {e}")
    
    async def monitor_node_sglang(self, node_id: int) -> Dict[str, Any]:
        """监控单个节点的SGLang服务"""
        try:
            # 获取节点信息
            result = await self.db.execute(
                select(MinerUNode).where(MinerUNode.id == node_id)
            )
            node = result.scalar_one_or_none()
            
            if not node:
                return {"success": False, "error": f"节点 {node_id} 不存在"}
            
            if not node.is_enabled:
                return {"success": False, "error": f"节点 {node_id} 已禁用"}
            
            # 确保SGLang信息已设置
            if not node.sglang_url:
                await self.update_node_sglang_info(node)
                await self.db.refresh(node)
            
            # 先检查基本状态，再检查健康状态
            status_result = await self.get_sglang_status_via_mineru_api(node.base_url)
            health_result = None

            if status_result["success"] and status_result["running"]:
                # 如果基本状态显示运行中，再进行健康检查
                health_result = await self.check_sglang_health_via_mineru_api(node.base_url)
            else:
                # 如果基本状态就显示未运行，直接标记为离线
                health_result = {
                    "healthy": False,
                    "error": status_result.get("error", "SGLang服务未运行"),
                    "response_time": None
                }

            # 更新节点SGLang状态
            old_status = node.sglang_status
            if health_result["healthy"]:
                node.sglang_status = SglangStatus.ONLINE
                # 如果健康，重置连续失败次数和重启周期
                if (node.sglang_consecutive_failures or 0) > 0:
                    logger.info(f"节点 {node.id} SGLang恢复正常，重置失败计数和重启周期")
                node.sglang_consecutive_failures = 0

                # 重置重启周期
                if (node.sglang_current_cycle_restarts or 0) > 0 or node.sglang_alert_sent:
                    node.sglang_current_cycle_restarts = 0
                    node.sglang_restart_cycle_start = None
                    node.sglang_alert_sent = False
                    node.sglang_alert_sent_at = None
            else:
                # 根据错误类型设置不同状态
                if "连接失败" in str(health_result.get("error", "")):
                    node.sglang_status = SglangStatus.OFFLINE
                else:
                    node.sglang_status = SglangStatus.ERROR
                # 安全处理空值
                node.sglang_consecutive_failures = (node.sglang_consecutive_failures or 0) + 1
            
            node.sglang_last_check = datetime.now()
            
            await self.db.commit()
            
            # 记录状态变化
            if old_status != node.sglang_status:
                logger.info(f"节点 {node.id} SGLang状态变化: {old_status} -> {node.sglang_status}")
            
            return {
                "success": True,
                "node_id": node_id,
                "node_name": node.name,
                "node_url": node.base_url,
                "sglang_status": node.sglang_status,
                "sglang_url": node.sglang_url,
                "healthy": health_result["healthy"],
                "consecutive_failures": node.sglang_consecutive_failures,
                "response_time": health_result.get("response_time"),
                "error": health_result.get("error"),
                "status_changed": old_status != node.sglang_status,
                "old_status": old_status,
                "check_method": "mineru_api",
                "basic_status": status_result.get("status_data") if 'status_result' in locals() else None
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"监控节点 {node_id} SGLang服务失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def monitor_all_nodes_sglang(self) -> Dict[str, Any]:
        """监控所有节点的SGLang服务"""
        try:
            # 获取所有启用的节点，但排除Pipeline模式的节点（Pipeline模式不需要SGLang服务）
            result = await self.db.execute(
                select(MinerUNode).where(
                    and_(
                        MinerUNode.is_enabled == True,
                        MinerUNode.status != NodeStatus.OFFLINE,  # 只监控在线或繁忙的节点
                        MinerUNode.parse_mode != ParseMode.PIPELINE  # 排除Pipeline模式节点
                    )
                )
            )
            nodes = result.scalars().all()
            
            if not nodes:
                return {"success": True, "message": "没有需要监控的节点", "results": []}
            
            logger.info(f"开始监控 {len(nodes)} 个节点的SGLang服务")
            
            # 并发监控所有节点
            tasks = []
            for node in nodes:
                task = self.monitor_node_sglang(node.id)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            success_count = 0
            error_count = 0
            healthy_count = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"监控节点 {nodes[i].id} 时发生异常: {result}")
                    error_count += 1
                elif result.get("success"):
                    success_count += 1
                    if result.get("healthy"):
                        healthy_count += 1
                else:
                    error_count += 1
            
            logger.info(f"SGLang监控完成: 成功 {success_count}, 错误 {error_count}, 健康 {healthy_count}")
            
            return {
                "success": True,
                "total_nodes": len(nodes),
                "success_count": success_count,
                "error_count": error_count,
                "healthy_count": healthy_count,
                "results": [r for r in results if not isinstance(r, Exception)]
            }
            
        except Exception as e:
            logger.error(f"监控所有节点SGLang服务失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_nodes_need_restart(self) -> List[MinerUNode]:
        """获取需要重启的节点"""
        try:
            # 查询连续失败次数超过阈值且最近没有重启过的节点，但排除Pipeline模式的节点
            threshold_time = datetime.now() - timedelta(minutes=10)  # 10分钟内重启过的不再重启

            result = await self.db.execute(
                select(MinerUNode).where(
                    and_(
                        MinerUNode.is_enabled == True,
                        # 安全处理NULL值：COALESCE(sglang_consecutive_failures, 0) >= 3
                        func.coalesce(MinerUNode.sglang_consecutive_failures, 0) >= 3,
                        MinerUNode.sglang_status == SglangStatus.OFFLINE,
                        MinerUNode.parse_mode != ParseMode.PIPELINE,  # 排除Pipeline模式节点
                        # 最近10分钟内没有重启过
                        (MinerUNode.sglang_last_restart.is_(None)) |
                        (MinerUNode.sglang_last_restart < threshold_time)
                    )
                )
            )
            
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"获取需要重启的节点失败: {e}")
            return []
