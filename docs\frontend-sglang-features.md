# 前端SGLang监控功能说明

## 🎯 功能概述

前端已集成SGLang服务监控功能，用户可以通过Web界面：

- 📊 查看所有节点的SGLang服务状态
- 🔍 单独检查节点的SGLang服务
- 🔄 重启单个或批量SGLang服务
- 📈 查看SGLang服务的失败次数和重启历史

## 🖥️ 界面功能

### 1. 节点列表新增SGLang状态列

在MinerU节点管理页面的表格中新增了"SGLang状态"列，显示：

- **状态标签**：在线/离线/错误/未知/重启中
- **失败次数**：连续失败次数（红色显示）
- **重启次数**：总重启次数（橙色显示）

### 2. 顶部操作按钮

新增两个批量操作按钮：

- **SGLang监控**：批量检查所有节点的SGLang服务状态
- **重启失败SGLang**：批量重启所有失败的SGLang服务

### 3. 节点操作菜单

在每个节点的操作下拉菜单中新增：

- **检查SGLang**：单独检查该节点的SGLang服务状态
- **重启SGLang**：重启该节点的SGLang服务

## 🎨 状态显示

### 状态标签颜色

| 状态 | 颜色 | 说明 |
|------|------|------|
| 在线 | 绿色 | SGLang服务正常运行 |
| 离线 | 红色 | SGLang服务无法访问 |
| 错误 | 红色 | SGLang服务出现错误 |
| 重启中 | 橙色 | SGLang服务正在重启 |
| 未知 | 灰色 | 尚未检测SGLang状态 |

### 附加信息

- **失败次数**：显示连续失败的次数，超过0时以红色文字显示
- **重启次数**：显示总重启次数，超过0时以橙色文字显示

## 🔧 操作流程

### 批量监控SGLang服务

1. 点击页面顶部的"SGLang监控"按钮
2. 系统会并发检查所有启用节点的SGLang服务
3. 显示检查结果：总节点数、健康节点数
4. 自动刷新节点列表显示最新状态

### 批量重启失败服务

1. 点击页面顶部的"重启失败SGLang"按钮
2. 系统确认对话框，点击确认
3. 系统会自动重启所有失败的SGLang服务
4. 显示重启结果：重启节点数、成功数量
5. 自动刷新节点列表

### 单节点操作

1. 点击节点操作列的"操作"按钮
2. 选择"检查SGLang"或"重启SGLang"
3. 系统执行操作并显示结果
4. 自动刷新该节点的状态

## 📱 响应式设计

### 表格列宽适配

- SGLang状态列宽度：110px
- 在小屏幕设备上会自动调整显示
- 支持横向滚动查看完整信息

### 状态信息布局

- 主状态标签显示在上方
- 失败次数和重启次数显示在下方
- 使用小字体避免占用过多空间

## 🔄 自动刷新

### 刷新机制

- 页面支持自动刷新功能（30秒间隔）
- 执行SGLang操作后会自动刷新数据
- 保持SGLang状态信息的实时性

### 加载状态

- 批量操作时显示加载动画
- 单节点操作时按钮显示加载状态
- 防止重复点击和操作冲突

## 🎛️ 用户体验优化

### 操作确认

- 重启操作需要用户确认
- 显示具体的节点名称和操作内容
- 支持取消操作

### 状态反馈

- 操作成功/失败都有明确的消息提示
- 显示具体的错误信息帮助排查问题
- 批量操作显示统计结果

### 视觉反馈

- 不同状态使用不同颜色区分
- 重要信息（失败次数）使用醒目颜色
- 操作按钮在不可用时自动禁用

## 🔧 技术实现

### 类型定义

```typescript
export type SglangStatus = 'online' | 'offline' | 'error' | 'unknown' | 'restarting'

interface MinerUNode {
  // ... 其他字段
  sglang_status: SglangStatus
  sglang_port: number | null
  sglang_url: string | null
  sglang_last_check: string | null
  sglang_consecutive_failures: number
  sglang_last_restart: string | null
  sglang_restart_count: number
  // 前端临时状态
  sglang_checking?: boolean
  sglang_restarting?: boolean
}
```

### API接口

```typescript
// 获取SGLang状态
getSglangStatus(nodeId: number): Promise<any>

// 重启SGLang服务
restartSglang(nodeId: number): Promise<any>

// 批量监控
monitorAllSglang(): Promise<any>

// 批量重启
restartFailedSglang(): Promise<any>
```

### 状态管理

- 使用Vue 3 Composition API
- 响应式状态管理
- 异步操作错误处理
- 加载状态控制

## 🐛 错误处理

### 网络错误

- API请求失败时显示友好错误信息
- 自动重试机制（部分操作）
- 超时处理

### 状态异常

- 处理后端返回的错误状态
- 显示具体的错误原因
- 提供操作建议

### 用户操作错误

- 防止重复操作
- 操作冲突检测
- 状态一致性保证

## 📋 使用建议

### 日常监控

1. 定期点击"SGLang监控"检查所有节点状态
2. 关注失败次数较高的节点
3. 及时处理离线或错误状态的节点

### 故障处理

1. 发现SGLang服务异常时，先尝试"检查SGLang"
2. 如果确认服务异常，使用"重启SGLang"
3. 多次重启失败时，检查节点服务器状态

### 批量操作

1. 系统维护时可使用批量重启功能
2. 批量操作前确认影响范围
3. 关注操作结果统计信息

---

**注意**：SGLang监控功能需要后端支持相应的API接口，请确保后端服务已更新到最新版本。
