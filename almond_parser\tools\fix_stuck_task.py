#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
修复特定的卡住任务
"""
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from sqlalchemy import select


async def fix_stuck_task():
    """修复卡住的任务"""
    print("🔧 修复卡住的任务...")
    
    async with get_async_session() as db:
        # 查找特定的卡住任务
        result = await db.execute(
            select(Document).where(
                Document.document_id == "b2cccaf012e3412aa62255c35840fe93"
            )
        )
        
        task = result.scalar_one_or_none()
        
        if not task:
            print("❌ 未找到指定的任务")
            return
        
        print(f"📋 找到任务: {task.document_id}")
        print(f"   文件名: {task.file_name}")
        print(f"   当前状态: {task.status.value}")
        print(f"   任务ID: {task.task_id}")
        print(f"   重试次数: {task.retry_count}/{task.max_retries}")
        print(f"   更新时间: {task.updated_at}")
        
        # 清理任务
        print("🔧 清理任务...")
        task.task_id = None
        task.status = DocumentStatus.RETRY_PENDING
        task.updated_at = datetime.now()
        
        await db.commit()
        
        print("✅ 任务已清理，现在可以重新分配")
        
        # 验证清理结果
        result2 = await db.execute(
            select(Document).where(
                Document.document_id == "b2cccaf012e3412aa62255c35840fe93"
            )
        )
        
        updated_task = result2.scalar_one_or_none()
        print(f"📋 验证结果:")
        print(f"   状态: {updated_task.status.value}")
        print(f"   任务ID: {updated_task.task_id}")
        print(f"   更新时间: {updated_task.updated_at}")


if __name__ == "__main__":
    asyncio.run(fix_stuck_task())
