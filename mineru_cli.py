#!/usr/bin/env python3
"""
MinerU API CLI 主入口
"""
import sys
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MinerU API 命令行工具")
    subparsers = parser.add_subparsers(dest="module", help="可用模块")
    
    # sglang 模块
    sglang_parser = subparsers.add_parser("sglang", help="SGLang 服务管理")
    sglang_subparsers = sglang_parser.add_subparsers(dest="sglang_command", help="SGLang 命令")
    
    # sglang 子命令
    sglang_subparsers.add_parser("status", help="显示服务状态")
    
    logs_parser = sglang_subparsers.add_parser("logs", help="查看服务日志")
    logs_parser.add_argument("-f", "--follow", action="store_true", help="跟踪日志")
    logs_parser.add_argument("-n", "--lines", type=int, default=50, help="显示行数")
    
    start_parser = sglang_subparsers.add_parser("start", help="启动服务")
    start_parser.add_argument("--force", action="store_true", help="强制重启")
    
    stop_parser = sglang_subparsers.add_parser("stop", help="停止服务")
    stop_parser.add_argument("--force", action="store_true", help="强制停止")
    
    sglang_subparsers.add_parser("restart", help="重启服务")
    sglang_subparsers.add_parser("health", help="健康检查")
    
    args = parser.parse_args()
    
    if not args.module:
        parser.print_help()
        return
    
    if args.module == "sglang":
        if not args.sglang_command:
            sglang_parser.print_help()
            return
        
        # 导入并执行 sglang CLI
        from mineru_api.cli.sglang_cli import SglangCLI
        import asyncio
        
        cli = SglangCLI()
        
        try:
            if args.sglang_command == "status":
                asyncio.run(cli.status())
            elif args.sglang_command == "logs":
                asyncio.run(cli.logs(follow=args.follow, lines=args.lines))
            elif args.sglang_command == "start":
                asyncio.run(cli.start(force_restart=args.force))
            elif args.sglang_command == "stop":
                asyncio.run(cli.stop(force_kill=args.force))
            elif args.sglang_command == "restart":
                asyncio.run(cli.restart())
            elif args.sglang_command == "health":
                asyncio.run(cli.health())
        except KeyboardInterrupt:
            print("\n👋 操作已取消")
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()
