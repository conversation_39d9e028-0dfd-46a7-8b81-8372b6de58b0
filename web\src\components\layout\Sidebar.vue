<template>
  <div class="sidebar">
    <!-- Logo section - only show when expanded -->
    <div class="logo-section" v-if="!isCollapsed">
      <div class="logo-content">
        <img src="@/assets/logo.svg" alt="Logo" class="logo-icon" />
        <h1 class="title">杏仁解析</h1>
      </div>
      <el-button class="collapse-btn" icon="Fold" @click="toggleCollapse" />
    </div>

    <!-- Menu section -->
    <div class="menu-section">
      <el-menu :default-active="activeMenu" :collapse="isCollapsed" :unique-opened="true" :collapse-transition="false"
        :router="false" class="sidebar-menu" background-color="#263445" text-color="#aab8c7" active-text-color="#409EFF"
        @select="handleSelect">
        <!-- Logo item - only show when collapsed -->
        <el-menu-item v-if="isCollapsed" class="logo-menu-item" index="__toggle__" @click="toggleCollapse">
          <el-icon>
            <img src="@/assets/logo.svg" alt="Logo" class="logo-icon-small" />
          </el-icon>
          <template #title>
            <span>杏仁解析</span>
          </template>
        </el-menu-item>

        <template v-for="item in menuItems" :key="item.path">
          <!-- 有子菜单的项目 -->
          <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.fullPath">
            <template #title>
              <el-icon class="menu-icon">
                <Monitor v-if="item.icon === 'Monitor'" />
                <DocumentIcon v-else-if="item.icon === 'DocumentIcon'" />
                <TrendCharts v-else-if="item.icon === 'TrendCharts'" />
                <Tools v-else-if="item.icon === 'Setting'" />
                <Key v-else-if="item.icon === 'Key'" />
                <Connection v-else-if="item.icon === 'Connection'" />
                <List v-else-if="item.icon === 'List'" />
                <Files v-else-if="item.icon === 'Files'" />
              </el-icon>
              <span>{{ item.title }}</span>
            </template>
            <el-menu-item
              v-for="child in item.children"
              :key="child.fullPath"
              :index="child.fullPath"
            >
              <el-icon class="menu-icon">
                <View v-if="child.icon === 'View'" />
                <Operation v-else-if="child.icon === 'Operation'" />
                <Warning v-else-if="child.icon === 'Warning'" />
                <Files v-else-if="child.icon === 'Files'" />
                <List v-else />
              </el-icon>
              <template #title>{{ child.title }}</template>
            </el-menu-item>
          </el-sub-menu>

          <!-- 没有子菜单的项目 -->
          <el-menu-item v-else :index="item.fullPath">
            <el-icon class="menu-icon">
              <Monitor v-if="item.icon === 'Monitor'" />
              <DocumentIcon v-else-if="item.icon === 'DocumentIcon'" />
              <TrendCharts v-else-if="item.icon === 'TrendCharts'" />
              <Tools v-else-if="item.icon === 'Setting'" />
              <Key v-else-if="item.icon === 'Key'" />
              <Connection v-else-if="item.icon === 'Connection'" />
              <List v-else-if="item.icon === 'List'" />
              <Files v-else-if="item.icon === 'Files'" />
            </el-icon>
            <template #title>{{ item.title }}</template>
          </el-menu-item>
        </template>
      </el-menu>
    </div>

    <!-- Logout section -->
    <div class="logout-section">
      <el-menu :collapse="isCollapsed" background-color="#263445" text-color="#aab8c7" class="logout-menu">
        <el-menu-item @click="handleLogout">
          <el-icon>
            <SwitchButton />
          </el-icon>
          <template #title>登出</template>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, shallowRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Monitor,
  Document as DocumentIcon,
  TrendCharts,
  Setting,
  Key,
  Connection,
  List,
  SwitchButton,
  Tools,
  Files,
  Warning,
  View,
  Operation
} from '@element-plus/icons-vue'

const props = defineProps<{
  isCollapsed: boolean
}>()

const emit = defineEmits<{
  'update:isCollapsed': [value: boolean]
}>()

const route = useRoute()
const router = useRouter()

// 图标名称映射，用于字符串匹配
const iconNameMap: Record<string, string> = {
  Overview: 'Monitor',
  DocumentList: 'DocumentIcon',
  OCRDocuments: 'DocumentIcon',
  DOCDocuments: 'Files',
  Statistics: 'TrendCharts',
  Settings: 'Setting',
  ApiKeys: 'Key',
  MinerUNodes: 'Connection',
  ParseTasks: 'List',
  Logs: 'Files',
  SystemLogs: 'View',
  ParsingLogs: 'Operation',
  ErrorLogs: 'Warning',
  AccessLogs: 'Files'
}

const mainRoute = computed(() =>
  router.options.routes.find((r) => r.path === '/dashboard')
)

// 使用shallowRef缓存菜单项，避免深度响应式开销
const menuItems = computed(() => {
  if (!mainRoute.value || !mainRoute.value.children) return []

  const items = []
  const logItems = []

  // 分离日志相关路由和其他路由
  mainRoute.value.children
    .filter(child => child.path !== '') // 过滤掉重定向路由
    .forEach(child => {
      const path = child.path
      const fullPath = `/dashboard/${path}`

      if (path.startsWith('logs/')) {
        // 日志相关路由
        logItems.push({
          title: child.meta?.title || child.name,
          fullPath,
          path: child.path,
          icon: iconNameMap[child.name as string] || null
        })
      } else {
        // 其他路由
        items.push({
          title: child.meta?.title || child.name,
          fullPath,
          path: child.path,
          icon: iconNameMap[child.name as string] || null,
          children: null
        })
      }
    })

  // 如果有日志路由，创建日志菜单组
  if (logItems.length > 0) {
    items.push({
      title: '日志管理',
      fullPath: '/dashboard/logs',
      path: 'logs',
      icon: 'Files',
      children: logItems
    })
  }

  return items
})

const activeMenu = computed(() => {
  return route.path
})

const handleSelect = (index: string) => {
  // 忽略 toggle
  if (index === '__toggle__') return
  router.push(index)
}

const toggleCollapse = () => {
  emit('update:isCollapsed', !props.isCollapsed)
}

const handleLogout = () => {
  localStorage.removeItem('token')
  router.push('/login')
}
</script>

<style scoped>
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #263445;
}

.logo-section {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo-icon-small {
  width: 20px;
  height: 20px;
}

.collapsed-logo-section {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-menu-item {
  height: 64px !important;
  line-height: 64px !important;
  padding: 0 20px !important;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  margin: 0;
  white-space: nowrap;
}

.collapse-btn {
  padding: 6px;
  border: none;
  background: transparent;
  color: #aab8c7;
  cursor: pointer;
  transition: color 0.15s ease-out;
}

.collapse-btn:hover {
  color: #fff;
}

.menu-section {
  flex: 1;
  overflow: hidden;
}

.sidebar-menu {
  border-right: none;
  user-select: none;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  padding: 0 16px !important;
}

.sidebar-menu :deep(.el-menu-item .el-icon) {
  width: 24px;
  text-align: center;
  font-size: 18px;
  margin-right: 12px;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background-color: #1890ff !important;
  color: #ffffff !important;
}

.sidebar-menu :deep(.el-menu-item.is-active .el-icon) {
  color: #ffffff !important;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #33425b !important;
}

.logout-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-menu {
  border: none;
}

.logout-menu :deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  padding: 0 16px !important;
}

.logout-menu :deep(.el-menu-item:hover) {
  background-color: #33425b !important;
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-menu--collapse) {
  width: 64px;
}

:deep(.el-scrollbar) {
  height: 100%;
}

/* 优化菜单项的hover效果 */
.sidebar-menu :deep(.el-menu-item) {
  transition: background-color 0.15s ease-out;
}

.logout-menu :deep(.el-menu-item) {
  transition: background-color 0.15s ease-out;
}

/* 图标性能优化 */
.menu-icon {
  will-change: auto;
  transform: translateZ(0);
}

/* 减少重绘 */
.sidebar-menu :deep(.el-menu-item .el-icon) {
  backface-visibility: hidden;
}
</style>
