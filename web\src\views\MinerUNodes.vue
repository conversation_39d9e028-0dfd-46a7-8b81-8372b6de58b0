<template>
  <div class="mineru-nodes">
    <div class="header">
      <h2>MinerU 节点管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加节点
        </el-button>
        <el-button @click="triggerHealthCheck" :loading="healthCheckLoading">
          <el-icon><Check /></el-icon>
          健康检查
        </el-button>
        <el-button type="success" @click="monitorAllSglang" :loading="sglangMonitorLoading">
          <el-icon><Search /></el-icon>
          SGLang监控
        </el-button>
        <el-button type="warning" @click="restartFailedSglang" :loading="sglangRestartLoading">
          <el-icon><Refresh /></el-icon>
          重启失败SGLang
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total_nodes }}</div>
              <div class="stat-label">总节点数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card online">
            <div class="stat-content">
              <div class="stat-number">{{ stats.online_nodes }}</div>
              <div class="stat-label">在线节点</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total_tasks }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ stats.success_rate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 过滤器 -->
    <div class="filters">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select v-model="filters.status" placeholder="节点状态" clearable>
            <el-option label="在线" value="online" />
            <el-option label="离线" value="offline" />
            <el-option label="繁忙" value="busy" />
            <el-option label="错误" value="error" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filters.parse_mode" placeholder="解析模式" clearable>
            <el-option label="Pipeline" value="pipeline" />
            <el-option label="SGLang" value="sglang" />
            <el-option label="自动" value="auto" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filters.service_type" placeholder="服务类型" clearable>
            <el-option label="文档解析" value="document" />
            <el-option label="知识库解析" value="knowledge_base" />
            <el-option label="通用节点" value="universal" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filters.is_enabled" placeholder="启用状态" clearable>
            <el-option label="已启用" :value="true" />
            <el-option label="已禁用" :value="false" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <div class="refresh-controls">
            <el-button @click="loadData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              :loading="loading"
              @change="handleAutoRefreshChange"
            />
            <span v-if="autoRefresh" class="countdown-text">
              {{ countdown }}秒后刷新
            </span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 节点列表 -->
    <el-table :data="nodes" v-loading="loading" stripe>
      <el-table-column prop="id" label="节点ID" width="80" />
      <el-table-column prop="name" label="节点名称" min-width="120" />
      <el-table-column prop="base_url" label="节点地址" min-width="150" show-overflow-tooltip />
      <el-table-column prop="parse_mode" label="解析模式" width="100">
        <template #default="{ row }">
          <el-tag :type="parseModeTagType(row.parse_mode)">
            {{ parseModeText(row.parse_mode) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="service_type" label="服务类型" width="120">
        <template #default="{ row }">
          <el-tag :type="serviceTypeTagType(row.service_type)">
            {{ serviceTypeText(row.service_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_enabled" label="启用状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.is_enabled ? 'success' : 'danger'">
            {{ row.is_enabled ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="节点状态" width="90">
        <template #default="{ row }">
          <el-tag :type="statusTagType(row.status)">
            {{ statusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sglang_status" label="SGLang状态" width="110">
        <template #default="{ row }">
          <div class="sglang-status-cell">
            <el-tooltip
              :content="`健康检查失败${(row.sglang_consecutive_failures || 0)}次，重启${(row.sglang_restart_count || 0)}次`"
              placement="top"
              :disabled="(row.sglang_consecutive_failures || 0) === 0 && (row.sglang_restart_count || 0) === 0"
            >
              <div class="sglang-status-wrapper">
                <el-tag :type="sglangStatusTagType(row.sglang_status)" size="small">
                  {{ sglangStatusText(row.sglang_status) }}
                </el-tag>
                <el-icon
                  v-if="(row.sglang_consecutive_failures || 0) > 0 || (row.sglang_restart_count || 0) > 0"
                  class="status-info-icon"
                  size="12"
                >
                  <InfoFilled />
                </el-icon>
              </div>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="系统资源" width="220">
        <template #default="{ row }">
          <NodeSystemSummary
            :node-id="row.id"
            :auto-refresh="true"
            :is-enabled="row.is_enabled"
          />
        </template>
      </el-table-column>
      <el-table-column prop="current_tasks" label="当前任务" width="80" align="center" />
      <el-table-column prop="max_concurrent_tasks" label="最大任务" width="80" align="center" />
      <el-table-column prop="priority" label="优先级" width="70" align="center" />
      <el-table-column prop="success_tasks" label="成功任务" width="80" align="center" />
      <el-table-column prop="failed_tasks" label="失败任务" width="80" align="center" />
      <el-table-column prop="last_health_check" label="最后检查" width="160" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatTime(row.last_health_check) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="90" align="center">
        <template #default="{ row }">
          <el-dropdown trigger="click">
            <el-button type="primary" size="small">
              <el-icon><More /></el-icon>
              操作
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="editNode(row)">
                  <el-icon color="#409EFF"><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item @click="checkSglangStatus(row)" :disabled="row.sglang_checking">
                  <el-icon color="#67C23A"><Check /></el-icon>
                  检查SGLang
                </el-dropdown-item>
                <el-dropdown-item @click="restartSglang(row)" :disabled="row.sglang_restarting || row.sglang_status === 'RESTARTING' || row.sglang_status === 'restarting'">
                  <el-icon color="#E6A23C"><Refresh /></el-icon>
                  重启SGLang
                </el-dropdown-item>
                <el-dropdown-item @click="showSystemInfo(row)">
                  <el-icon color="#909399"><Setting /></el-icon>
                  系统信息
                </el-dropdown-item>
                <el-dropdown-item @click="toggleNodeStatus(row)" :divided="true">
                  <el-icon :color="row.is_enabled ? '#909399' : '#67C23A'">
                    <component :is="row.is_enabled ? 'Close' : 'Check'" />
                  </el-icon>
                  {{ row.is_enabled ? '禁用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item @click="deleteNode(row)" :divided="true">
                  <el-icon color="#F56C6C"><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑节点对话框 -->
    <el-dialog
      :title="editingNode ? '编辑节点' : '添加节点'"
      v-model="showAddDialog"
      width="600px"
    >
      <el-form :model="nodeForm" :rules="nodeRules" ref="nodeFormRef" label-width="120px">
        <el-form-item label="节点名称" prop="name">
          <el-input v-model="nodeForm.name" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="nodeForm.host" placeholder="例如: *************" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="nodeForm.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="解析模式" prop="parse_mode">
          <el-select v-model="nodeForm.parse_mode" placeholder="请选择解析模式">
            <el-option label="Pipeline" value="pipeline" />
            <el-option label="SGLang" value="sglang" />
            <el-option label="自动" value="auto" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务类型" prop="service_type">
          <el-select v-model="nodeForm.service_type" placeholder="请选择服务类型">
            <el-option label="文档解析" value="document" />
            <el-option label="知识库解析" value="knowledge_base" />
            <el-option label="通用节点" value="universal" />
          </el-select>
        </el-form-item>
        <el-form-item label="最大并发任务" prop="max_concurrent_tasks">
          <el-input-number v-model="nodeForm.max_concurrent_tasks" :min="1" :max="20" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="nodeForm.priority" :min="0" :max="10" />
        </el-form-item>
        <el-form-item label="健康检查间隔" prop="health_check_interval">
          <el-input-number v-model="nodeForm.health_check_interval" :min="10" :max="3600" />
          <span style="margin-left: 10px; color: #999;">秒</span>
        </el-form-item>
        <el-form-item label="认证令牌" prop="auth_token">
          <el-input v-model="nodeForm.auth_token" placeholder="可选，节点认证令牌" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="nodeForm.description"
            type="textarea"
            :rows="3"
            placeholder="节点描述信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveNode" :loading="saving">
          {{ editingNode ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 系统信息对话框 -->
    <el-dialog
      v-model="showSystemInfoDialog"
      :title="`${selectedNode?.name || ''} - 系统信息`"
      width="90%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <NodeSystemInfo
        v-if="selectedNode"
        :node-id="selectedNode.id"
        :node-name="selectedNode.name"
        :is-enabled="selectedNode.is_enabled"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, More, Edit, Check, Search, Delete, Close, Refresh, Setting, InfoFilled } from '@element-plus/icons-vue'
import NodeSystemInfo from '@/components/NodeSystemInfo.vue'
import NodeSystemSummary from '@/components/NodeSystemSummary.vue'
import { mineruNodeApi } from '@/api/mineruNode'
import type { MinerUNode, MinerUNodeStats, MinerUNodeCreate, MinerUNodeUpdate, ServiceType } from '@/types/mineruNode'

// 响应式数据
const loading = ref(false)
const healthCheckLoading = ref(false)
const sglangMonitorLoading = ref(false)
const sglangRestartLoading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const showSystemInfoDialog = ref(false)
const editingNode = ref<MinerUNode | null>(null)
const selectedNode = ref<MinerUNode | null>(null)
const nodeFormRef = ref()

const nodes = ref<MinerUNode[]>([])
const stats = ref<MinerUNodeStats>({
  total_nodes: 0,
  online_nodes: 0,
  offline_nodes: 0,
  busy_nodes: 0,
  error_nodes: 0,
  total_tasks: 0,
  success_tasks: 0,
  failed_tasks: 0,
  running_tasks: 0,
  success_rate: 0
})

const filters = reactive({
  status: '',
  parse_mode: '',
  service_type: '',
  is_enabled: undefined as boolean | undefined
})

const nodeForm = reactive<MinerUNodeCreate>({
  name: '',
  host: '',
  port: 8000,
  parse_mode: 'auto',
  service_type: 'universal',
  max_concurrent_tasks: 3,
  priority: 1,
  health_check_interval: 60,
  auth_token: '',
  description: ''
})

const nodeRules = {
  name: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
  host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  parse_mode: [{ required: true, message: '请选择解析模式', trigger: 'change' }],
  service_type: [{ required: true, message: '请选择服务类型', trigger: 'change' }]
}

const autoRefresh = ref(false)
const countdown = ref(30)
let refreshTimer: number | null = null
let countdownTimer: number | null = null

// 方法
const loadNodes = async () => {
  loading.value = true
  try {
    const response = await mineruNodeApi.getNodes({
      status: filters.status || undefined,
      parse_mode: filters.parse_mode || undefined,
      service_type: filters.service_type || undefined,
      is_enabled: filters.is_enabled
    })
    nodes.value = response
  } catch (error) {
    ElMessage.error('加载节点列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await mineruNodeApi.getStats()
    stats.value = response
  } catch (error) {
    console.error('加载统计信息失败:', error)
    // 发生错误时重置统计数据为0
    stats.value = {
      total_nodes: 0,
      online_nodes: 0,
      offline_nodes: 0,
      busy_nodes: 0,
      error_nodes: 0,
      total_tasks: 0,
      success_tasks: 0,
      failed_tasks: 0,
      running_tasks: 0,
      success_rate: 0
    }
  }
}

const loadData = async () => {
  try {
    await Promise.all([
      loadNodes(),
      // loadStats()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

const editNode = (node: MinerUNode) => {
  editingNode.value = node
  Object.assign(nodeForm, {
    name: node.name,
    host: node.host,
    port: node.port,
    parse_mode: node.parse_mode,
    service_type: node.service_type,
    max_concurrent_tasks: node.max_concurrent_tasks,
    priority: node.priority,
    health_check_interval: node.health_check_interval,
    auth_token: node.auth_token || '',
    description: node.description || ''
  })
  showAddDialog.value = true
}

const saveNode = async () => {
  if (!nodeFormRef.value) return
  
  try {
    await nodeFormRef.value.validate()
    saving.value = true
    
    if (editingNode.value) {
      await mineruNodeApi.updateNode(editingNode.value.id, nodeForm as MinerUNodeUpdate)
      ElMessage.success('节点更新成功')
    } else {
      await mineruNodeApi.createNode(nodeForm)
      ElMessage.success('节点添加成功')
    }
    
    showAddDialog.value = false
    resetForm()
    await loadData()
  } catch (error) {
    ElMessage.error(editingNode.value ? '节点更新失败' : '节点添加失败')
  } finally {
    saving.value = false
  }
}

const deleteNode = async (node: MinerUNode) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除节点 "${node.name}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    await mineruNodeApi.deleteNode(node.id)
    ElMessage.success('节点删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('节点删除失败')
    }
  }
}

const checkNodeHealth = async (nodeId: number) => {
  const node = nodes.value.find(n => n.id === nodeId)
  if (!node) return
  
  node.checking = true
  try {
    const result = await mineruNodeApi.triggerSingleHealthCheck(nodeId)
    ElMessage.success(`健康检查完成: ${result.is_healthy ? '健康' : '不健康'}`)
    await loadData()
  } catch (error) {
    ElMessage.error('健康检查失败')
  } finally {
    node.checking = false
  }
}

const autoDetectMode = async (nodeId: number) => {
  const node = nodes.value.find(n => n.id === nodeId)
  if (!node) return
  
  node.detecting = true
  try {
    const result = await mineruNodeApi.autoDetectParseMode(nodeId)
    if (result.updated) {
      ElMessage.success(`检测到解析模式: ${result.detected_mode}`)
      await loadData()
    } else {
      ElMessage.warning('未能检测到解析模式')
    }
  } catch (error) {
    ElMessage.error('解析模式检测失败')
  } finally {
    node.detecting = false
  }
}

const toggleNodeStatus = async (node: MinerUNode) => {
  try {
    await mineruNodeApi.toggleNodeStatus(node.id, !node.is_enabled)
    ElMessage.success(`节点${node.is_enabled ? '禁用' : '启用'}成功`)
    await loadData()
  } catch (error) {
    ElMessage.error(`节点${node.is_enabled ? '禁用' : '启用'}失败`)
  }
}

const triggerHealthCheck = async () => {
  healthCheckLoading.value = true
  try {
    await mineruNodeApi.triggerHealthCheck()
    ElMessage.success('健康检查已启动')
    setTimeout(() => {
      loadData()
    }, 2000)
  } catch (error) {
    ElMessage.error('触发健康检查失败')
  } finally {
    healthCheckLoading.value = false
  }
}

// SGLang相关方法
const checkSglangStatus = async (node: MinerUNode) => {
  node.sglang_checking = true
  try {
    const result = await mineruNodeApi.getSglangStatus(node.id)
    if (result.success) {
      ElMessage.success(`SGLang状态: ${result.healthy ? '健康' : '异常'}`)
      await loadData()
    } else {
      ElMessage.error(`SGLang检查失败: ${result.error}`)
    }
  } catch (error) {
    ElMessage.error('SGLang状态检查失败')
  } finally {
    node.sglang_checking = false
  }
}

const restartSglang = async (node: MinerUNode) => {
  try {
    await ElMessageBox.confirm(
      `确定要重启节点 "${node.name}" 的SGLang服务吗？`,
      '确认重启',
      { type: 'warning' }
    )

    node.sglang_restarting = true
    try {
      const result = await mineruNodeApi.restartSglang(node.id)
      if (result.success) {
        ElMessage.success('SGLang服务重启成功')
        await loadData()
      } else {
        ElMessage.error(`SGLang重启失败: ${result.error}`)
      }
    } catch (error) {
      ElMessage.error('SGLang服务重启失败')
    } finally {
      node.sglang_restarting = false
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('SGLang服务重启失败')
    }
  }
}

const monitorAllSglang = async () => {
  sglangMonitorLoading.value = true
  try {
    const result = await mineruNodeApi.monitorAllSglang()
    if (result.success) {
      ElMessage.success(`SGLang监控完成: 检查${result.total_nodes}个节点，健康${result.healthy_count}个`)
      await loadData()
    } else {
      ElMessage.error(`SGLang监控失败: ${result.error}`)
    }
  } catch (error) {
    ElMessage.error('SGLang批量监控失败')
  } finally {
    sglangMonitorLoading.value = false
  }
}

const restartFailedSglang = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重启所有失败的SGLang服务吗？',
      '确认批量重启',
      { type: 'warning' }
    )

    sglangRestartLoading.value = true
    try {
      const result = await mineruNodeApi.restartFailedSglang()
      if (result.success) {
        if (result.restart_count > 0) {
          ElMessage.success(`批量重启完成: 重启${result.restart_count}个节点，成功${result.success_count}个`)
        } else {
          ElMessage.info('没有需要重启的SGLang服务')
        }
        await loadData()
      } else {
        ElMessage.error(`批量重启失败: ${result.error}`)
      }
    } catch (error) {
      ElMessage.error('SGLang批量重启失败')
    } finally {
      sglangRestartLoading.value = false
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('SGLang批量重启失败')
    }
  }
}

const resetForm = () => {
  editingNode.value = null
  Object.assign(nodeForm, {
    name: '',
    host: '',
    port: 8000,
    parse_mode: 'auto',
    service_type: 'universal',
    max_concurrent_tasks: 3,
    priority: 1,
    health_check_interval: 60,
    auth_token: '',
    description: ''
  })
}

const startRefreshTimer = () => {
  if (refreshTimer) return
  
  // 设置刷新定时器
  refreshTimer = window.setInterval(() => {
    loadData() // 同时刷新节点列表和统计数据
    countdown.value = 30 // 重置倒计时
  }, 30000)

  // 设置倒计时定时器
  countdownTimer = window.setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    }
  }, 1000)
}

const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  countdown.value = 30
}

const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startRefreshTimer()
    loadData() // 立即刷新一次
  } else {
    stopRefreshTimer()
  }
}

// 显示系统信息
const showSystemInfo = (node: MinerUNode) => {
  selectedNode.value = node
  showSystemInfoDialog.value = true
}

// 辅助方法
const statusTagType = (status: string) => {
  const types: Record<string, string> = {
    online: 'success',
    offline: 'info',
    busy: 'warning',
    error: 'danger'
  }
  return types[status] || 'info'
}

const statusText = (status: string) => {
  const texts: Record<string, string> = {
    online: '在线',
    offline: '离线',
    busy: '繁忙',
    error: '错误'
  }
  return texts[status] || status
}

const parseModeTagType = (mode: string) => {
  const types: Record<string, string> = {
    pipeline: 'primary',
    sglang: 'success',
    auto: 'warning'
  }
  return types[mode] || 'info'
}

const parseModeText = (mode: string) => {
  const texts: Record<string, string> = {
    pipeline: 'Pipeline',
    sglang: 'SGLang',
    auto: '自动'
  }
  return texts[mode] || mode
}

const serviceTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    document: 'primary',
    knowledge_base: 'success',
    universal: 'warning'
  }
  return types[type] || 'info'
}

const serviceTypeText = (type: string) => {
  const texts: Record<string, string> = {
    document: '文档解析',
    knowledge_base: '知识库解析',
    universal: '通用节点'
  }
  return texts[type] || type
}

const formatTime = (time: string | null) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

// SGLang状态相关方法
const sglangStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    ONLINE: 'success',
    OFFLINE: 'danger',
    ERROR: 'danger',
    UNKNOWN: 'info',
    RESTARTING: 'warning',
    // 兼容小写
    online: 'success',
    offline: 'danger',
    error: 'danger',
    unknown: 'info',
    restarting: 'warning'
  }
  return types[status] || 'info'
}

const sglangStatusText = (status: string) => {
  const texts: Record<string, string> = {
    ONLINE: '在线',
    OFFLINE: '离线',
    ERROR: '错误',
    UNKNOWN: '未知',
    RESTARTING: '重启中',
    // 兼容小写
    online: '在线',
    offline: '离线',
    error: '错误',
    unknown: '未知',
    restarting: '重启中'
  }
  return texts[status] || status
}

// 监听过滤器变化
watch(filters, () => {
  // 避免在loading期间重复调用
  if (!loading.value) {
    loadData() // 同时刷新节点列表和统计数据
  }
}, { deep: true })

// 组件挂载时加载数据
onMounted(() => {
  loadData() // 初始加载数据
})

// 在组件卸载时清理定时器
onUnmounted(() => {
  stopRefreshTimer()
})
</script>

<style scoped>
.mineru-nodes {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-card.online {
  border-left: 4px solid #67c23a;
}

.stat-card.success {
  border-left: 4px solid #409eff;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.filters {
  margin-bottom: 20px;
}

.operation-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
}

.el-dropdown-menu__item .el-icon {
  margin-right: 4px;
}

.el-dropdown {
  margin: 0;
}

.el-table {
  width: 100%;
  margin-bottom: 20px;
}

.el-table :deep(th) {
  background-color: var(--el-fill-color-light) !important;
}

.el-table :deep(.el-table__cell) {
  padding: 8px 0;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.countdown-text {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.el-switch {
  margin-right: 8px;
}

/* SGLang状态相关样式 */
.sglang-status-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sglang-status-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-info-icon {
  color: var(--el-color-info);
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.status-info-icon:hover {
  opacity: 1;
  color: var(--el-color-primary);
}

.sglang-info {
  font-size: 11px;
  line-height: 1.2;
}

.sglang-info .el-text {
  font-size: 11px;
}
</style>
