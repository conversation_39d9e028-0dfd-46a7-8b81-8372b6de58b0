#!/usr/bin/env python3
"""
数据库问题快速修复脚本
用于解决 "没有这个db文件" 的问题
"""

import sys
import sqlite3
from pathlib import Path
from loguru import logger

# 确保能导入项目模块
sys.path.insert(0, str(Path(__file__).parent))

def fix_database_issues():
    """修复数据库问题"""
    logger.info("🔧 开始修复数据库问题...")
    
    try:
        # 导入配置
        from mineru_api.config import BASE_DIR, ENABLE_AUTH, get_auth_config
        
        # 1. 创建必要的目录
        logger.info("📁 创建必要的目录...")
        directories = [
            BASE_DIR / "data",
            BASE_DIR / "logs",
            BASE_DIR / "output", 
            BASE_DIR / "temp"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ 目录: {directory}")
        
        # 2. 修复任务历史数据库
        logger.info("🗄️ 修复任务历史数据库...")
        from mineru_api.services.history_service import TaskHistoryService
        
        history_service = TaskHistoryService()
        logger.info(f"✅ 任务历史数据库: {history_service.db_path}")
        
        # 验证数据库表
        with sqlite3.connect(history_service.db_path) as conn:
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='task_history'")
            if cursor.fetchone():
                logger.info("✅ task_history 表存在")
            else:
                logger.warning("⚠️ task_history 表不存在，重新创建...")
                history_service.init_database()
        
        # 3. 修复认证数据库
        if ENABLE_AUTH:
            logger.info("🔐 修复认证数据库...")
            
            auth_config = get_auth_config()
            
            if auth_config.backend_type.value == "sqlite":
                from mineru_api.auth.backends.sqlite_backend import SQLiteAuthBackend
                
                # 直接创建后端实例会自动初始化数据库
                backend = SQLiteAuthBackend(auth_config)
                
                db_path = Path(auth_config.sqlite_path)
                logger.info(f"✅ 认证数据库: {db_path}")
                
                # 验证数据库表
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'")
                    if cursor.fetchone():
                        logger.info("✅ api_keys 表存在")
                    else:
                        logger.warning("⚠️ api_keys 表不存在，重新创建...")
                        backend._init_database()
            
            elif auth_config.backend_type.value == "file":
                # 文件后端，检查 JSON 文件
                file_path = Path(auth_config.file_path)
                if not file_path.exists():
                    logger.info(f"📄 创建认证文件: {file_path}")
                    file_path.write_text("[]")
                logger.info(f"✅ 认证文件: {file_path}")
        else:
            logger.info("🔓 认证功能已禁用")
        
        # 4. 测试数据库连接
        logger.info("🧪 测试数据库连接...")
        
        # 测试任务历史数据库
        with sqlite3.connect(history_service.db_path) as conn:
            conn.execute("SELECT 1").fetchone()
        logger.info("✅ 任务历史数据库连接正常")
        
        # 测试认证数据库
        if ENABLE_AUTH and auth_config.backend_type.value == "sqlite":
            with sqlite3.connect(Path(auth_config.sqlite_path)) as conn:
                conn.execute("SELECT 1").fetchone()
            logger.info("✅ 认证数据库连接正常")
        
        logger.success("🎉 数据库问题修复完成！")
        print("\n✅ 修复成功！现在可以正常启动服务了")
        print("启动命令: python main.py 或 python start_server.py")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复失败: {e}")
        print(f"\n❌ 修复失败: {e}")
        print("\n🔍 请检查以下问题：")
        print("1. 是否有文件写入权限")
        print("2. 磁盘空间是否充足")
        print("3. Python 环境是否正确")
        return False


def main():
    """主函数"""
    print("🔧 MineruAPI 数据库问题修复工具")
    print("=" * 50)
    
    if fix_database_issues():
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
