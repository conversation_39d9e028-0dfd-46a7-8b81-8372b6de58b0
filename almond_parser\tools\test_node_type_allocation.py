#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试按节点类型分配任务的功能
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from sqlalchemy import select, func
from almond_parser.db.database import init_database, get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus, ParseMode, ServiceType
from almond_parser.services.task_allocation_service import TaskAllocationService


async def test_node_type_capacity_query():
    """测试节点类型容量查询"""
    print("🔍 测试节点类型容量查询...")
    
    async with get_async_session() as db:
        try:
            task_service = TaskAllocationService()
            capacities = await task_service._get_node_type_capacities(db)
            
            print(f"📊 发现 {len(capacities)} 种节点类型:")
            for cap in capacities:
                print(f"  - {cap.type_key}:")
                print(f"    总容量: {cap.total_capacity}")
                print(f"    当前负载: {cap.current_load}")
                print(f"    可用容量: {cap.available_capacity}")
                print(f"    节点数量: {cap.node_count}")
                print(f"    优先级: {cap.priority}")
                print()
                
            return len(capacities) > 0
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False


async def test_task_query_for_node_type():
    """测试为特定节点类型查询任务"""
    print("🔍 测试为特定节点类型查询任务...")
    
    async with get_async_session() as db:
        try:
            task_service = TaskAllocationService()
            
            # 获取节点类型
            capacities = await task_service._get_node_type_capacities(db)
            if not capacities:
                print("⚠️ 没有可用的节点类型")
                return False
            
            # 测试每种节点类型的任务查询
            for cap in capacities:
                print(f"📝 测试节点类型 {cap.type_key} 的任务查询:")
                
                tasks = await task_service._get_tasks_for_node_type(db, cap, 5)
                print(f"  找到 {len(tasks)} 个适合的任务")
                
                for task in tasks[:3]:  # 只显示前3个
                    print(f"    - {task.document_id}: {task.status.value}, 解析模式: {task.current_parse_mode}")
                
                print()
                
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False


async def test_node_type_allocation():
    """测试按节点类型分配任务"""
    print("🚀 测试按节点类型分配任务...")
    
    try:
        task_service = TaskAllocationService()
        
        # 执行分配
        stats = await task_service.allocate_pending_tasks_by_node_type(max_allocations=5)
        
        print("📊 分配结果:")
        print(f"  成功分配: {stats.get('allocated', 0)}")
        print(f"  分配失败: {stats.get('failed', 0)}")
        print(f"  等待任务: {stats.get('total_pending', 0)}")
        print(f"  分配方法: {stats.get('allocation_method', 'unknown')}")
        
        # 显示详细的节点类型分配信息
        details = stats.get('node_type_details', {})
        if details:
            print("\n📋 各节点类型分配详情:")
            for node_type, detail in details.items():
                print(f"  {node_type}:")
                print(f"    分配成功: {detail.get('allocated', 0)}")
                print(f"    分配失败: {detail.get('failed', 0)}")
                print(f"    可用容量: {detail.get('available_capacity', 0)}")
                print(f"    适合任务: {detail.get('suitable_tasks', 0)}")
        
        return stats.get('allocated', 0) > 0 or stats.get('total_pending', 0) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def compare_allocation_methods():
    """比较新旧分配方法的结果"""
    print("🔄 比较新旧分配方法...")
    
    try:
        task_service = TaskAllocationService()
        
        # 测试旧方法
        print("📊 旧方法 (总容量分配):")
        old_stats = await task_service.allocate_pending_tasks(max_allocations=5)
        print(f"  分配结果: {old_stats}")
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 测试新方法
        print("\n📊 新方法 (按节点类型分配):")
        new_stats = await task_service.allocate_pending_tasks_by_node_type(max_allocations=5)
        print(f"  分配结果: {new_stats}")
        
        # 比较结果
        print("\n🔍 方法比较:")
        print(f"  旧方法分配: {old_stats.get('allocated', 0)}")
        print(f"  新方法分配: {new_stats.get('allocated', 0)}")
        print(f"  旧方法失败: {old_stats.get('failed', 0)}")
        print(f"  新方法失败: {new_stats.get('failed', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 比较测试失败: {e}")
        return False


async def show_current_system_status():
    """显示当前系统状态"""
    print("📊 当前系统状态:")
    
    async with get_async_session() as db:
        try:
            # 节点状态
            node_result = await db.execute(
                select(
                    MinerUNode.parse_mode,
                    MinerUNode.service_type,
                    MinerUNode.status,
                    func.count(MinerUNode.id).label("count"),
                    func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                    func.sum(MinerUNode.current_tasks).label("current_load")
                ).where(
                    MinerUNode.is_enabled == True
                ).group_by(
                    MinerUNode.parse_mode,
                    MinerUNode.service_type,
                    MinerUNode.status
                )
            )
            
            print("🖥️ 节点状态:")
            for row in node_result:
                print(f"  {row.parse_mode.value}_{row.service_type.value} ({row.status.value}): "
                      f"{row.count}个节点, 容量={row.current_load}/{row.total_capacity}")
            
            # 任务状态
            task_result = await db.execute(
                select(
                    Document.status,
                    func.count(Document.id).label("count")
                ).group_by(Document.status)
            )
            
            print("\n📝 任务状态:")
            for row in task_result:
                print(f"  {row.status.value}: {row.count}个")
                
        except Exception as e:
            print(f"❌ 获取系统状态失败: {e}")


async def main():
    """主测试函数"""
    print("🧪 开始测试按节点类型分配任务功能\n")
    
    # 初始化数据库
    await init_database()
    
    # 显示系统状态
    await show_current_system_status()
    print("\n" + "="*60 + "\n")
    
    # 运行测试
    tests = [
        ("节点类型容量查询", test_node_type_capacity_query),
        ("节点类型任务查询", test_task_query_for_node_type),
        ("按节点类型分配", test_node_type_allocation),
        ("分配方法比较", compare_allocation_methods),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🧪 {test_name}:")
        try:
            result = await test_func()
            results.append((test_name, result))
            print(f"{'✅' if result else '❌'} {test_name} {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
        
        print("\n" + "-"*40 + "\n")
    
    # 总结
    print("📋 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        print(f"  {'✅' if result else '❌'} {test_name}")
    
    print(f"\n🎯 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！按节点类型分配功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查配置和实现。")


if __name__ == "__main__":
    asyncio.run(main())
