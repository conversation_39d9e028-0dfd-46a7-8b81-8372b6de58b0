#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
添加SGLang重启周期相关字段
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger
from sqlalchemy import text

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.db.database import get_async_session


async def add_sglang_restart_cycle_fields():
    """添加SGLang重启周期相关字段"""
    
    # SQL语句列表
    sql_statements = [
        # 添加当前重启周期的重启次数
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_current_cycle_restarts INT 
        DEFAULT 0 
        COMMENT '当前重启周期的重启次数'
        """,
        
        # 添加重启周期开始时间
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_restart_cycle_start DATETIME 
        COMMENT '重启周期开始时间'
        """,
        
        # 添加是否已发送告警标记
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_alert_sent BOOLEAN 
        DEFAULT FALSE 
        COMMENT '是否已发送告警'
        """,
        
        # 添加告警发送时间
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_alert_sent_at DATETIME 
        COMMENT '告警发送时间'
        """
    ]
    
    try:
        async with get_async_session() as db:
            logger.info("开始添加SGLang重启周期字段...")
            
            for i, sql in enumerate(sql_statements, 1):
                try:
                    logger.info(f"执行SQL语句 {i}/{len(sql_statements)}")
                    await db.execute(text(sql.strip()))
                    await db.commit()
                    logger.success(f"✅ SQL语句 {i} 执行成功")
                except Exception as e:
                    if "Duplicate column name" in str(e):
                        logger.warning(f"⚠️ 字段已存在，跳过: {e}")
                    else:
                        logger.error(f"❌ SQL语句 {i} 执行失败: {e}")
                        raise
            
            logger.success("🎉 SGLang重启周期字段添加完成")
            
    except Exception as e:
        logger.error(f"添加SGLang重启周期字段失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        await add_sglang_restart_cycle_fields()
        logger.success("数据库迁移完成")
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
