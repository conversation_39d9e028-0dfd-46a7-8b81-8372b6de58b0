# -*- encoding: utf-8 -*-
"""
DOC文档管理 API
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from fastapi.responses import JSONResponse
from loguru import logger

from almond_parser.schemas.doc_document import (
    DOCDocumentQueryParams,
    DOCDocumentListResponse,
    DOCDocumentResponse,
    DOCDocumentRetryRequest,
    DOCDocumentLogsResponse,
    ServerConfigResponse, KBType, ParseType
)
from almond_parser.schemas.base import BaseResponse
from almond_parser.services.doc_document_service import DOCDocumentService
from almond_parser.utils.auth import get_current_api_key, get_current_user
from almond_parser.db import ApiKey

router = APIRouter(prefix="/doc-document", tags=["DOC文档管理"])


@router.get("/servers", response_model=List[ServerConfigResponse])
async def get_server_list(
    current_user: ApiKey = Depends(get_current_user)
):
    """
    获取可用服务器列表
    """
    try:
        servers = await DOCDocumentService.get_server_list()
        return servers

    except Exception as e:
        logger.error(f"获取服务器列表失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取服务器列表失败"
        )


@router.get("/documents", response_model=DOCDocumentListResponse)
async def query_doc_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小"),
    server: str = Query(None, description="服务器名称"),
    source_type: str = Query("document", description="数据源类型: document(多文档) 或 knowledge(知识库)"),
    username: str = Query(None, description="用户名"),
    file_name: str = Query(None, description="文件名"),
    status: str = Query(None, description="状态"),
    kb_type: KBType = Query(None, description="知识库类型"),
    parse_type: ParseType = Query(None, description="解析类型"),
    batch_id: str = Query(None, description="批次ID"),
    document_id: str = Query(None, description="文档ID"),
    sort_by: str = Query(None, description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    current_user: ApiKey = Depends(get_current_user)
):
    """
    查询DOC文档列表
    """
    try:
        # 构建查询参数
        params = DOCDocumentQueryParams(
            page=page,
            page_size=page_size,
            server=server,
            source_type=source_type,
            username=username,
            file_name=file_name,
            status=status,
            kb_type=kb_type,
            parse_type=parse_type,
            batch_id=batch_id,
            document_id=document_id,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # 查询文档列表
        documents, pagination = await DOCDocumentService.query_documents(params)

        return DOCDocumentListResponse(
            items=[DOCDocumentResponse(**doc) for doc in documents],
            pagination=pagination
        )

    except Exception as e:
        logger.error(f"查询DOC文档列表失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询DOC文档列表失败"
        )


@router.get("/documents/{document_id}", response_model=DOCDocumentResponse)
async def get_doc_document_detail(
    document_id: str,
    server: str = Query(..., description="服务器名称"),
    current_user: ApiKey = Depends(get_current_user)
):
    """
    获取DOC文档详情
    """
    try:
        document = await DOCDocumentService.get_document_detail(server, document_id)

        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        return DOCDocumentResponse(**document)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取DOC文档详情失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取DOC文档详情失败"
        )


@router.get("/documents/{document_id}/logs", response_model=DOCDocumentLogsResponse)
async def get_doc_document_logs(
    document_id: str,
    server: str = Query(..., description="服务器名称"),
    current_user: ApiKey = Depends(get_current_user)
):
    """
    获取DOC文档日志
    """
    try:
        logs = await DOCDocumentService.get_document_logs(server, document_id)

        return DOCDocumentLogsResponse(logs=logs)

    except Exception as e:
        logger.error(f"获取DOC文档日志失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取DOC文档日志失败"
        )


@router.post("/documents/{document_id}/retry", response_model=BaseResponse)
async def retry_doc_document(
    document_id: str,
    request: DOCDocumentRetryRequest,
    current_user: ApiKey = Depends(get_current_user)
):
    """
    重试DOC文档处理
    """
    try:
        success = await DOCDocumentService.retry_document(request.server, document_id)

        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="重试失败"
            )

        return BaseResponse(
            success=True,
            message="重试任务已提交"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试DOC文档处理失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重试DOC文档处理失败"
        )


@router.get("/documents/{document_id}/download")
async def download_doc_original_file(
    document_id: str,
    server: str = Query(..., description="服务器名称"),
    current_user: ApiKey = Depends(get_current_user)
):
    """
    下载DOC文档原始文件
    """
    try:
        # 获取文档信息
        document_detail = await DOCDocumentService.get_document_detail(server, document_id)
        if not document_detail:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 获取服务器配置
        from almond_parser.db.doc_database_manager import doc_db_manager
        if not doc_db_manager._initialized:
            await doc_db_manager.initialize()

        server_configs = doc_db_manager.get_server_list()
        server_config = next((s for s in server_configs if s['name'] == server), None)

        if not server_config or not server_config.get('download'):
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="服务器下载配置不存在"
            )

        download_config = server_config['download']

        # 构建下载URL
        if document_detail.get('extra_info', {}).get('meta_info'):
            # 多文档：从meta_info获取temp_file_path
            meta_info = document_detail['extra_info']['meta_info']
            if isinstance(meta_info, str):
                import json
                meta_info = json.loads(meta_info)

            temp_file_path = meta_info.get('temp_file_path', document_detail['file_name'])
            actual_filename = temp_file_path.split('/')[-1] if '/' in temp_file_path else temp_file_path
            download_url = f"{download_config['base_url']}{download_config['docs_prefix']}{actual_filename}"
        else:
            # 知识库：从file_path构建
            file_path = document_detail.get('file_path', '')
            if file_path.startswith(download_config['kb_path_prefix']):
                relative_path = file_path[len(download_config['kb_path_prefix']):]
            else:
                relative_path = document_detail['file_name']
            download_url = f"{download_config['base_url']}{download_config['kb_prefix']}{relative_path}"

        logger.info(f"代理下载URL: {download_url}")

        # 通过httpx代理下载
        import httpx
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(download_url)
            response.raise_for_status()

            # 返回文件响应，使用原始文件名
            from fastapi.responses import Response
            return Response(
                content=response.content,
                media_type="application/octet-stream",
                headers={
                    "Content-Disposition": f'attachment; filename="{document_detail["file_name"]}"'
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载DOC文档原始文件失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="下载DOC文档原始文件失败"
        )


@router.post("/reload-config", response_model=BaseResponse)
async def reload_doc_databases_config(
    current_user: ApiKey = Depends(get_current_user)
):
    """
    重新加载DOC数据库配置
    """
    try:
        from almond_parser.db.doc_database_manager import doc_db_manager
        await doc_db_manager.reload_config()

        return BaseResponse(
            success=True,
            message="DOC数据库配置重新加载成功"
        )

    except Exception as e:
        logger.error(f"重新加载DOC数据库配置失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重新加载DOC数据库配置失败"
        )
