# ARQ 任务数据库会话修复

## 问题描述

在 ARQ Worker 执行定时任务时出现 `__aenter__` 错误：

```
2025-06-26 10:54:03 | ERROR | almond_parser.tasks.node_tasks:health_check_node:82 | 节点 1 健康检查失败: __aenter__
```

## 问题原因

1. **异步上下文管理器使用错误**：
   - `get_db()` 函数返回的是 `AsyncGenerator[AsyncSession, None]`
   - 它是为 FastAPI 依赖注入设计的，不是异步上下文管理器
   - 在 ARQ 任务中使用 `async with get_db() as db:` 会导致协议不匹配

2. **设计差异**：
   - FastAPI 依赖注入使用异步生成器
   - ARQ 任务需要直接的异步上下文管理器

## 修复方案

### 修改前（错误）

```python
from almond_parser.db import get_db

async def health_check_node(ctx, node_id):
    try:
        async with get_db() as db:  # ❌ 错误：get_db() 不是上下文管理器
            # 数据库操作
            pass
```

### 修改后（正确）

```python
from almond_parser.db import db_manager

async def health_check_node(ctx, node_id):
    try:
        async with db_manager.session_factory() as db:  # ✅ 正确：直接使用会话工厂
            # 数据库操作
            pass
```

## 修复的文件

1. **almond_parser/tasks/node_tasks.py**
   - `health_check_node()` 函数
   - `health_check_all_nodes()` 函数

2. **almond_parser/tasks/document_tasks.py**
   - `process_document()` 函数
   - `retry_document()` 函数
   - `process_batch_documents()` 函数
   - `query_document_status()` 函数

## 验证修复

### 1. 测试数据库会话

```bash
python almond_parser/test_task_fix.py
```

### 2. 重新启动 Worker

```bash
# 停止当前 Worker (Ctrl+C)
# 重新启动
python almond_parser/worker.py
```

### 3. 观察日志

修复后应该看到：

```
2025-06-26 11:00:00 | INFO | almond_parser.tasks.node_tasks:health_check_all_nodes:96 | 开始检查所有节点健康状态
2025-06-26 11:00:00 | INFO | almond_parser.tasks.node_tasks:health_check_node:30 | 开始检查节点 1 健康状态
2025-06-26 11:00:00 | INFO | almond_parser.tasks.node_tasks:health_check_node:70 | 节点 1 健康检查完成: 健康
```

## 技术说明

### FastAPI 依赖注入 vs ARQ 任务

| 场景 | 使用方式 | 原因 |
|------|----------|------|
| FastAPI 路由 | `async def route(db: AsyncSession = Depends(get_db))` | 依赖注入系统自动管理 |
| ARQ 任务 | `async with db_manager.session_factory() as db:` | 手动管理会话生命周期 |

### 异步生成器 vs 异步上下文管理器

```python
# 异步生成器（FastAPI 依赖）
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with session_factory() as session:
        yield session

# 异步上下文管理器（ARQ 任务）
async with session_factory() as session:
    # 直接使用会话
    pass
```

## 最佳实践

1. **FastAPI 路由**：使用 `Depends(get_db)` 依赖注入
2. **ARQ 任务**：使用 `db_manager.session_factory()` 直接创建会话
3. **服务类**：接受 `AsyncSession` 参数，由调用方管理会话
4. **工具函数**：根据使用场景选择合适的会话获取方式

## 预防措施

1. **代码审查**：确保 ARQ 任务中不使用 `get_db()`
2. **类型提示**：明确函数参数类型，避免混淆
3. **测试覆盖**：为 ARQ 任务编写单元测试
4. **文档说明**：在代码中注释会话管理方式的选择原因
