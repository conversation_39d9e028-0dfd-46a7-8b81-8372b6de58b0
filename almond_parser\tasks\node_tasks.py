# -*- encoding: utf-8 -*-
"""
节点管理任务
"""
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, Any, List
from urllib.parse import urljoin
from loguru import logger

from almond_parser.db import db_manager
from almond_parser.db.models import MinerUNode, NodeHealthCheck, NodeStatus


async def health_check_node(
    ctx: Dict[str, Any],
    node_id: int
) -> Dict[str, Any]:
    """
    检查单个节点健康状态

    Args:
        ctx: ARQ 上下文
        node_id: 节点ID

    Returns:
        检查结果
    """
    logger.info(f"开始检查节点 {node_id} 健康状态")

    try:
        async with db_manager.session_factory() as db:
            # 查询节点信息
            from sqlalchemy import select
            result = await db.execute(
                select(MinerUNode).where(MinerUNode.id == node_id)
            )
            node = result.scalar_one_or_none()

            if not node:
                error_msg = f"节点不存在: {node_id}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # 执行健康检查
            health_result = await _perform_health_check(node)

            # 更新节点状态
            node.status = NodeStatus.ONLINE if health_result["is_healthy"] else NodeStatus.OFFLINE
            node.last_health_check = datetime.now()

            if health_result["is_healthy"]:
                node.consecutive_failures = 0
            else:
                node.consecutive_failures += 1

            # 保存健康检查记录
            health_check = NodeHealthCheck(
                node_id=node_id,
                is_healthy=health_result["is_healthy"],
                response_time=health_result.get("response_time"),
                error_message=health_result.get("error_message"),
                node_status=node.status.value,
                current_tasks=health_result.get("current_tasks", 0),
                server_type=health_result.get("server_type"),
                sglang_available=health_result.get("sglang_available"),
                detected_parse_mode=health_result.get("parse_mode"),
                raw_response=health_result.get("raw_response")
            )
            db.add(health_check)
            await db.commit()

            logger.info(f"节点 {node_id} 健康检查完成: {'健康' if health_result['is_healthy'] else '异常'}")

            return {
                "success": True,
                "node_id": node_id,
                "is_healthy": health_result["is_healthy"],
                "response_time": health_result.get("response_time"),
                "status": node.status.value
            }

    except Exception as e:
        error_msg = f"节点 {node_id} 健康检查失败: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": str(e)}


async def health_check_all_nodes(ctx: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查所有启用节点的健康状态

    Args:
        ctx: ARQ 上下文

    Returns:
        检查结果汇总
    """
    logger.info("开始检查所有节点健康状态")

    try:
        async with db_manager.session_factory() as db:
            # 查询所有启用的节点
            from sqlalchemy import select
            result = await db.execute(
                select(MinerUNode).where(MinerUNode.is_enabled == True)
            )
            nodes = result.scalars().all()

            logger.info(f"查询到 {len(nodes)} 个启用的节点")
            for node in nodes:
                logger.info(f"  节点 {node.id}: {node.name} - {node.base_url}")

            if not nodes:
                logger.warning("没有找到启用的节点")
                return {"success": True, "message": "没有启用的节点", "results": []}

            # 并发检查所有节点
            tasks = []
            for i, node in enumerate(nodes):
                logger.info(f"为节点 {node.id} ({node.name}) 创建健康检查任务 {i+1}")
                task = _check_single_node_health(node)
                tasks.append(task)

            logger.info(f"总共创建了 {len(tasks)} 个健康检查任务")

            # 等待所有检查完成
            logger.info("开始并发执行健康检查任务...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
            logger.info(f"并发健康检查完成，收到 {len(results)} 个结果")

            # 处理结果
            healthy_count = 0
            total_count = len(nodes)
            check_results = []

            for i, result in enumerate(results):
                node = nodes[i]

                if isinstance(result, Exception):
                    logger.error(f"节点 {node.id} {node.name} 检查异常: {result}")
                    check_result = {
                        "node_id": node.id,
                        "node_name": node.name,
                        "is_healthy": False,
                        "error": str(result)
                    }
                else:
                    check_result = result
                    if result.get("is_healthy"):
                        healthy_count += 1

                check_results.append(check_result)

                # 更新数据库中的节点状态
                try:
                    await _update_node_status(db, node, check_result)
                except Exception as e:
                    logger.error(f"更新节点 {node.id} 状态失败: {e}")

            await db.commit()

            logger.info(f"所有节点健康检查完成: {healthy_count}/{total_count} 健康")

            return {
                "success": True,
                "total_nodes": total_count,
                "healthy_nodes": healthy_count,
                "unhealthy_nodes": total_count - healthy_count,
                "results": check_results
            }

    except Exception as e:
        error_msg = f"批量健康检查失败: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": str(e)}


async def _perform_health_check(node: MinerUNode) -> Dict[str, Any]:
    """执行节点健康检查"""
    logger.info(f'节点 {node.id} ({node.base_url}) 开始健康监测请求...')
    logger.debug(f'节点 {node.id} 详细信息: name={node.name}, auth_token={bool(node.auth_token)}')
    start_time = datetime.now()
    try:
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            health_url = urljoin(node.base_url, "/health")
            headers = {}

            # if node.auth_token:
            #     headers["Authorization"] = f"Bearer {node.auth_token}"

            async with session.get(health_url, headers=headers) as response:
                response_time = int((datetime.now() - start_time).total_seconds() * 1000)
                logger.info(f"url: {health_url}")
                if response.status == 200:
                    # LitServer 的 /health 接口只返回文本 "ok"，不是 JSON
                    response_text = await response.text()
                    logger.info(f"{node.base_url} response_text: {response_text}")

                    # 检查是否为 LitServer 的健康检查响应
                    if response_text.strip().lower() == "ok":
                        # 尝试获取更多状态信息（如果有自定义状态接口）
                        additional_info = await _get_additional_node_info(session, node, headers)

                        return {
                            "is_healthy": True,
                            "response_time": response_time,
                            "current_tasks": additional_info.get("current_tasks", 0),
                            "version": additional_info.get("version"),
                            "parse_mode": additional_info.get("parse_mode"),
                            "server_type": "litserver"
                        }
                    else:
                        # 尝试解析为 JSON（兼容其他类型的健康检查接口）
                        try:
                            data = await response.json()
                            return {
                                "is_healthy": True,
                                "response_time": response_time,
                                "current_tasks": data.get("current_tasks", 0),
                                "version": data.get("version"),
                                "parse_mode": data.get("parse_mode"),
                                "server_type": "custom"
                            }
                        except:
                            # 如果既不是 "ok" 也不是有效 JSON，仍然认为是健康的
                            return {
                                "is_healthy": True,
                                "response_time": response_time,
                                "current_tasks": 0,
                                "server_type": "unknown",
                                "raw_response": response_text[:100]  # 记录前100个字符
                            }
                else:
                    return {
                        "is_healthy": False,
                        "response_time": response_time,
                        "error_message": f"HTTP {response.status}"
                    }

    except asyncio.TimeoutError:
        logger.warning(f"节点 {node.id} ({node.base_url}) 健康检查超时")
        return {
            "is_healthy": False,
            "error_message": "请求超时"
        }
    except Exception as e:
        logger.error(f"节点 {node.id} ({node.base_url}) 健康检查异常: {e}")
        return {
            "is_healthy": False,
            "error_message": str(e)
        }


async def _get_additional_node_info(session: aiohttp.ClientSession, node: MinerUNode, headers: Dict[str, str]) -> Dict[str, Any]:
    """尝试获取节点的额外状态信息"""
    additional_info = {}

    # 尝试获取任务状态（如果有相关接口）
    try:
        status_url = urljoin(node.base_url, "/status")
        async with session.get(status_url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
            if response.status == 200:
                try:
                    data = await response.json()
                    additional_info.update({
                        "current_tasks": data.get("current_tasks", 0),
                        "version": data.get("version"),
                        "parse_mode": data.get("parse_mode")
                    })
                except:
                    pass
    except:
        pass

    # 尝试获取 sglang 状态（如果是 mineru-api）
    try:
        sglang_url = urljoin(node.base_url, "/sglang/status")
        async with session.get(sglang_url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
            if response.status == 200:
                try:
                    data = await response.json()
                    if data.get("health_check"):
                        additional_info["sglang_available"] = True
                        additional_info["parse_mode"] = "vlm/sglang"
                    else:
                        additional_info["sglang_available"] = False
                        additional_info["parse_mode"] = "pipeline"
                except:
                    pass
    except:
        pass

    return additional_info


async def _check_single_node_health(node: MinerUNode) -> Dict[str, Any]:
    """检查单个节点健康状态（内部方法）"""
    logger.info(f"开始检查节点 {node.id} ({node.name}) 健康状态: {node.base_url}")

    try:
        health_result = await _perform_health_check(node)
        logger.info(f"节点 {node.id} _perform_health_check 返回结果: {health_result}")
        logger.info(f"节点 {node.id} 健康检查完成: {'健康' if health_result.get('is_healthy') else '异常'}")

        result = {
            "node_id": node.id,
            "node_name": node.name,
            "base_url": node.base_url,
            "is_healthy": health_result["is_healthy"],
            "response_time": health_result.get("response_time"),
            "error_message": health_result.get("error_message"),
            "current_tasks": health_result.get("current_tasks", 0),
            "version": health_result.get("version"),
            "parse_mode": health_result.get("parse_mode")
        }
    except Exception as e:
        logger.error(f"节点 {node.id} 健康检查发生异常: {e}")
        result = {
            "node_id": node.id,
            "node_name": node.name,
            "base_url": node.base_url,
            "is_healthy": False,
            "error_message": f"检查异常: {str(e)}"
        }

    # 添加服务器类型信息
    if "server_type" in health_result:
        result["server_type"] = health_result["server_type"]

    # 添加 sglang 可用性信息
    if "sglang_available" in health_result:
        result["sglang_available"] = health_result["sglang_available"]

    # 添加原始响应信息（用于调试）
    if "raw_response" in health_result:
        result["raw_response"] = health_result["raw_response"]

    return result


async def _update_node_status(db, node: MinerUNode, check_result: Dict[str, Any]):
    """更新节点状态"""
    node.status = NodeStatus.ONLINE if check_result["is_healthy"] else NodeStatus.OFFLINE
    node.last_health_check = datetime.now()

    if check_result["is_healthy"]:
        node.consecutive_failures = 0
        # if "current_tasks" in check_result:
        #     node.current_tasks = check_result["current_tasks"]
    else:
        node.consecutive_failures += 1

    # 保存健康检查记录
    health_check = NodeHealthCheck(
        node_id=node.id,
        is_healthy=check_result["is_healthy"],
        response_time=check_result.get("response_time"),
        error_message=check_result.get("error_message"),
        node_status=node.status.value,
        current_tasks=check_result.get("current_tasks", 0),
        server_type=check_result.get("server_type"),
        sglang_available=check_result.get("sglang_available"),
        detected_parse_mode=check_result.get("parse_mode"),
        raw_response=check_result.get("raw_response")
    )
    db.add(health_check)
