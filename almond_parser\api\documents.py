# -*- encoding: utf-8 -*-
"""
文档管理 API
"""
import os
import json
import uuid
from pathlib import Path
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi import status as http_status
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from almond_parser.config import settings
from almond_parser.db import get_db, Api<PERSON>ey
from almond_parser.schemas.document import (
    DocumentResponse, DocumentQueryParams, DocumentListResponse,
    DocumentRetryRequest, BatchTaskResponse, BatchStatusResponse, DocumentStatus
)
from almond_parser.schemas.base import BaseResponse
from almond_parser.services.document_service import DocumentService
from almond_parser.services.task_allocation_service import task_allocation_service
from almond_parser.utils.auth import get_current_user, get_current_api_key
from almond_parser.tasks.arq_app import arq_manager
from almond_parser.utils.simple_node_manager import simple_node_manager

router = APIRouter(prefix="/document", tags=["文档管理"])

# 支持的文件类型
SUPPORTED_EXTENSIONS = {".pdf", ".doc", ".docx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png"}


def is_supported_file(filename: str) -> bool:
    """检查文件类型是否支持"""
    return Path(filename).suffix.lower() in SUPPORTED_EXTENSIONS


@router.post("/upload", response_model=BatchTaskResponse)
async def upload_documents(
    files: List[UploadFile] = File(..., description="要上传的文件列表"),
    priority: int = Form(1, ge=1, le=10, description="任务优先级"),
    service_type: str = Form("auto", description="服务类型: auto/document/knowledge_base"),
    parse_mode: str = Form("auto", description="解析模式: auto/pipeline/sglang"),
    max_retries: int = Form(2, ge=0, le=5, description="最大重试次数"),
    parse_config: Optional[str] = Form(None, description="解析配置(JSON字符串)"),
    remarks: Optional[str] = Form(None, description="备注信息"),
    extra_info: Optional[str] = Form(None, description="扩展信息(JSON字符串)"),
    db: AsyncSession = Depends(get_db),
    current_user: ApiKey = Depends(get_current_api_key)  # 使用 API Key 认证
):
    """
    批量上传文档并提交解析任务

    - **files**: 要上传的文件列表（支持 .pdf, .doc, .docx, .ppt, .pptx, .jpg, .jpeg, .png）
    - **priority**: 任务优先级 (1-10，数字越大优先级越高)
    - **service_type**: 服务类型 (auto: 自动选择, document: 文档解析, knowledge_base: 知识库解析)
    - **parse_mode**: 解析模式 (auto: 自动选择, pipeline: Pipeline模式, sglang: SGLang模式)
    - **max_retries**: 最大重试次数 (0-5)
    - **parse_config**: 解析配置参数（可选）
    """
    try:
        # 确保 ARQ 已初始化
        await arq_manager.initialize()

        if not files:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="请至少上传一个文件"
            )

        # 检查文件数量限制
        if len(files) > 50:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="单次最多上传50个文件"
            )

        # 生成批次ID
        batch_id = str(uuid.uuid4())
        user_id = str(current_user.user_id)

        logger.info(f"开始批量上传，用户: {user_id}, 批次: {batch_id}, 文件数: {len(files)}")
        logger.info(f'请求参数：{locals()}')
        if parse_mode in ['sglang', 'vlm']:
            parse_mode = 'vlm-sglang-client'

        # 处理扩展信息
        extra_info_dict = None
        if extra_info:
            try:
                extra_info_dict = json.loads(extra_info)
            except json.JSONDecodeError:
                logger.warning(f"扩展信息JSON格式错误: {extra_info}")

        # 确保上传目录存在
        upload_dir = Path(settings.UPLOAD_DIR)
        upload_dir.mkdir(parents=True, exist_ok=True)

        service = DocumentService(db)
        uploaded_files = []
        failed_files = []

        # 处理每个文件
        for file in files:
            try:
                # 检查文件类型
                if not is_supported_file(file.filename):
                    failed_files.append({
                        "filename": file.filename,
                        "error": f"不支持的文件类型: {Path(file.filename).suffix}"
                    })
                    continue

                # 检查文件大小
                content = await file.read()
                if len(content) > settings.MAX_FILE_SIZE:
                    failed_files.append({
                        "filename": file.filename,
                        "error": f"文件大小超过限制: {len(content)} > {settings.MAX_FILE_SIZE}"
                    })
                    continue

                # 生成唯一文件名
                file_ext = Path(file.filename).suffix
                saved_filename = f"{uuid.uuid4()}{file_ext}"
                file_path = upload_dir / saved_filename

                # 保存文件
                with open(file_path, "wb") as f:
                    f.write(content)

                # 创建文档记录
                document = await service.create_document(
                    batch_id=batch_id,
                    user_id=user_id,
                    filename=file.filename,
                    file_size=len(content),
                    file_type=file_ext[1:].lower(),
                    file_path=str(file_path),
                    parse_config=parse_config,
                    parse_mode=parse_mode,
                    service_type=service_type,
                    max_retries=max_retries,
                    remarks=remarks,
                    extra_info=extra_info_dict
                )

                uploaded_files.append({
                    "document_id": document.document_id,
                    "filename": file.filename,
                    "status": "uploaded"
                })

                logger.info(f"文件上传成功: {file.filename} -> {document.document_id}")

            except Exception as e:
                logger.error(f"处理文件失败: {file.filename}, 错误: {e}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })

        # 如果有成功上传的文件，提交批量解析任务
        if uploaded_files:
            job_id = await arq_manager.enqueue_task(
                "process_batch_documents",
                batch_id=batch_id,
                user_id=user_id,
                priority=priority,
                service_type=service_type,
                parse_mode=parse_mode,
                max_retries=max_retries
            )

            logger.info(f"批量解析任务已提交: {batch_id}, 任务ID: {job_id}, 服务类型: {service_type}")

        return BatchTaskResponse(
            success=True,
            message=f"批量上传完成，成功: {len(uploaded_files)}, 失败: {len(failed_files)}",
            batch_id=batch_id,
            task_count=len(uploaded_files),
            uploaded_files=uploaded_files,
            failed_files=failed_files if failed_files else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量上传失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量上传失败: {str(e)}"
        )


@router.get("/documents", response_model=DocumentListResponse)
async def get_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    document_id: Optional[str] = Query(None, description="文档ID"),
    batch_id: Optional[str] = Query(None, description="批次ID"),
    status: Optional[str] = Query(None, description="文档状态"),
    file_name: Optional[str] = Query(None, description="文件名"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    获取文档列表

    支持分页、筛选和排序功能
    """
    try:
        # 处理空字符串参数
        processed_status = status if status and status.strip() else None
        processed_document_id = document_id if document_id and document_id.strip() else None
        processed_batch_id = batch_id if batch_id and batch_id.strip() else None
        processed_file_name = file_name if file_name and file_name.strip() else None

        # 构建查询参数
        query_params = DocumentQueryParams(
            page=page,
            page_size=page_size,
            document_id=processed_document_id,
            batch_id=processed_batch_id,
            status=processed_status,
            file_name=processed_file_name,
            user_id=str(current_user["user_id"]),
            sort_by=sort_by,
            sort_order=sort_order
        )

        service = DocumentService(db)
        result = await service.get_documents(query_params)

        logger.info(f"用户 {current_user['username']} 查询文档列表: {result.pagination.total} 条记录")
        return result

    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档列表失败"
        )


@router.get("/documents/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取单个文档详情"""
    try:
        service = DocumentService(db)
        document = await service.get_document(document_id, str(current_user["user_id"]))

        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        return document

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档详情失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档详情失败"
        )


@router.post("/documents/{document_id}/retry", response_model=BaseResponse)
async def retry_document(
    document_id: str,
    retry_request: DocumentRetryRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """重试文档处理"""
    try:
        service = DocumentService(db)

        # 检查文档是否存在且属于当前用户
        document = await service.get_document(document_id, str(current_user["user_id"]))
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 提交重试任务
        job_id = await arq_manager.enqueue_task(
            "retry_document",
            document_id=document_id,
            user_id=str(current_user["user_id"]),
            force=retry_request.force
        )

        logger.info(f"用户 {current_user['username']} 重试文档: {document_id}, 任务ID: {job_id}")
        return BaseResponse(message=f"文档重试任务已提交，任务ID: {job_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试文档失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"重试文档失败: {str(e)}"
        )


@router.get("/batch/{batch_id}", response_model=BatchStatusResponse)
async def get_batch_status(
    batch_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取批次任务状态"""
    try:
        service = DocumentService(db)
        batch_status = await service.get_batch_status(batch_id, str(current_user["user_id"]))

        if not batch_status:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="批次不存在"
            )

        return batch_status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批次状态失败: {batch_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取批次状态失败"
        )


@router.delete("/documents/{document_id}", response_model=BaseResponse)
async def delete_document(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除文档"""
    try:
        service = DocumentService(db)
        success = await service.delete_document(document_id, str(current_user["user_id"]))

        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        logger.info(f"用户 {current_user['username']} 删除文档: {document_id}")
        return BaseResponse(message="文档删除成功")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"删除文档失败: {str(e)}"
        )


@router.get("/documents/{document_id}/logs")
async def get_document_logs(
    document_id: str,
    limit: int = Query(100, ge=1, le=1000, description="日志条数限制"),
    offset: int = Query(0, ge=0, description="日志偏移量"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取文档处理日志"""
    try:
        service = DocumentService(db)

        # 检查文档权限
        document = await service.get_document(document_id, str(current_user["user_id"]))
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        logs = await service.get_document_logs(document_id, limit, offset, level)
        return {
            "document_id": document_id,
            "logs": logs,
            "has_more": len(logs) == limit
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档日志失败: {document_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文档日志失败"
        )


@router.post("/callback", response_model=BaseResponse)
async def receive_parse_result(
    result_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_api_key)  # mineru-api 回调使用 API Key
):
    """
    接收 mineru-api 的解析结果回调

    - **document_id**: 文档ID
    - **result_data**: 解析结果数据
    """
    task_id = None
    try:
        logger.info(f"接受到回调: {result_data}")
        service = DocumentService(db)

        if "task_id" not in result_data:
            logger.warning(f"回调数据中未包含 task_id: {result_data}")
            return BaseResponse(message="回调数据格式错误，但已处理")

        task_id = result_data["task_id"]

        # 🔑 关键优化：立即释放节点任务槽位
        # MinerU已经完成任务开始回调，无论后续处理如何都要先释放current_task

        # 🔧 增强查询：使用双重查询机制解决task_id不匹配问题
        document = await service.get_document_by_task_id(task_id)

        if document and document.node_id:
            logger.info(f"🚀 立即释放节点槽位: task_id={task_id}, node_id={document.node_id}")

            # 立即释放current_task，让pending任务可以马上被调度
            early_release_success = await simple_node_manager.release_node(
                db=db,
                node_id=document.node_id,
                task_id=task_id,
                success=True,  # 先假设成功，后续会根据实际结果更新统计
                reason="回调开始-立即释放"
            )

            if early_release_success:
                logger.info(f"✅ 节点槽位已立即释放: {task_id}")
                # 立即触发新任务分配，不等待后续处理完成
                # try:
                #     stats = await task_allocation_service.allocate_pending_tasks(max_allocations=5)
                #     logger.info(f"🚀 立即触发任务分配完成: {stats}")
                # except Exception as alloc_error:
                #     logger.error(f"立即触发任务分配失败: {alloc_error}")
            else:
                logger.warning(f"⚠️  节点槽位立即释放失败: {task_id}")
        else:
            logger.warning(f"⚠️  无法立即释放槽位，文档或节点信息不存在: {task_id}")
            logger.warning(f"📋 回调数据详情: {result_data}")

            # 🔧 即使找不到文档，也要记录回调信息，避免丢失重要数据
            logger.info(f"📝 记录未匹配的回调: task_id={task_id}, status={result_data.get('status')}")

        status = result_data.get("status", "unknown")

        logger.info(f"处理任务 {task_id} 的回调，状态: {status}")

        # 检查是否是失败状态
        if status == "failed":
            error_message = result_data.get("error", "未知错误")
            logger.warning(f"任务 {task_id} 解析失败: {error_message}")

            # 更新文档状态为失败

            success = await service.update_document_status_by_task_id(
                task_id,
                DocumentStatus.FAILED,
                error_message=error_message
            )
            logger.info(f"task_id：{task_id} 接收到回调为失败状态，更新文档状态为失败 更新结果为：{success}")
            return BaseResponse(success=False, message="收到回调，但返回结果为空，有错误", code=500)


        # 处理成功状态
        success = await service.update_document_result(task_id, result_data)
        logger.info(f"接收到文档解析结果: {task_id} success为：{success}")
        return BaseResponse(message="解析结果已更新")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理解析结果回调失败: {task_id}, 错误: {e}")
        # 不抛出异常，返回成功响应避免mineru-api重试
        return BaseResponse(success=False, message="处理解析结果失败", code=500)


@router.get("/{document_id}/result")
async def get_parse_result(
    document_id: str,
    content_type: str = Query("json", enum=["json", "file"], description="返回类型"),
    db: AsyncSession = Depends(get_db),
    # current_user: dict = Depends(get_current_user)  # 使用用户认证，支持前端调用
):
    """
    获取文档解析结果
http://localhost:5173/api/v1/document/6da2e8d01af64f758184c66cd47bdd07/result?content_type=json
    - **document_id**: 文档ID
    - **content_type**: 返回类型 (json: JSON格式, file: 文件下载)
    """
    try:
        service = DocumentService(db)

        # 检查文档权限
        document = await service.get_document(document_id,)
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        if document.status == DocumentStatus.FAILED:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="文档解析失败"
            )

        # 检查解析状态
        if document.status != "COMPLETED":
            raise HTTPException(
                status_code=http_status.HTTP_202_ACCEPTED,
                detail=f"文档尚未解析完成，当前状态: {document.status}"
            )

        if not document.result_data:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="解析结果不存在"
            )


        if content_type == "json":
            md_file = Path(document.output_path)
            if md_file.exists():
                markdown_text = md_file.read_text(encoding="utf-8")
            else:
                logger.warning(f"Markdown 文件不存在: {md_file}")
                raise HTTPException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail="解析结果文件不存在"
                )
            return {
                "document_id": document_id,
                "status": document.status,
                "result": {
                    "markdown_text": markdown_text
                },
                "completed_at": document.completed_at
            }
        else:
            # 返回文件下载
            from fastapi.responses import FileResponse

            result_file = document.output_path
            if not result_file or not os.path.exists(result_file):
                raise HTTPException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail="解析结果文件不存在"
                )

            return FileResponse(
                path=result_file,
                media_type="application/octet-stream",
                filename=f"{document.file_name}_result.md"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取解析结果失败: {document_id}, 错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取解析结果失败"
        )


@router.get("/documents/{document_id}/download")
async def download_original_file(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    下载文档的原始文件

    - **document_id**: 文档ID
    """
    try:
        service = DocumentService(db)

        logger.info(f"开始下载文档: {document_id}, 用户: {current_user['user_id']}")

        # 检查文档权限
        document = await service.get_document_by_id(document_id)
        if not document:
            logger.warning(f"文档不存在或无权限: {document_id}")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        logger.info(f"文档信息: file_path={document.file_path}, file_name={document.file_name}")

        # 检查原始文件是否存在
        if not document.file_path:
            logger.error(f"文档 {document_id} 的 file_path 为空")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文件路径不存在"
            )

        if not os.path.exists(document.file_path):
            logger.error(f"文件不存在: {document.file_path}")
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"原始文件不存在: {document.file_path}"
            )

        logger.info(f"开始返回文件: {document.file_path}")

        # 返回文件下载
        return FileResponse(
            path=document.file_path,
            media_type="application/octet-stream",
            filename=document.file_name
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载原始文件失败: {document_id}, 错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="下载原始文件失败"
        )


@router.post("/documents/{document_id}/query-status", response_model=BaseResponse)
async def query_parse_status(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    手动查询文档解析状态（主动向 mineru-api 查询）

    - **document_id**: 文档ID
    """
    try:
        service = DocumentService(db)

        # 检查文档权限
        document = await service.get_document(document_id, str(current_user["user_id"]))
        if not document:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 提交状态查询任务
        job_id = await arq_manager.enqueue_task(
            "query_document_status",
            document_id=document_id,
            user_id=str(current_user["user_id"])
        )

        logger.info(f"用户 {current_user['username']} 查询文档状态: {document_id}, 任务ID: {job_id}")
        return BaseResponse(message=f"状态查询任务已提交，任务ID: {job_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询解析状态失败: {document_id}, 错误: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail=f"查询解析状态失败: {str(e)}"
        )


async def _handle_task_completion_callback(
    db: AsyncSession,
    task_id: str,
    success: bool = True,
    already_released: bool = False
):
    """处理任务完成回调的辅助函数 - 更新统计信息（槽位已在回调开始时释放）"""
    try:
        from sqlalchemy import select
        from almond_parser.db.models.document import Document
        from almond_parser.utils.simple_node_manager import simple_node_manager
        from almond_parser.services.task_allocation_service import task_allocation_service

        logger.info(f"🔄 处理任务完成回调: {task_id}, 成功: {success}, 已释放: {already_released}")

        # 查找文档
        result = await db.execute(
            select(Document).where(Document.task_id == task_id)
        )
        document = result.scalar_one_or_none()

        if document and document.node_id:
            logger.info(f"📋 找到文档: {document.document_id}, 节点: {document.node_id}")

            # 先查询节点当前状态
            from almond_parser.db.models.mineru_node import MinerUNode
            node_result = await db.execute(
                select(MinerUNode).where(MinerUNode.id == document.node_id)
            )
            node = node_result.scalar_one_or_none()

            if node:
                logger.info(f"📊 节点状态: {node.name}, current_tasks={node.current_tasks}, max_tasks={node.max_concurrent_tasks}")

            if not already_released:
                # 如果还没有释放槽位，则释放（兜底逻辑）
                release_success = await simple_node_manager.release_node(
                    db=db,
                    node_id=document.node_id,
                    task_id=task_id,
                    success=success,
                    reason="回调完成-兜底释放"
                )

                if release_success:
                    logger.info(f"✅ 节点槽位已释放（兜底）: {task_id}")

                    # 触发新任务分配
                    try:
                        stats = await task_allocation_service.allocate_pending_tasks(max_allocations=5)
                        logger.info(f"🚀 兜底触发任务分配完成: {stats}")
                    except Exception as alloc_error:
                        logger.error(f"兜底触发任务分配失败: {alloc_error}")
                else:
                    logger.warning(f"⚠️  节点槽位释放失败（兜底）: {task_id}")
            else:
                # 槽位已经释放，只需要更新统计信息
                logger.info(f"📊 槽位已在回调开始时释放，仅更新统计信息: {task_id}")

                # 更新节点统计信息（成功/失败计数）
                if node:
                    if success:
                        node.success_tasks += 1
                    else:
                        node.failed_tasks += 1
                    await db.flush()
                    logger.info(f"📈 已更新节点统计: {node.name}, 成功: {node.success_tasks}, 失败: {node.failed_tasks}")

                # 🔑 修复：设置release_success变量，避免UnboundLocalError
                release_success = True  # 槽位已在回调开始时释放，视为成功

            if release_success:
                logger.info(f"✅ 节点槽位已释放: {task_id}")

                # 立即触发新任务分配
                try:
                    stats = await task_allocation_service.allocate_pending_tasks(max_allocations=5)
                    logger.info(f"🚀 回调触发任务分配完成: {stats}")
                except Exception as alloc_error:
                    logger.error(f"回调触发任务分配失败: {alloc_error}")
            else:
                logger.warning(f"⚠️  节点槽位释放失败: {task_id}")

        else:
            logger.warning(f"⚠️  无法处理任务完成，文档或节点信息不存在: {task_id}")
            if document:
                logger.warning(f"   文档存在但node_id为空: {document.document_id}")
            else:
                logger.warning(f"   未找到task_id对应的文档: {task_id}")

    except Exception as e:
        logger.error(f"❌ 处理任务完成回调失败: {task_id}, 错误: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")


async def _release_node_task_slot(
    db: AsyncSession,
    task_id: str,
    success: bool = True,
    reason: str = "任务完成"
):
    """释放节点任务槽位的辅助函数（已弃用，使用 _handle_task_completion_callback）"""
    try:
        from sqlalchemy import select
        from almond_parser.db.models.document import Document
        from almond_parser.utils.node_concurrency_manager import node_concurrency_manager

        # 查找文档
        result = await db.execute(
            select(Document).where(Document.task_id == task_id)
        )
        document = result.scalar_one_or_none()

        if document and document.node_id:
            await node_concurrency_manager.release_task_slot(
                db=db,
                node_id=document.node_id,
                task_id=task_id,
                success=success,
                reason=reason
            )
        else:
            logger.warning(f"无法释放任务槽位，文档或节点信息不存在: {task_id}")

    except Exception as e:
        logger.error(f"释放节点任务槽位失败: {task_id}, 错误: {e}")
