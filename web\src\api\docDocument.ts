import request from '@/utils/request'
import type {
  DOCQueryParams,
  DOCQueryResult,
  ServerConfig,
  DOCDocument
} from '@/types/docDocument'



/**
 * 获取可用服务器列表
 */
export const getServerList = (): Promise<ServerConfig[]> => {
  return request({
    url: '/doc-document/servers',
    method: 'get'
  })
}



/**
 * 查询DOC文档列表
 */
export const queryDOCDocuments = (params: DOCQueryParams): Promise<DOCQueryResult> => {
  return request({
    url: '/doc-document/documents',
    method: 'get',
    params: {
      ...params,
      source_type: params.source_type || 'document'  // 默认查询多文档
    }
  })
}

/**
 * 获取DOC文档详情
 */
export const getDOCDocumentDetail = (server: string, documentId: string): Promise<DOCDocument> => {
  return request({
    url: `/doc-document/documents/${documentId}`,
    method: 'get',
    params: { server }
  })
}

/**
 * 重试DOC文档处理
 */
export const retryDOCDocument = (server: string, documentId: string): Promise<void> => {
  return request({
    url: `/doc-document/documents/${documentId}/retry`,
    method: 'post',
    data: { server }
  })
}

/**
 * 下载DOC文档原始文件
 */
export const downloadDOCOriginalFile = async (
  server: string,
  documentId: string,
  fileName: string,
  sourceType: string = 'document',
  document?: any
): Promise<void> => {
  try {
    // 获取服务器列表，找到对应服务器的下载配置
    const servers = await getServerList()
    const serverConfig = servers.find(s => s.name === server)

    if (!serverConfig?.download) {
      throw new Error('服务器未配置下载信息')
    }

    const config = serverConfig.download
    let downloadUrl: string

    if (sourceType === 'knowledge' && document?.file_path) {
      // 知识库文件：需要从file_path中截断前缀
      let relativePath = document.file_path
      if (document.file_path.startsWith(config.kb_path_prefix)) {
        relativePath = document.file_path.substring(config.kb_path_prefix.length)
      }
      downloadUrl = `${config.base_url}${config.kb_prefix}${relativePath}`
    } else if (sourceType === 'document' && document?.extra_info?.meta_info) {
      // 多文档：从meta_info中获取temp_file_path
      let tempFilePath = ''
      try {
        const metaInfo = typeof document.extra_info.meta_info === 'string'
          ? JSON.parse(document.extra_info.meta_info)
          : document.extra_info.meta_info
        tempFilePath = metaInfo?.temp_file_path || fileName
      } catch (e) {
        console.warn('解析meta_info失败，使用文件名:', e)
        tempFilePath = fileName
      }

      // 如果temp_file_path是完整路径，只取文件名部分
      const actualFileName = tempFilePath.includes('/')
        ? tempFilePath.split('/').pop()
        : tempFilePath

      downloadUrl = `${config.base_url}${config.docs_prefix}${actualFileName}?download_name=${document.file_name}`
    } else {
      // 默认：直接使用文件名
      downloadUrl = `${config.base_url}${config.docs_prefix}${fileName}`
    }
    console.log('直接下载URL:', downloadUrl)
    window.open(downloadUrl, '_blank')

  }
  catch (error) {
    console.error('获取下载配置失败:', error)
    // 配置获取失败时，所有类型都降级到API下载
  }

}

/**
 * 获取DOC文档日志
 */
export const getDOCDocumentLogs = (
  server: string,
  documentId: string
): Promise<{ logs: string[] }> => {
  return request({
    url: `/doc-document/documents/${documentId}/logs`,
    method: 'get',
    params: { server }
  })
}
