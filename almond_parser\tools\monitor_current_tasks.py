#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
监控 current_tasks 的变化
"""
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.mineru_node import MinerUNode
from almond_parser.db.models.document import Document
from almond_parser.schemas import DocumentStatus
from sqlalchemy import select, func


async def monitor_current_tasks():
    """监控 current_tasks 的变化"""
    print("=" * 60)
    print("监控 current_tasks 变化")
    print("=" * 60)
    
    last_values = {}
    
    while True:
        try:
            async with get_async_session() as db:
                current_time = datetime.now().strftime("%H:%M:%S")
                
                # 查询所有节点状态
                nodes_result = await db.execute(
                    select(MinerUNode).where(MinerUNode.is_enabled == True).order_by(MinerUNode.id)
                )
                nodes = nodes_result.scalars().all()
                
                # 查询正在处理的任务数
                processing_result = await db.execute(
                    select(func.count(Document.id)).where(
                        Document.status.in_([
                            DocumentStatus.PENDING,
                            DocumentStatus.PARSING
                        ]),
                        Document.node_id.isnot(None)
                    )
                )
                actual_processing = processing_result.scalar() or 0
                
                # 检查变化
                changes = []
                for node in nodes:
                    node_key = f"node_{node.id}"
                    current_value = node.current_tasks
                    
                    if node_key in last_values:
                        if last_values[node_key] != current_value:
                            changes.append(f"节点{node.id}: {last_values[node_key]} -> {current_value}")
                    
                    last_values[node_key] = current_value
                
                # 显示当前状态
                total_current_tasks = sum(node.current_tasks for node in nodes)
                
                if changes or total_current_tasks > 0:
                    print(f"\n[{current_time}] 状态更新:")
                    print(f"  总 current_tasks: {total_current_tasks}")
                    print(f"  实际处理任务数: {actual_processing}")
                    print(f"  差异: {total_current_tasks - actual_processing}")
                    
                    for node in nodes:
                        print(f"  节点{node.id}({node.name}): current_tasks={node.current_tasks}")
                    
                    if changes:
                        print(f"  变化: {', '.join(changes)}")
                    
                    if total_current_tasks != actual_processing:
                        print(f"  ⚠️  current_tasks 与实际处理任务数不一致！")
                
                # 每5秒检查一次
                await asyncio.sleep(5)
                
        except KeyboardInterrupt:
            print("\n监控已停止")
            break
        except Exception as e:
            print(f"监控出错: {e}")
            await asyncio.sleep(5)


if __name__ == "__main__":
    asyncio.run(monitor_current_tasks())
