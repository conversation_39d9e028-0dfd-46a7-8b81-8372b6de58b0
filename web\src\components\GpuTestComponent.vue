<template>
  <div class="gpu-test">
    <h3>GPU显示测试</h3>
    
    <div class="test-section">
      <h4>模拟GPU数据</h4>
      <div class="mock-gpu-summary">
        <NodeSystemSummary 
          :nodeId="1" 
          :autoRefresh="false" 
          :isEnabled="true"
        />
      </div>
    </div>

    <div class="test-section">
      <h4>GPU信息预览</h4>
      <div class="gpu-preview">
        <div class="detail-section">
          <h4><span class="gpu-icon">🎮</span> GPU</h4>
          <div v-for="gpu in mockGpuData" :key="gpu.index" class="gpu-item">
            <div class="gpu-header-compact">
              <span class="gpu-name-compact">GPU{{ gpu.index }}</span>
              <span class="gpu-usage" :class="getGpuUsageClass(gpu.utilization_gpu)">
                {{ gpu.utilization_gpu }}%
              </span>
            </div>
            <div class="gpu-details">
              <div class="gpu-detail-row">
                <span class="gpu-memory">
                  {{ formatGpuMemory(gpu.memory_used_mb, gpu.memory_total_mb) }}
                </span>
                <span v-if="gpu.temperature" class="gpu-temp">
                  {{ gpu.temperature }}°C
                </span>
              </div>
              <div class="gpu-name-full" :title="gpu.name">
                {{ gpu.name.length > 25 ? gpu.name.substring(0, 25) + '...' : gpu.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NodeSystemSummary from './NodeSystemSummary.vue'

// 模拟GPU数据
const mockGpuData = ref([
  {
    index: 0,
    name: 'NVIDIA RTX A6000',
    memory_used_mb: 37200,
    memory_total_mb: 48000,
    utilization_gpu: 0,
    temperature: 32
  },
  {
    index: 1,
    name: 'NVIDIA RTX A6000',
    memory_used_mb: 30600,
    memory_total_mb: 48000,
    utilization_gpu: 0,
    temperature: 32
  }
])

// GPU使用率样式类
const getGpuUsageClass = (usage: number) => {
  if (usage >= 80) return 'usage-high'
  if (usage >= 50) return 'usage-medium'
  return 'usage-low'
}

// 格式化GPU内存显示
const formatGpuMemory = (usedMb: number, totalMb: number) => {
  const usedGb = usedMb / 1024
  const totalGb = totalMb / 1024
  
  if (totalGb < 1) {
    return `${usedMb}/${totalMb}MB`
  }
  
  const usedStr = usedGb % 1 === 0 ? usedGb.toFixed(0) : usedGb.toFixed(1)
  const totalStr = totalGb % 1 === 0 ? totalGb.toFixed(0) : totalGb.toFixed(1)
  
  return `${usedStr}/${totalStr}GB`
}
</script>

<style scoped>
.gpu-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.mock-gpu-summary {
  display: inline-block;
}

.gpu-preview {
  background: var(--el-fill-color-extra-light);
  padding: 16px;
  border-radius: 8px;
  max-width: 450px;
}

/* 复制NodeSystemSummary的GPU样式 */
.gpu-icon {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
  text-shadow: 0 0 2px rgba(64, 158, 255, 0.3);
}

.gpu-item {
  margin-bottom: 8px;
  padding: 6px 8px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.gpu-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.gpu-name-compact {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 12px;
}

.gpu-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.gpu-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.gpu-name-full {
  font-size: 10px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
  margin-top: 2px;
}

.gpu-temp {
  color: var(--el-text-color-secondary);
  font-size: 10px;
}

.gpu-usage.usage-high {
  color: var(--el-color-danger);
}

.gpu-usage.usage-medium {
  color: var(--el-color-warning);
}

.gpu-usage.usage-low {
  color: var(--el-color-success);
}
</style>
