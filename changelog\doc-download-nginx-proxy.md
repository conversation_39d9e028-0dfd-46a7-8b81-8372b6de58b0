## [2025-01-16 14:30] DOC文档下载nginx代理功能

### 变更内容
- ✨ 新增：DOC文档下载nginx代理功能
- 🔧 配置：添加下载相关配置项
- ♻️ 重构：修改前端下载逻辑，支持nginx代理直接下载
- 🐛 修复：完善多文档和知识库的文件路径处理

### 详细说明

#### 1. 配置文件修改
- 在 `almond_parser/config/doc_databases.yml` 中为每个服务器添加下载配置：
  - `download.base_url`: 下载基础URL
  - `download.docs_prefix`: 多文档下载路径前缀（默认: /download-docs/）
  - `download.kb_prefix`: 知识库下载路径前缀（默认: /download-kb/）
  - `download.kb_path_prefix`: 知识库文件路径前缀，用于截断（默认: /app/python/files/）

#### 2. 配置模型完善
- 在 `almond_parser/config.py` 中新增 `DownloadConfig` 模型
- 修改 `DOCDatabaseConfig` 模型，添加可选的 `download` 字段
- 在 `almond_parser/schemas/doc_document.py` 中新增 `DownloadConfigResponse` 模型
- 修改 `ServerConfigResponse` 模型，添加可选的 `download` 字段

#### 3. 数据模型完善
- 在 `almond_parser/schemas/doc_document.py` 中为 `DOCDocumentBase` 添加 `file_path` 字段
- 在 `almond_parser/db/models/doc_document.py` 中修改转换函数，包含 `file_path` 字段
- 在 `web/src/types/docDocument.ts` 中为 `DOCDocument` 接口添加 `file_path` 字段

#### 3. 服务器列表API增强
- 修改 `almond_parser/db/doc_database_manager.py` 中的 `get_server_list` 方法
- 在服务器列表中包含每个服务器的下载配置信息

#### 4. 后端下载API增强
- 修改 `almond_parser/api/doc_documents.py` 中的下载接口：
  - 实现真正的文件下载功能，替代之前的501响应
  - 支持通过httpx代理下载nginx文件
  - 自动处理文件重命名，返回原始文件名
  - 支持多文档和知识库的不同路径处理

#### 5. 前端下载逻辑重构
- 修改 `web/src/api/docDocument.ts` 中的 `downloadDOCOriginalFile` 函数：
  - **多文档**：使用后端API代理下载（解决CORS问题，支持重命名）
  - **知识库**：直接通过nginx代理下载（window.open方式）
  - 从服务器列表中获取对应服务器的下载配置
  - 移除fetch方式下载（避免CORS问题）
- 修改 `web/src/types/docDocument.ts` 添加下载配置相关类型定义

#### 6. 路径处理逻辑
- **多文档处理**：
  - 后端从 `document.extra_info.meta_info.temp_file_path` 获取服务器存储的文件路径（通常是UUID等唯一名称）
  - 后端拼接nginx下载URL：`${base_url}${docs_prefix}${server_filename}`
  - 后端通过httpx代理下载文件，并设置正确的文件名响应头
  - 前端通过API下载，获得重命名后的文件

- **知识库处理**：
  - 前端从 `document.file_path` 获取完整路径
  - 前端截断配置的前缀路径（如：/app/python/files/）
  - 前端拼接URL：`${base_url}${kb_prefix}${relative_path}`
  - 前端直接下载，保持原有文件名

### 使用示例

#### nginx配置示例
```nginx
location /download-docs/ {
    # 多文档代理配置
    proxy_pass http://docs-server/;
}

location /download-kb/ {
    # 知识库代理配置  
    proxy_pass http://kb-server/files/;
}
```

#### 配置文件示例
```yaml
databases:
  - name: "server1"
    label: "服务器1"
    host: "localhost"
    port: 3306
    user: "root"
    password: "password"
    database: "database"
    charset: "utf8mb4"
    download:
      base_url: "http://127.0.0.1"
      docs_prefix: "/download-docs/"
      kb_prefix: "/download-kb/"
      kb_path_prefix: "/app/python/files/"
```

### 影响范围
- DOC文档列表页面的下载功能
- 支持多文档和知识库的不同下载路径处理
- 提供配置化的下载URL管理
- 保持向后兼容，配置失败时自动降级

### 依赖项
- nginx代理配置需要正确设置 `/download-docs/` 和 `/download-kb/` 路径
- 后端配置项需要根据实际部署环境进行调整
