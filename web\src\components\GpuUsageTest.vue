<template>
  <div class="gpu-usage-test">
    <h3>GPU使用率显示测试</h3>
    
    <div class="test-section">
      <h4>模拟GPU数据</h4>
      <div class="mock-data">
        <pre>{{ JSON.stringify(mockGpuData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h4>主界面显示效果</h4>
      <div class="main-display">
        <div class="summary-trigger">
          <div class="summary-item">
            <img src="/CPU.png" alt="CPU" class="icon-img" />
            <span class="value usage-low">0.5%</span>
          </div>
          <div class="summary-item">
            <img src="/Memory.png" alt="Memory" class="icon-img" />
            <span class="value usage-medium">69.5%</span>
          </div>
          <div class="summary-item">
            <img src="/GPU.png" alt="GPU" class="icon-img" />
            <span class="value usage-high">{{ getGpuMemoryUsage() }}%</span>
          </div>
          <div class="summary-status">
            <el-icon color="#67C23A"><SuccessFilled /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>弹窗详情显示效果</h4>
      <div class="popup-display">
        <div class="detail-section">
          <h4><img src="/GPU.png" alt="GPU" class="section-icon" /> GPU</h4>
          <div v-for="gpu in mockGpuData" :key="gpu.index" class="gpu-item">
            <div class="gpu-header-compact">
              <span class="gpu-name-compact">GPU{{ gpu.index }}</span>
              <span class="gpu-usage" :class="getGpuMemoryUsageClassForGpu(gpu)">
                {{ getGpuMemoryUsageForGpu(gpu) }}%
              </span>
            </div>
            <div class="gpu-details">
              <div class="gpu-detail-row">
                <span class="gpu-memory">
                  {{ formatGpuMemory(gpu.memory_used_mb, gpu.memory_total_mb) }}
                </span>
                <span v-if="gpu.temperature" class="gpu-temp">
                  {{ gpu.temperature }}°C
                </span>
              </div>
              <div class="gpu-detail-row">
                <span class="gpu-compute-usage">
                  计算: {{ gpu.utilization_gpu }}%
                </span>
                <span v-if="gpu.power_draw" class="gpu-power">
                  {{ gpu.power_draw.toFixed(1) }}W
                </span>
              </div>
              <div class="gpu-name-full" :title="gpu.name">
                {{ gpu.name.length > 25 ? gpu.name.substring(0, 25) + '...' : gpu.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>计算结果验证</h4>
      <div class="calculation-results">
        <div v-for="gpu in mockGpuData" :key="gpu.index" class="calc-item">
          <h5>GPU{{ gpu.index }}</h5>
          <div class="calc-detail">
            <div>已使用内存: {{ gpu.memory_used_mb }}MB</div>
            <div>总内存: {{ gpu.memory_total_mb }}MB</div>
            <div>内存使用率: {{ getGpuMemoryUsageForGpu(gpu) }}%</div>
            <div>计算使用率: {{ gpu.utilization_gpu }}%</div>
            <div>温度: {{ gpu.temperature }}°C</div>
            <div>功耗: {{ gpu.power_draw }}W</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { SuccessFilled } from '@element-plus/icons-vue'

// 模拟GPU数据（基于用户提供的真实数据）
const mockGpuData = ref([
  {
    index: 0,
    name: "NVIDIA RTX A6000",
    memory_total_mb: 49140,
    memory_used_mb: 38114,
    memory_free_mb: 10457,
    utilization_gpu: 0,
    utilization_memory: 0,
    temperature: 32,
    power_draw: 21.55,
    power_limit: 300.0
  },
  {
    index: 1,
    name: "NVIDIA RTX A6000",
    memory_total_mb: 49140,
    memory_used_mb: 30600,
    memory_free_mb: 18540,
    utilization_gpu: 15,
    utilization_memory: 5,
    temperature: 28,
    power_draw: 18.32,
    power_limit: 300.0
  }
])

// 计算GPU内存使用率（平均值）
const getGpuMemoryUsage = () => {
  if (!mockGpuData.value || mockGpuData.value.length === 0) {
    return '0.0'
  }
  
  let totalUsage = 0
  let validGpuCount = 0
  
  mockGpuData.value.forEach(gpu => {
    if (gpu.memory_total_mb > 0) {
      const usage = (gpu.memory_used_mb / gpu.memory_total_mb) * 100
      totalUsage += usage
      validGpuCount++
    }
  })
  
  if (validGpuCount === 0) return '0.0'
  
  const avgUsage = totalUsage / validGpuCount
  return avgUsage.toFixed(1)
}

// 获取单个GPU内存使用率
const getGpuMemoryUsageForGpu = (gpu: any) => {
  if (!gpu || gpu.memory_total_mb <= 0) return '0.0'
  const usage = (gpu.memory_used_mb / gpu.memory_total_mb) * 100
  return usage.toFixed(1)
}

// 获取单个GPU内存使用率样式类
const getGpuMemoryUsageClassForGpu = (gpu: any) => {
  const usage = parseFloat(getGpuMemoryUsageForGpu(gpu))
  if (usage >= 85) return 'usage-high'
  if (usage >= 70) return 'usage-medium'
  return 'usage-low'
}

// 格式化GPU内存显示
const formatGpuMemory = (usedMb: number, totalMb: number) => {
  const usedGb = usedMb / 1024
  const totalGb = totalMb / 1024
  
  if (totalGb < 1) {
    return `${usedMb}/${totalMb}MB`
  }
  
  const usedStr = usedGb % 1 === 0 ? usedGb.toFixed(0) : usedGb.toFixed(1)
  const totalStr = totalGb % 1 === 0 ? totalGb.toFixed(0) : totalGb.toFixed(1)
  
  return `${usedStr}/${totalStr}GB`
}
</script>

<style scoped>
.gpu-usage-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.mock-data {
  background: var(--el-fill-color-extra-light);
  padding: 15px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.main-display {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
}

.popup-display {
  background: var(--el-fill-color-extra-light);
  padding: 20px;
  border-radius: 8px;
  max-width: 500px;
}

.calculation-results {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.calc-item {
  flex: 1;
  min-width: 300px;
  padding: 15px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
}

.calc-detail {
  margin-top: 10px;
}

.calc-detail > div {
  margin-bottom: 5px;
  font-size: 14px;
}

/* 复制组件样式 */
.summary-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  min-width: 200px;
  width: auto;
  flex-wrap: nowrap;
  justify-content: flex-start;
  border: 1px solid var(--el-border-color-light);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  min-width: 0;
  flex-shrink: 0;
  white-space: nowrap;
}

.icon-img {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 2px;
  opacity: 0.8;
}

.section-icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
  opacity: 0.8;
}

.value.usage-high { color: var(--el-color-danger); }
.value.usage-medium { color: var(--el-color-warning); }
.value.usage-low { color: var(--el-color-success); }

.gpu-item {
  margin-bottom: 8px;
  padding: 6px 8px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.gpu-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.gpu-name-compact {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 12px;
}

.gpu-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.gpu-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.gpu-name-full {
  font-size: 10px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
  margin-top: 2px;
}

.gpu-usage.usage-high { color: var(--el-color-danger); }
.gpu-usage.usage-medium { color: var(--el-color-warning); }
.gpu-usage.usage-low { color: var(--el-color-success); }

.gpu-temp, .gpu-compute-usage, .gpu-power {
  color: var(--el-text-color-secondary);
  font-size: 10px;
}
</style>
