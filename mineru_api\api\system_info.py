"""
系统信息 API
提供服务器硬件信息、资源使用情况等
"""
import platform
import psutil
import subprocess
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Header
from loguru import logger

from ..auth import verify_api_key

router = APIRouter(prefix="/system", tags=["系统信息"])


@router.get("/info")
async def get_system_info(
    authorization: str = Header(None)
):
    """
    获取系统基本信息
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        # 系统基本信息
        system_info = {
            "platform": platform.system(),
            "platform_release": platform.release(),
            "platform_version": platform.version(),
            "architecture": platform.machine(),
            "hostname": platform.node(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
        }
        
        # CPU信息
        cpu_info = {
            "physical_cores": psutil.cpu_count(logical=False),
            "total_cores": psutil.cpu_count(logical=True),
            "max_frequency": psutil.cpu_freq().max if psutil.cpu_freq() else None,
            "min_frequency": psutil.cpu_freq().min if psutil.cpu_freq() else None,
            "current_frequency": psutil.cpu_freq().current if psutil.cpu_freq() else None,
            "cpu_usage": psutil.cpu_percent(interval=1),
            "cpu_usage_per_core": psutil.cpu_percent(interval=1, percpu=True)
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            "total": round(memory.total / (1024**3), 2),  # GB
            "available": round(memory.available / (1024**3), 2),  # GB
            "used": round(memory.used / (1024**3), 2),  # GB
            "percentage": memory.percent
        }
        
        # 磁盘信息
        disk_info = []
        for partition in psutil.disk_partitions():
            try:
                partition_usage = psutil.disk_usage(partition.mountpoint)
                disk_info.append({
                    "device": partition.device,
                    "mountpoint": partition.mountpoint,
                    "file_system": partition.fstype,
                    "total": round(partition_usage.total / (1024**3), 2),  # GB
                    "used": round(partition_usage.used / (1024**3), 2),  # GB
                    "free": round(partition_usage.free / (1024**3), 2),  # GB
                    "percentage": round((partition_usage.used / partition_usage.total) * 100, 1)
                })
            except PermissionError:
                continue
        
        # GPU信息
        gpu_info = get_gpu_info()
        
        # 网络信息
        network_info = get_network_info()
        
        return {
            "system": system_info,
            "cpu": cpu_info,
            "memory": memory_info,
            "disk": disk_info,
            "gpu": gpu_info,
            "network": network_info,
            "timestamp": psutil.boot_time()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统信息失败")


@router.get("/resources")
async def get_system_resources(
    authorization: str = Header(None)
):
    """
    获取系统资源使用情况（实时）
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_per_core = psutil.cpu_percent(interval=1, percpu=True)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘IO
        disk_io = psutil.disk_io_counters()
        
        # 网络IO
        network_io = psutil.net_io_counters()
        
        # 进程数量
        process_count = len(psutil.pids())
        
        # 负载平均值（仅Linux）
        load_avg = None
        if hasattr(psutil, "getloadavg"):
            load_avg = psutil.getloadavg()
        
        # GPU使用情况
        gpu_usage = get_gpu_usage()
        
        return {
            "cpu": {
                "usage_percent": cpu_percent,
                "usage_per_core": cpu_per_core,
                "load_avg": load_avg
            },
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "usage_percent": memory.percent
            },
            "disk_io": {
                "read_bytes": disk_io.read_bytes if disk_io else 0,
                "write_bytes": disk_io.write_bytes if disk_io else 0,
                "read_count": disk_io.read_count if disk_io else 0,
                "write_count": disk_io.write_count if disk_io else 0
            } if disk_io else None,
            "network_io": {
                "bytes_sent": network_io.bytes_sent if network_io else 0,
                "bytes_recv": network_io.bytes_recv if network_io else 0,
                "packets_sent": network_io.packets_sent if network_io else 0,
                "packets_recv": network_io.packets_recv if network_io else 0
            } if network_io else None,
            "processes": {
                "total_count": process_count
            },
            "gpu": gpu_usage,
            "timestamp": psutil.time.time()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统资源失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统资源失败")


def get_gpu_info() -> List[Dict[str, Any]]:
    """获取GPU信息"""
    gpu_info = []
    try:
        result = subprocess.run([
            "nvidia-smi", 
            "--query-gpu=index,name,memory.total,memory.used,memory.free,utilization.gpu,utilization.memory,temperature.gpu,power.draw,power.limit",
            "--format=csv,noheader,nounits"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = [p.strip() for p in line.split(', ')]
                    if len(parts) >= 8:
                        gpu_info.append({
                            "index": int(parts[0]),
                            "name": parts[1],
                            "memory_total_mb": int(parts[2]),
                            "memory_used_mb": int(parts[3]),
                            "memory_free_mb": int(parts[4]),
                            "utilization_gpu": int(parts[5]),
                            "utilization_memory": int(parts[6]),
                            "temperature": int(parts[7]) if parts[7] != '[N/A]' else None,
                            "power_draw": float(parts[8]) if len(parts) > 8 and parts[8] != '[N/A]' else None,
                            "power_limit": float(parts[9]) if len(parts) > 9 and parts[9] != '[N/A]' else None
                        })
    except (subprocess.TimeoutExpired, FileNotFoundError, ValueError) as e:
        logger.debug(f"获取GPU信息失败: {e}")
    
    return gpu_info


def get_gpu_usage() -> List[Dict[str, Any]]:
    """获取GPU实时使用情况"""
    gpu_usage = []
    try:
        result = subprocess.run([
            "nvidia-smi", 
            "--query-gpu=index,utilization.gpu,utilization.memory,memory.used,memory.total,temperature.gpu",
            "--format=csv,noheader,nounits"
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = [p.strip() for p in line.split(', ')]
                    if len(parts) >= 6:
                        gpu_usage.append({
                            "index": int(parts[0]),
                            "utilization_gpu": int(parts[1]),
                            "utilization_memory": int(parts[2]),
                            "memory_used_mb": int(parts[3]),
                            "memory_total_mb": int(parts[4]),
                            "temperature": int(parts[5]) if parts[5] != '[N/A]' else None
                        })
    except (subprocess.TimeoutExpired, FileNotFoundError, ValueError) as e:
        logger.debug(f"获取GPU使用情况失败: {e}")
    
    return gpu_usage


def get_network_info() -> List[Dict[str, Any]]:
    """获取网络接口信息"""
    network_info = []
    try:
        network_interfaces = psutil.net_if_addrs()
        network_stats = psutil.net_if_stats()
        
        for interface_name, addresses in network_interfaces.items():
            interface_info = {
                "name": interface_name,
                "addresses": [],
                "is_up": False,
                "speed": None
            }
            
            # 获取地址信息
            for addr in addresses:
                if addr.family == psutil.AF_LINK:  # MAC地址
                    interface_info["mac_address"] = addr.address
                elif addr.family == 2:  # IPv4
                    interface_info["addresses"].append({
                        "type": "IPv4",
                        "address": addr.address,
                        "netmask": addr.netmask
                    })
                elif addr.family == 10:  # IPv6
                    interface_info["addresses"].append({
                        "type": "IPv6", 
                        "address": addr.address,
                        "netmask": addr.netmask
                    })
            
            # 获取接口状态
            if interface_name in network_stats:
                stats = network_stats[interface_name]
                interface_info["is_up"] = stats.isup
                interface_info["speed"] = stats.speed
            
            network_info.append(interface_info)
            
    except Exception as e:
        logger.debug(f"获取网络信息失败: {e}")
    
    return network_info
