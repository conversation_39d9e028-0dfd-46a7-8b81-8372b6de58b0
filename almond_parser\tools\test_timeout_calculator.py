#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试超时计算器
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.utils.timeout_calculator import TimeoutCalculator, calculate_task_timeout, get_zombie_task_threshold
from almond_parser.db.models.mineru_node import ServiceType


def test_timeout_calculator():
    """测试超时计算器"""
    print("=" * 60)
    print("测试超时计算器")
    print("=" * 60)
    
    # 测试不同文件大小和服务类型
    test_cases = [
        # (服务类型, 文件大小MB, 解析模式, 描述)
        (ServiceType.DOCUMENT, 1, "pipeline", "小文档-标准模式"),
        (ServiceType.DOCUMENT, 10, "pipeline", "中等文档-标准模式"),
        (ServiceType.DOCUMENT, 50, "sglang", "大文档-VLM模式"),
        
        (ServiceType.KNOWLEDGE_BASE, 5, "pipeline", "小知识库-标准模式"),
        (ServiceType.KNOWLEDGE_BASE, 100, "pipeline", "大知识库-标准模式"),
        (ServiceType.KNOWLEDGE_BASE, 500, "auto", "超大知识库-自动模式"),
        
        (ServiceType.UNIVERSAL, 2, "pipeline", "小通用文件-标准模式"),
        (ServiceType.UNIVERSAL, 20, "auto", "中等通用文件-自动模式"),
        (ServiceType.UNIVERSAL, 100, "sglang", "大通用文件-VLM模式"),
    ]
    
    print(f"{'服务类型':<15} {'文件大小':<10} {'解析模式':<10} {'超时时间':<10} {'僵尸阈值':<10} {'描述'}")
    print("-" * 80)
    
    for service_type, file_size_mb, parse_mode, description in test_cases:
        file_size_bytes = int(file_size_mb * 1024 * 1024)
        
        # 计算超时时间
        timeout_minutes = TimeoutCalculator.calculate_timeout_minutes(
            service_type, file_size_bytes, parse_mode
        )
        
        # 计算僵尸任务阈值
        zombie_threshold = get_zombie_task_threshold(
            service_type.value, file_size_bytes, parse_mode
        )
        
        print(f"{service_type.value:<15} {file_size_mb:<10} {parse_mode:<10} {timeout_minutes:<10} {zombie_threshold:<10} {description}")
    
    print("\n" + "=" * 60)
    print("详细信息示例")
    print("=" * 60)
    
    # 显示详细信息
    example_info = TimeoutCalculator.get_timeout_info(
        ServiceType.KNOWLEDGE_BASE, 
        100 * 1024 * 1024,  # 100MB
        "pipeline"
    )
    
    print("100MB 知识库文件的详细超时信息:")
    for key, value in example_info.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    test_timeout_calculator()
