#!/usr/bin/env python3
"""
多实例启动脚本
支持启动多个 MinerU API 实例，每个实例使用不同的端口和GPU
"""
import argparse
import subprocess
import sys
import time
import os
from pathlib import Path
from typing import List, Dict, Optional

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from mineru_api.utils.logger import logger


class MultiInstanceManager:
    """多实例管理器"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.instances: List[Dict] = []
    
    def get_available_gpus(self) -> List[int]:
        """获取可用的GPU列表"""
        try:
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=index", "--format=csv,noheader,nounits"],
                capture_output=True, text=True, check=True
            )
            gpu_indices = [int(line.strip()) for line in result.stdout.strip().split('\n') if line.strip()]
            return gpu_indices
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning("无法检测GPU，将使用CPU模式")
            return []
    
    def check_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    def start_instance(self, instance_id: int, api_port: int, gpu_id: Optional[int] = None, 
                      auto_sglang: bool = True, backend_mode: str = "auto") -> subprocess.Popen:
        """启动单个实例"""
        logger.info(f"启动实例 {instance_id}...")
        logger.info(f"  API端口: {api_port}")
        logger.info(f"  SGLang端口: {30000 + instance_id}")
        if gpu_id is not None:
            logger.info(f"  GPU设备: {gpu_id}")
        
        # 检查端口可用性
        if not self.check_port_available(api_port):
            raise RuntimeError(f"端口 {api_port} 已被占用")
        
        sglang_port = 30000 + instance_id
        if not self.check_port_available(sglang_port):
            raise RuntimeError(f"SGLang端口 {sglang_port} 已被占用")
        
        # 构建启动命令
        cmd = [
            sys.executable, "mineru_api/start_server.py",
            "--host", "0.0.0.0",
            "--port", str(api_port),
            "--instance-id", str(instance_id),
            "--backend-mode", backend_mode
        ]
        
        if auto_sglang:
            cmd.append("--auto-sglang")
        
        # 设置环境变量
        env = os.environ.copy()
        env["INSTANCE_ID"] = str(instance_id)
        
        if gpu_id is not None:
            env["CUDA_VISIBLE_DEVICES"] = str(gpu_id)
        
        # 启动进程
        try:
            process = subprocess.Popen(
                cmd,
                env=env,
                cwd=Path(__file__).parent.parent,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 记录实例信息
            instance_info = {
                "id": instance_id,
                "api_port": api_port,
                "sglang_port": sglang_port,
                "gpu_id": gpu_id,
                "process": process,
                "pid": process.pid
            }
            
            self.instances.append(instance_info)
            self.processes.append(process)
            
            logger.success(f"✅ 实例 {instance_id} 启动成功 (PID: {process.pid})")
            return process
            
        except Exception as e:
            logger.error(f"❌ 启动实例 {instance_id} 失败: {e}")
            raise
    
    def start_multiple_instances(self, count: int, base_api_port: int = 2233, 
                               auto_gpu_assign: bool = True, auto_sglang: bool = True,
                               backend_mode: str = "auto") -> None:
        """启动多个实例"""
        logger.info(f"准备启动 {count} 个实例...")
        
        # 获取可用GPU
        available_gpus = self.get_available_gpus() if auto_gpu_assign else []
        logger.info(f"检测到 {len(available_gpus)} 个GPU: {available_gpus}")
        
        # 启动实例
        for i in range(count):
            instance_id = i
            api_port = base_api_port + i
            gpu_id = available_gpus[i % len(available_gpus)] if available_gpus else None
            
            try:
                self.start_instance(
                    instance_id=instance_id,
                    api_port=api_port,
                    gpu_id=gpu_id,
                    auto_sglang=auto_sglang,
                    backend_mode=backend_mode
                )
                
                # 等待一下再启动下一个实例
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"启动实例 {instance_id} 失败: {e}")
                self.stop_all()
                sys.exit(1)
        
        logger.success(f"🎉 成功启动 {len(self.instances)} 个实例")
        self.show_status()
    
    def show_status(self) -> None:
        """显示实例状态"""
        logger.info("\n📋 实例状态:")
        logger.info("-" * 80)
        logger.info(f"{'ID':<4} {'API端口':<8} {'SGLang端口':<10} {'GPU':<6} {'PID':<8} {'状态'}")
        logger.info("-" * 80)
        
        for instance in self.instances:
            status = "运行中" if instance["process"].poll() is None else "已停止"
            gpu_info = f"GPU{instance['gpu_id']}" if instance['gpu_id'] is not None else "CPU"
            
            logger.info(f"{instance['id']:<4} {instance['api_port']:<8} {instance['sglang_port']:<10} "
                       f"{gpu_info:<6} {instance['pid']:<8} {status}")
        
        logger.info("-" * 80)
        logger.info("\n🔗 访问地址:")
        for instance in self.instances:
            logger.info(f"  实例 {instance['id']}: http://localhost:{instance['api_port']}")
            logger.info(f"    - API文档: http://localhost:{instance['api_port']}/docs")
            logger.info(f"    - SGLang: http://localhost:{instance['sglang_port']}")
    
    def monitor_instances(self) -> None:
        """监控实例运行状态"""
        logger.info("\n🔍 监控实例中... (按 Ctrl+C 停止)")
        
        try:
            while True:
                time.sleep(5)
                
                # 检查进程状态
                for instance in self.instances:
                    if instance["process"].poll() is not None:
                        logger.error(f"❌ 实例 {instance['id']} 已停止 (PID: {instance['pid']})")
                        # 可以在这里添加自动重启逻辑
                
        except KeyboardInterrupt:
            logger.info("\n收到停止信号...")
            self.stop_all()
    
    def stop_all(self) -> None:
        """停止所有实例"""
        logger.info("停止所有实例...")
        
        for instance in self.instances:
            try:
                if instance["process"].poll() is None:
                    logger.info(f"停止实例 {instance['id']} (PID: {instance['pid']})")
                    instance["process"].terminate()
                    
                    # 等待进程结束
                    try:
                        instance["process"].wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"强制杀死实例 {instance['id']}")
                        instance["process"].kill()
                        instance["process"].wait()
                        
            except Exception as e:
                logger.error(f"停止实例 {instance['id']} 失败: {e}")
        
        logger.info("✅ 所有实例已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MinerU API 多实例启动器")
    parser.add_argument("--count", type=int, default=2, help="启动实例数量")
    parser.add_argument("--base-port", type=int, default=2233, help="API基础端口")
    parser.add_argument("--no-auto-gpu", action="store_true", help="禁用自动GPU分配")
    parser.add_argument("--no-auto-sglang", action="store_true", help="禁用自动启动SGLang")
    parser.add_argument("--backend-mode", type=str, choices=["auto", "pipeline-only", "sglang-only"],
                        default="auto", help="后端模式")
    parser.add_argument("--no-monitor", action="store_true", help="启动后不监控")
    
    args = parser.parse_args()
    
    if args.count <= 0:
        logger.error("实例数量必须大于0")
        sys.exit(1)
    
    if args.count > 8:
        logger.warning("启动过多实例可能导致资源不足")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            sys.exit(0)
    
    manager = MultiInstanceManager()
    
    try:
        manager.start_multiple_instances(
            count=args.count,
            base_api_port=args.base_port,
            auto_gpu_assign=not args.no_auto_gpu,
            auto_sglang=not args.no_auto_sglang,
            backend_mode=args.backend_mode
        )
        
        if not args.no_monitor:
            manager.monitor_instances()
        else:
            logger.info("实例已启动，使用 Ctrl+C 停止监控")
            input("按 Enter 键停止所有实例...")
            manager.stop_all()
            
    except KeyboardInterrupt:
        logger.info("\n收到中断信号...")
        manager.stop_all()
    except Exception as e:
        logger.error(f"启动失败: {e}")
        manager.stop_all()
        sys.exit(1)


if __name__ == "__main__":
    main()
