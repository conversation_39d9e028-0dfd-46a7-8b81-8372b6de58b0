<template>
  <div class="upload-container">
    <el-button type="primary" class="upload-button" @click="dialogVisible = true">
      <el-icon class="upload-icon"><Upload /></el-icon>
      上传文档
    </el-button>

    <el-dialog
      v-model="dialogVisible"
      title="上传文档"
      width="900px"
      :close-on-click-modal="false"
      class="upload-dialog"
      top="5vh"
    >
      <div class="upload-content">
        <el-form ref="formRef" :model="uploadForm" :rules="rules" label-width="120px" class="upload-form">
          <!-- 文件上传区域 -->
          <div class="upload-section">
<!--            <div class="section-header">-->
<!--              <el-icon class="section-icon"><Upload /></el-icon>-->
<!--              <span class="section-title">文件上传</span>-->
<!--            </div>-->
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :multiple="true"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              :limit="10"
              drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或 <em>点击上传</em>
              </div>
              <div class="el-upload__tip">
                支持 PDF、Word、PPT、图片等格式，单次最多10个文件
              </div>
            </el-upload>
          </div>

          <!-- 认证设置 -->
          <div class="form-section auth-section">
            <div class="section-header">
              <el-icon class="section-icon"><Key /></el-icon>
              <span class="section-title">认证设置</span>
            </div>
            <el-form-item label="API Key" prop="api_key" required>
              <el-input
                v-model="uploadForm.api_key"
                type="password"
                placeholder="请输入API Key（必填）"
                show-password
                clearable
                class="api-key-input"
              />
              <div class="form-tip">
                <el-icon><InfoFilled /></el-icon>
                API Key 是必填项，请前往 <strong>API 密钥</strong> 页面获取您的密钥
              </div>
            </el-form-item>
          </div>

          <!-- 基本设置 -->
          <div class="form-section basic-section">
            <div class="section-header">
              <el-icon class="section-icon"><Setting /></el-icon>
              <span class="section-title">基本设置</span>
            </div>
            <el-form-item label="任务优先级" prop="priority">
              <el-slider
                v-model="uploadForm.priority"
                :min="1"
                :max="10"
                :marks="{
                  1: '低',
                  5: '中',
                  10: '高'
                }"
                class="priority-slider"
              />
            </el-form-item>
            <el-form-item label="最大重试次数" prop="max_retries">
              <el-input-number
                v-model="uploadForm.max_retries"
                :min="0"
                :max="5"
                controls-position="right"
                class="retry-input"
              />
            </el-form-item>
          </div>

          <!-- 解析配置 -->
          <div class="form-section parse-section">
            <div class="section-header">
              <el-icon class="section-icon"><Cpu /></el-icon>
              <span class="section-title">解析配置</span>
            </div>
            <el-form-item label="服务类型" prop="service_type">
              <el-select v-model="uploadForm.service_type" placeholder="请选择服务类型" class="full-width">
                <el-option label="文档解读" value="document">
                  <el-icon><document /></el-icon>
                  <span>文档解读</span>
                </el-option>
                <el-option label="知识库解析" value="knowledge_base">
                  <el-icon><collection /></el-icon>
                  <span>知识库解析</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="解析模式" prop="parse_mode">
              <el-select v-model="uploadForm.parse_mode" placeholder="请选择解析模式" class="full-width">
                <el-option label="Pipeline模式" value="pipeline">
                  <el-icon><connection /></el-icon>
                  <span>Pipeline模式</span>
                </el-option>
                <el-option label="SGLang模式" value="sglang">
                  <el-icon><cpu /></el-icon>
                  <span>SGLang模式</span>
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <!-- 解析配置 -->
          <div class="form-section config-section">
            <div class="section-header">
              <el-icon class="section-icon"><Document /></el-icon>
              <span class="section-title">高级配置</span>
            </div>
            <el-form-item label="备注信息" prop="remarks">
              <el-input
                v-model="uploadForm.remarks"
                type="textarea"
                :rows="2"
                placeholder="请输入备注信息（可选）"
                class="remarks-textarea"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="解析配置" prop="parse_config">
              <el-input
                v-model="uploadForm.parse_config"
                type="textarea"
                :rows="4"
                placeholder="请输入JSON格式的解析配置（可选）"
                class="config-textarea"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpload" :loading="uploading">
            {{ uploading ? '正在上传...' : '开始上传' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, UploadInstance, UploadFile } from 'element-plus'
import {
  Upload,
  UploadFilled,
  Document,
  Collection,
  Connection,
  Cpu,
  MagicStick,
  InfoFilled,
  Key,
  Setting
} from '@element-plus/icons-vue'
import { uploadDocuments } from '@/api/document'
import { getDefaultApiKey } from '@/api/apiKey'

const emit = defineEmits<{
  (e: 'upload-success'): void
}>()

const dialogVisible = ref(false)
const uploading = ref(false)
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadFile[]>([])

const uploadForm = reactive({
  api_key: '',
  priority: 1,
  service_type: 'document',
  parse_mode: 'sglang',
  max_retries: 2,
  parse_config: '',
  remarks: ''
})

const rules = {
  api_key: [
    { required: true, message: '请输入API Key', trigger: 'blur' },
    { min: 1, message: 'API Key不能为空', trigger: 'blur' }
  ]
}

const handleFileChange = (uploadFile: UploadFile) => {
  // 检查是否超过最大文件数限制
  if (fileList.value.length >= 50) {
    ElMessage.warning('单次最多上传50个文件')
    return false
  }

  // 确保文件被添加到列表中
  if (!fileList.value.some(file => file.uid === uploadFile.uid)) {
    fileList.value.push(uploadFile)
  }
  return true
}

const handleFileRemove = (uploadFile: UploadFile) => {
  fileList.value = fileList.value.filter(file => file.uid !== uploadFile.uid)
}

// 监听对话框打开，自动获取默认API key
watch(dialogVisible, async (newValue) => {
  if (newValue && !uploadForm.api_key) {
    try {
      const response = await getDefaultApiKey()
      if (response && response.key) {
        uploadForm.api_key = response.key
        ElMessage.success('已自动填入默认API Key')
      }
    } catch (error) {
      console.warn('获取默认API Key失败:', error)
      ElMessage.warning('无法获取默认API Key，请手动输入')
    }
  }
})

const handleUpload = async () => {
  if (!fileList.value.length) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 验证表单
  if (!formRef.value) return
  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确')
    return
  }

  // 检查文件是否都有效
  const invalidFiles = fileList.value.filter(file => !file.raw)
  if (invalidFiles.length > 0) {
    ElMessage.error('存在无效的文件，请重新选择')
    return
  }

  uploading.value = true
  try {
    const formData = new FormData()
    fileList.value.forEach(file => {
      if (file.raw) {
        formData.append('files', file.raw)
      }
    })
    
    // 添加其他参数
    formData.append('priority', uploadForm.priority.toString())
    formData.append('service_type', uploadForm.service_type)
    formData.append('parse_mode', uploadForm.parse_mode)
    formData.append('max_retries', uploadForm.max_retries.toString())
    if (uploadForm.parse_config) {
      formData.append('parse_config', uploadForm.parse_config)
    }
    if (uploadForm.remarks) {
      formData.append('remarks', uploadForm.remarks)
    }

    const response = await uploadDocuments(formData, uploadForm.api_key)
    ElMessage.success(`上传成功，批次ID: ${response.batch_id}`)
    dialogVisible.value = false
    emit('upload-success')

    // 清空表单
    fileList.value = []
    uploadForm.api_key = ''
    uploadForm.priority = 1
    uploadForm.service_type = 'document'
    uploadForm.parse_mode = 'sglang'
    uploadForm.max_retries = 2
    uploadForm.parse_config = ''
    uploadForm.remarks = ''
  } catch (error: any) {
    ElMessage.error('上传失败: ' + error.message)
  } finally {
    uploading.value = false
  }
}
</script>

<style scoped>
.upload-container {
  display: flex;
  align-items: center;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 15px;
}

.upload-icon {
  font-size: 18px;
}

:deep(.upload-dialog) {
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__header {
    padding: 24px 32px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .el-dialog__footer {
    padding: 16px 32px 24px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

.upload-content {
  padding: 24px 32px;
  max-height: 70vh;
  overflow-y: auto;
}

.upload-form {
  .el-form-item__label {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.upload-section {
  margin-bottom: 24px;

  .upload-demo {
    width: 100%;
  }
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);

  .section-icon {
    font-size: 18px;
    color: var(--el-color-primary);
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.form-section {
  background: var(--el-bg-color-page);
  padding: 24px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  margin-bottom: 20px;
  //width: 100%;
}

:deep(.upload-demo) {
  width: 100%;

  .el-upload-dragger {
    width: 100%;
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }

    .el-icon--upload {
      font-size: 48px;
      color: var(--el-color-primary);
      margin-bottom: 16px;
    }

    .el-upload__text {
      font-size: 16px;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;

      em {
        color: var(--el-color-primary);
        font-style: normal;
        font-weight: 600;
      }
    }

    .el-upload__tip {
      color: var(--el-text-color-secondary);
      font-size: 13px;
      margin: 0;
    }
  }

  .el-upload-list {
    max-height: 200px;
    overflow-y: auto;
    margin-top: 16px;
    padding: 8px;
    background: var(--el-bg-color-page);
    border-radius: 6px;
  }


}



.api-key-input {
  width: 100%;
}

.priority-slider {
  width: 100%;
  margin: 8px 0;
}

.config-textarea {
  width: 100%;

  :deep(.el-textarea__inner) {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
  }
}

:deep(.el-select) {
  width: 100%;

  .el-select-dropdown__item {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 16px;
    }
  }
}

:deep(.el-input-number) {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 4px;

  .el-icon {
    color: var(--el-color-primary);
    font-size: 14px;
  }

  strong {
    color: var(--el-color-primary);
    font-weight: 600;
  }
}

/* 响应式布局 */

@media (max-width: 768px) {
  :deep(.upload-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }

  .upload-content {
    padding: 16px 20px;
  }



  .form-section {
    padding: 16px;
  }

  .section-header {
    .section-title {
      font-size: 14px;
    }

    .section-icon {
      font-size: 16px;
    }
  }
}
</style> 