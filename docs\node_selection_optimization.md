# 节点选择策略优化方案

## 🎯 优化目标

解决上传解析时的节点选择问题，实现更智能、更合理的资源分配策略。

## 📊 问题分析

### 原有问题
1. **架构混乱**：前端同时调用两套API（almond_parser 和 mineru-api）
2. **缺少服务类型选择**：上传时无法指定服务类型
3. **节点选择策略不合理**：简单的优先级覆盖可能导致资源浪费

### 用户原始需求
- 上传时手动指定类型（文档解析、知识库解析）
- 不指定时走通用解析按优先级分配
- 同优先级下按类型归类处理
- **问题**：优先级最高的节点处理所有任务（不合理）

## 💡 优化策略

### 核心原则：类型匹配 + 优先级 + 负载均衡

```
节点选择优先级：
1. 类型匹配优先：专用节点 > 通用节点
2. 同类型内排序：优先级(高→低) > 负载(低→高)
3. 避免跨类型分配（除非无其他选择）
4. 智能类型推断：PDF/DOC → document
```

## 🛠️ 实现方案

### 1. 前端界面改进

**新增字段**：
- 服务类型选择：自动选择/文档解析/知识库解析
- 任务优先级：1-10级别
- 解析模式：auto/pipeline/sglang

**API统一**：
- 统一使用 almond_parser 的 `/manage/upload` 接口
- 废弃直接调用 mineru-api 的方式

### 2. 后端API改进

**新增参数**：
```python
@router.post("/upload")
async def upload_documents(
    files: List[UploadFile],
    priority: int = 1,
    service_type: str = "auto",      # 新增
    parse_mode: str = "auto",        # 新增
    max_retries: int = 2,            # 新增
    parse_config: Optional[str] = None
):
```

### 3. 智能节点选择算法

```python
class NodeSelector:
    async def select_best_node(
        self, 
        service_type: str = "auto",
        parse_mode: Optional[str] = None,
        file_type: Optional[str] = None
    ) -> Optional[MinerUNode]:
        
        # 1. 智能推断服务类型
        if service_type == "auto":
            service_type = self._infer_service_type(file_type)
        
        # 2. 优先查找专用节点
        if service_type in ["document", "knowledge_base"]:
            node = await self._find_dedicated_node(service_type, parse_mode)
            if node:
                return node
        
        # 3. 查找通用节点作为备选
        return await self._find_universal_node(parse_mode)
```

### 4. 文件类型智能推断

```python
def _infer_service_type(self, file_type: Optional[str]) -> str:
    document_types = {"pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx"}
    image_types = {"jpg", "jpeg", "png", "bmp", "tiff"}
    
    if file_type.lower() in document_types or file_type.lower() in image_types:
        return "document"
    else:
        return "document"  # 默认为文档解析
```

## 🎯 优势分析

### 相比原方案的改进

1. **避免资源浪费**
   - 专用节点处理匹配的任务类型，效率更高
   - 避免知识库节点处理文档任务的不匹配情况

2. **保证服务质量**
   - 知识库解析和文档解析可能有不同的优化配置
   - 专用节点针对特定任务类型优化

3. **负载均衡**
   - 避免单个高优先级节点过载
   - 同等条件下选择负载最低的节点

4. **灵活性**
   - 用户可以手动指定类型
   - 系统可以智能自动选择
   - 支持降级到通用节点

## 📈 使用场景

### 场景1：用户指定文档解析
```
用户选择：service_type = "document"
选择逻辑：文档专用节点 → 通用节点
```

### 场景2：用户指定知识库解析
```
用户选择：service_type = "knowledge_base"
选择逻辑：知识库专用节点 → 通用节点
```

### 场景3：用户选择自动
```
用户选择：service_type = "auto"
推断逻辑：根据文件类型推断 → 按推断类型选择节点
```

## 🔧 配置建议

### 节点配置策略
1. **专用节点**：配置为特定服务类型，优先级可以相同
2. **通用节点**：配置为universal类型，作为备选
3. **优先级设置**：根据硬件性能和重要性设置，避免过度集中

### 负载均衡
- 同等条件下优先选择当前任务数最少的节点
- 避免单点过载，提高整体处理效率

## 📝 总结

这个优化方案解决了原有的架构混乱和资源分配不合理问题，通过智能的类型匹配和负载均衡，实现了更高效、更合理的节点选择策略。既保证了用户的灵活性需求，又避免了简单优先级覆盖带来的问题。
