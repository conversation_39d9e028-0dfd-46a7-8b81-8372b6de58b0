/**
 * DOC文档相关类型定义
 */

// DOC文档状态枚举
export type DOCDocumentStatus = 
  | 'PENDING'
  | 'PROCESSING' 
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'

// 知识库类型
export type KBType = 'personal' | 'project'

// 解析类型
export type ParseType = 'OCR' | 'LAYOUT' | 'TEXT'

// 下载配置接口
export interface DownloadConfig {
  base_url: string
  docs_prefix: string
  kb_prefix: string
  kb_path_prefix: string
}

// 服务器配置接口
export interface ServerConfig {
  name: string
  label: string
  host: string
  port: number
  database: string
  download?: DownloadConfig
}

// Parse Task 文档接口 (对应 parse_tasks 表)
export interface ParseTaskDocument {
  id: number
  username: string
  file_name: string
  file_path: string
  doc_id?: string
  batch_id: string
  almond_parser_id: string
  status?: string
  maxkb_url?: string
  maxkb_id?: string
  error_message?: string
  created_at?: string
  updated_at?: string
  session_id?: string
  task_type?: string
  kb_type: KBType
  file_size?: number
  file_type?: string
  parsed_content_path?: string
}

// Doc Reader Task 文档接口 (对应 doc_reader_tasks 表)
export interface DocReaderTaskDocument {
  id: number
  task_id: string
  username: string
  session_id: string
  doc_id: string
  batch_id: string
  filename: string
  file_type: string
  parse_type: ParseType
  parser_doc_id?: string
  almond_batch_id?: string
  status?: string
  chunks_count?: number
  error_message?: string
  meta_info?: Record<string, any>
  created_at?: string
  updated_at?: string
  completed_at?: string
  retry_count: number
}

// 统一的DOC文档接口
export interface DOCDocument {
  id: number
  document_id: string
  batch_id: string
  username: string
  file_name: string
  file_path?: string
  file_type: string
  file_size?: number
  status: DOCDocumentStatus
  kb_type?: KBType
  parse_type?: ParseType
  error_message?: string
  created_at: string
  updated_at: string
  completed_at?: string
  duration?: number // 耗时（秒）
  remarks?: string
  extra_info?: Record<string, any>
  source_table: 'parse_tasks' | 'doc_reader_tasks' // 标识数据来源
  source_name: string // 数据来源名称
}

// DOC文档查询参数
export interface DOCQueryParams {
  page: number
  page_size: number
  server?: string
  source_type?: 'document' | 'knowledge'  // 数据源类型: document(多文档) 或 knowledge(知识库)
  username?: string
  file_name?: string
  status?: string
  kb_type?: KBType
  parse_type?: ParseType
  batch_id?: string
  document_id?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// DOC文档查询结果
export interface DOCQueryResult {
  items: DOCDocument[]
  pagination: {
    page: number
    page_size: number
    total: number
    pages: number
  }
}

// 状态选项
export interface DOCStatusOption {
  value: DOCDocumentStatus
  label: string
  type: 'success' | 'warning' | 'danger' | 'info'
}

// 知识库类型选项
export interface KBTypeOption {
  value: KBType
  label: string
  color: string
}

// 解析类型选项
export interface ParseTypeOption {
  value: ParseType
  label: string
  color: string
}
