#!/usr/bin/env python3
"""
简单测试认证模块导入
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🧪 测试认证模块导入")

try:
    print("1️⃣ 导入配置...")
    from mineru_api.config import ENABLE_AUTH, get_auth_config
    print(f"   认证启用: {ENABLE_AUTH}")
    
    if not ENABLE_AUTH:
        print("   认证已禁用，测试完成")
        exit(0)
    
    print("2️⃣ 获取认证配置...")
    config = get_auth_config()
    print(f"   后端类型: {config.backend_type}")
    
    print("3️⃣ 导入认证模型...")
    from mineru_api.auth.models import AuthConfig, APIKey
    print("   ✅ 模型导入成功")
    
    print("4️⃣ 导入认证后端...")
    from mineru_api.auth.backends import get_auth_backend
    backend = get_auth_backend(config)
    print(f"   后端类型: {type(backend).__name__}")
    
    print("5️⃣ 导入认证管理器...")
    from mineru_api.auth.manager import AuthManager
    print("   ✅ 管理器类导入成功")
    
    print("6️⃣ 创建认证管理器...")
    manager = AuthManager(config)
    print(f"   管理器创建成功: {type(manager).__name__}")
    
    print("\n🎉 所有导入测试通过！")
    
except Exception as e:
    print(f"\n❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
