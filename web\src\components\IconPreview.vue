<template>
  <div class="icon-preview">
    <h3>图标预览测试</h3>
    
    <div class="preview-section">
      <h4>系统资源摘要预览</h4>
      <div class="mock-summary">
        <div class="summary-trigger">
          <div class="summary-item">
            <img src="/CPU.png" alt="CPU" class="icon-img" />
            <span class="value usage-low">15.2%</span>
          </div>
          <div class="summary-item">
            <img src="/Memory.png" alt="Memory" class="icon-img" />
            <span class="value usage-medium">68.5%</span>
          </div>
          <div class="summary-item">
            <span class="icon">🎮</span>
            <span class="value">2GPU</span>
          </div>
          <div class="summary-status">
            <el-icon color="#67C23A"><SuccessFilled /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="preview-section">
      <h4>禁用状态预览</h4>
      <div class="mock-summary">
        <div class="summary-trigger">
          <div class="disabled-summary">
            <span class="disabled-text">已禁用</span>
            <el-icon color="#C0C4CC"><QuestionFilled /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="preview-section">
      <h4>不同使用率状态</h4>
      <div class="status-examples">
        <div class="example-item">
          <span class="label">低使用率:</span>
          <div class="summary-item">
            <img src="/CPU.png" alt="CPU" class="icon-img" />
            <span class="value usage-low">25.3%</span>
          </div>
        </div>
        <div class="example-item">
          <span class="label">中等使用率:</span>
          <div class="summary-item">
            <img src="/Memory.png" alt="Memory" class="icon-img" />
            <span class="value usage-medium">72.8%</span>
          </div>
        </div>
        <div class="example-item">
          <span class="label">高使用率:</span>
          <div class="summary-item">
            <img src="/CPU.png" alt="CPU" class="icon-img" />
            <span class="value usage-high">89.1%</span>
          </div>
        </div>
      </div>
    </div>

    <div class="preview-section">
      <h4>图标尺寸测试</h4>
      <div class="size-test">
        <div class="size-item">
          <span>12px:</span>
          <img src="/CPU.png" alt="CPU" style="width: 12px; height: 12px;" />
          <img src="/Memory.png" alt="Memory" style="width: 12px; height: 12px;" />
        </div>
        <div class="size-item">
          <span>16px:</span>
          <img src="/CPU.png" alt="CPU" style="width: 16px; height: 16px;" />
          <img src="/Memory.png" alt="Memory" style="width: 16px; height: 16px;" />
        </div>
        <div class="size-item">
          <span>20px:</span>
          <img src="/CPU.png" alt="CPU" style="width: 20px; height: 20px;" />
          <img src="/Memory.png" alt="Memory" style="width: 20px; height: 20px;" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SuccessFilled, QuestionFilled } from '@element-plus/icons-vue'
</script>

<style scoped>
.icon-preview {
  padding: 20px;
  max-width: 800px;
}

.preview-section {
  margin-bottom: 30px;
  padding: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.preview-section h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.mock-summary {
  display: inline-block;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  padding: 8px;
  background: var(--el-bg-color);
}

/* 复制NodeSystemSummary的样式 */
.summary-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  min-width: 120px;
  justify-content: space-between;
}

.summary-trigger:hover {
  background-color: var(--el-fill-color-light);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 12px;
  min-width: 0;
  flex-shrink: 0;
}

.summary-item .icon {
  font-size: 14px;
  display: inline-block;
  width: 16px;
  text-align: center;
}

.summary-item .icon-img {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 2px;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.summary-trigger:hover .icon-img {
  opacity: 1;
}

.summary-item .value {
  font-weight: 500;
}

.summary-item .value.usage-high {
  color: var(--el-color-danger);
}

.summary-item .value.usage-medium {
  color: var(--el-color-warning);
}

.summary-item .value.usage-low {
  color: var(--el-color-success);
}

.summary-status {
  display: flex;
  align-items: center;
}

.summary-status .el-icon {
  font-size: 12px;
}

.disabled-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-disabled);
}

.disabled-text {
  font-size: 12px;
  color: var(--el-text-color-disabled);
}

.status-examples {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.example-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.example-item .label {
  min-width: 80px;
  font-weight: 500;
}

.size-test {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.size-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.size-item span {
  min-width: 40px;
  font-weight: 500;
}
</style>
