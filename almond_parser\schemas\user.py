# -*- encoding: utf-8 -*-
"""
用户相关 Pydantic 模型
"""
from typing import Optional
from pydantic import BaseModel, Field, field_validator

from .base import TimestampMixin, IDMixin


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(description="用户名", max_length=50)

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if not v or len(v.strip()) < 3:
            raise ValueError("用户名长度至少3个字符")
        return v.strip()


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(description="密码", min_length=6, max_length=100)
    is_admin: bool = Field(default=False, description="是否管理员")


class UserUpdate(BaseModel):
    """更新用户模型"""
    username: Optional[str] = Field(default=None, description="用户名", max_length=50)
    password: Optional[str] = Field(default=None, description="密码", min_length=6, max_length=100)
    is_active: Optional[bool] = Field(default=None, description="是否激活")
    is_admin: Optional[bool] = Field(default=None, description="是否管理员")


class UserResponse(UserBase, IDMixin, TimestampMixin):
    """用户响应模型"""
    is_active: bool = Field(description="是否激活")
    is_admin: bool = Field(description="是否管理员")

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str = Field(description="用户名")
    password: str = Field(description="密码")


class Token(BaseModel):
    """令牌模型"""
    access_token: str = Field(description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(description="过期时间(秒)")


class TokenData(BaseModel):
    """令牌数据模型"""
    user_id: Optional[int] = Field(default=None, description="用户ID")
    username: Optional[str] = Field(default=None, description="用户名")
