<template>
  <el-dialog
    v-model="visible"
    title="DOC文档日志"
    width="900px"
    :before-close="handleClose"
    class="document-logs-dialog"
  >
    <div class="logs-container">
      <div class="logs-header">
        <div class="logs-info">
          <span class="server-info">服务器: {{ server }}</span>
          <span class="document-info">文档ID: {{ documentId }}</span>
        </div>
        <div class="logs-actions">
          <el-button 
            type="primary" 
            :icon="Refresh" 
            @click="refreshLogs"
            :loading="loading"
            size="small"
          >
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="logs-content" ref="logsContentRef">
        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>加载日志中...</span>
        </div>
        
        <div v-else-if="logs.length === 0" class="empty-logs">
          <el-empty description="暂无日志数据" />
        </div>
        
        <div v-else class="logs-list">
          <div 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-line"
          >
            <span class="log-index">{{ index + 1 }}</span>
            <span class="log-content">{{ log }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Refresh, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getDOCDocumentLogs } from '@/api/docDocument'

const props = defineProps<{
  visible: boolean
  server: string
  documentId: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}>()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const loading = ref(false)
const logs = ref<string[]>([])
const logsContentRef = ref<HTMLElement>()

// 获取日志数据
const fetchLogs = async () => {
  if (!props.server || !props.documentId) return
  
  loading.value = true
  try {
    const response = await getDOCDocumentLogs(props.server, props.documentId)
    logs.value = response.logs || []
    
    // 滚动到底部
    await nextTick()
    if (logsContentRef.value) {
      logsContentRef.value.scrollTop = logsContentRef.value.scrollHeight
    }
  } catch (error) {
    console.error('获取DOC文档日志失败:', error)
    ElMessage.error('获取日志失败')
    logs.value = []
  } finally {
    loading.value = false
  }
}

// 刷新日志
const refreshLogs = () => {
  fetchLogs()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('close')
}

// 监听对话框显示状态
watch(visible, (newVisible) => {
  if (newVisible && props.server && props.documentId) {
    fetchLogs()
  } else if (!newVisible) {
    logs.value = []
  }
})

// 监听服务器和文档ID变化
watch([() => props.server, () => props.documentId], () => {
  if (visible.value && props.server && props.documentId) {
    fetchLogs()
  }
})
</script>

<style scoped>
.document-logs-dialog {
  border-radius: 12px;
}

.logs-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
}

.logs-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #64748b;
}

.server-info,
.document-info {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.logs-actions {
  display: flex;
  gap: 8px;
}

.logs-content {
  flex: 1;
  background: #1e293b;
  border-radius: 8px;
  padding: 16px;
  overflow-y: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #94a3b8;
  gap: 12px;
}

.loading-container .el-icon {
  font-size: 24px;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.log-line {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.log-line:last-child {
  border-bottom: none;
}

.log-index {
  color: #64748b;
  font-size: 11px;
  min-width: 40px;
  text-align: right;
  flex-shrink: 0;
  user-select: none;
}

.log-content {
  color: #e2e8f0;
  flex: 1;
  word-break: break-word;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 滚动条样式 */
.logs-content::-webkit-scrollbar {
  width: 8px;
}

.logs-content::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
}

.logs-content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 4px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .logs-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .logs-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .logs-content {
    font-size: 12px;
    padding: 12px;
  }
  
  .log-index {
    min-width: 30px;
    font-size: 10px;
  }
}
</style>
