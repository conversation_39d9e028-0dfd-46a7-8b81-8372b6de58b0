<template>
  <div class="clipboard-test-container">
    <div class="page-header">
      <h2>剪贴板功能测试</h2>
      <p>用于测试不同环境下的复制功能兼容性</p>
    </div>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>环境信息</span>
        </div>
      </template>
      
      <div class="environment-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="浏览器">
            {{ browserInfo.name }} {{ browserInfo.version }}
          </el-descriptions-item>
          <el-descriptions-item label="操作系统">
            {{ browserInfo.os }}
          </el-descriptions-item>
          <el-descriptions-item label="安全上下文">
            {{ isSecureContext ? '是 (HTTPS)' : '否 (HTTP)' }}
          </el-descriptions-item>
          <el-descriptions-item label="现代 Clipboard API">
            {{ isModernClipboardSupported ? '支持' : '不支持' }}
          </el-descriptions-item>
          <el-descriptions-item label="传统复制方法">
            {{ isLegacySupported ? '支持' : '不支持' }}
          </el-descriptions-item>
          <el-descriptions-item label="整体支持">
            {{ isClipboardSupported ? '支持' : '不支持' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>复制功能测试</span>
        </div>
      </template>
      
      <div class="test-section">
        <h3>测试文本</h3>
        <el-input
          v-model="testText"
          type="textarea"
          :rows="4"
          placeholder="输入要测试复制的文本..."
        />
        
        <div class="button-group">
          <el-button 
            type="primary" 
            @click="testCopy"
            :loading="copying"
          >
            <el-icon><DocumentCopy /></el-icon>
            测试复制
          </el-button>
          
          <el-button 
            type="success" 
            @click="testModernCopy"
            :disabled="!isModernClipboardSupported"
            :loading="copyingModern"
          >
            <el-icon><DocumentCopy /></el-icon>
            测试现代 API
          </el-button>
          
          <el-button 
            type="warning" 
            @click="testLegacyCopy"
            :loading="copyingLegacy"
          >
            <el-icon><DocumentCopy /></el-icon>
            测试传统方法
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>测试结果</span>
        </div>
      </template>
      
      <div class="results-section">
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :type="result.success ? 'success' : 'danger'"
            :timestamp="result.timestamp"
          >
            <div class="result-item">
              <div class="result-title">{{ result.method }}</div>
              <div class="result-status">
                {{ result.success ? '✅ 成功' : '❌ 失败' }}
              </div>
              <div v-if="result.error" class="result-error">
                错误: {{ result.error }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        
        <el-button 
          v-if="testResults.length > 0"
          type="info" 
          size="small" 
          @click="clearResults"
        >
          清空结果
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import { 
  copyToClipboard, 
  isClipboardSupported, 
  isModernClipboardSupported 
} from '@/utils/clipboard'

interface TestResult {
  method: string
  success: boolean
  error?: string
  timestamp: string
}

interface BrowserInfo {
  name: string
  version: string
  os: string
}

const testText = ref('这是一段测试文本，用于验证复制功能是否正常工作。\n\n包含多行内容和特殊字符：@#$%^&*()_+{}|:"<>?[]\\;\',./')
const copying = ref(false)
const copyingModern = ref(false)
const copyingLegacy = ref(false)
const testResults = ref<TestResult[]>([])

const isSecureContext = computed(() => window.isSecureContext)
const isLegacySupported = computed(() => !!document.execCommand)
const browserInfo = ref<BrowserInfo>({ name: '', version: '', os: '' })

// 获取浏览器信息
const getBrowserInfo = (): BrowserInfo => {
  const userAgent = navigator.userAgent
  let name = 'Unknown'
  let version = 'Unknown'
  let os = 'Unknown'

  // 检测浏览器
  if (userAgent.includes('Chrome')) {
    name = 'Chrome'
    const match = userAgent.match(/Chrome\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
  } else if (userAgent.includes('Firefox')) {
    name = 'Firefox'
    const match = userAgent.match(/Firefox\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
  } else if (userAgent.includes('Safari')) {
    name = 'Safari'
    const match = userAgent.match(/Version\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
  } else if (userAgent.includes('Edge')) {
    name = 'Edge'
    const match = userAgent.match(/Edge\/(\d+\.\d+)/)
    version = match ? match[1] : 'Unknown'
  }

  // 检测操作系统
  if (userAgent.includes('Windows')) {
    os = 'Windows'
  } else if (userAgent.includes('Mac')) {
    os = 'macOS'
  } else if (userAgent.includes('Linux')) {
    os = 'Linux'
  } else if (userAgent.includes('Android')) {
    os = 'Android'
  } else if (userAgent.includes('iOS')) {
    os = 'iOS'
  }

  return { name, version, os }
}

// 添加测试结果
const addResult = (method: string, success: boolean, error?: string) => {
  testResults.value.unshift({
    method,
    success,
    error,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 测试通用复制方法
const testCopy = async () => {
  copying.value = true
  try {
    const success = await copyToClipboard(testText.value, { showMessage: false })
    addResult('通用复制方法', success)
    if (success) {
      ElMessage.success('复制成功')
    } else {
      ElMessage.error('复制失败')
    }
  } catch (error) {
    addResult('通用复制方法', false, String(error))
    ElMessage.error('复制失败')
  } finally {
    copying.value = false
  }
}

// 测试现代 API
const testModernCopy = async () => {
  copyingModern.value = true
  try {
    await navigator.clipboard.writeText(testText.value)
    addResult('现代 Clipboard API', true)
    ElMessage.success('现代 API 复制成功')
  } catch (error) {
    addResult('现代 Clipboard API', false, String(error))
    ElMessage.error('现代 API 复制失败')
  } finally {
    copyingModern.value = false
  }
}

// 测试传统方法
const testLegacyCopy = async () => {
  copyingLegacy.value = true
  try {
    const textArea = document.createElement('textarea')
    textArea.value = testText.value
    textArea.style.position = 'fixed'
    textArea.style.left = '-9999px'
    textArea.style.top = '0'
    document.body.appendChild(textArea)
    textArea.select()
    textArea.setSelectionRange(0, textArea.value.length)
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    
    addResult('传统 execCommand 方法', successful)
    if (successful) {
      ElMessage.success('传统方法复制成功')
    } else {
      ElMessage.error('传统方法复制失败')
    }
  } catch (error) {
    addResult('传统 execCommand 方法', false, String(error))
    ElMessage.error('传统方法复制失败')
  } finally {
    copyingLegacy.value = false
  }
}

// 清空结果
const clearResults = () => {
  testResults.value = []
}

onMounted(() => {
  browserInfo.value = getBrowserInfo()
})
</script>

<style scoped>
.clipboard-test-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.test-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.environment-info {
  margin-bottom: 16px;
}

.test-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.button-group {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.results-section {
  min-height: 200px;
}

.result-item {
  padding: 8px 0;
}

.result-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.result-status {
  color: #606266;
  margin-bottom: 4px;
}

.result-error {
  color: #f56c6c;
  font-size: 12px;
  background: #fef0f0;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
}
</style>
