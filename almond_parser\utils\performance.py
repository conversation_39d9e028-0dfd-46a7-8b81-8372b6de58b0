# -*- encoding: utf-8 -*-
"""
性能监控工具
"""
import time
import asyncio
from typing import Dict, Any
from fastapi import Request
from loguru import logger


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.slow_requests = []
        self.request_stats = {}
    
    async def monitor_request(self, request: Request, call_next):
        """监控请求性能"""
        start_time = time.time()
        
        # 记录各个阶段的时间
        timings = {
            "start": start_time,
            "auth_start": None,
            "auth_end": None,
            "db_start": None,
            "db_end": None,
            "response_start": None,
            "response_end": None
        }
        
        # 将时间记录器添加到请求状态
        request.state.timings = timings
        
        try:
            response = await call_next(request)
            end_time = time.time()
            total_time = end_time - start_time
            
            # 记录慢请求
            if total_time > 1.0:  # 超过1秒的请求
                slow_request = {
                    "method": request.method,
                    "path": request.url.path,
                    "total_time": total_time,
                    "timings": timings,
                    "timestamp": start_time
                }
                self.slow_requests.append(slow_request)
                
                # 只保留最近100个慢请求
                if len(self.slow_requests) > 100:
                    self.slow_requests.pop(0)
                
                # 详细日志记录
                logger.warning(
                    f"🐌 慢请求检测: {request.method} {request.url.path} "
                    f"耗时: {total_time:.3f}s"
                )
            
            # 更新统计信息
            path_key = f"{request.method} {request.url.path}"
            if path_key not in self.request_stats:
                self.request_stats[path_key] = {
                    "count": 0,
                    "total_time": 0,
                    "min_time": float('inf'),
                    "max_time": 0,
                    "avg_time": 0
                }
            
            stats = self.request_stats[path_key]
            stats["count"] += 1
            stats["total_time"] += total_time
            stats["min_time"] = min(stats["min_time"], total_time)
            stats["max_time"] = max(stats["max_time"], total_time)
            stats["avg_time"] = stats["total_time"] / stats["count"]
            
            return response
            
        except Exception as e:
            end_time = time.time()
            total_time = end_time - start_time
            
            logger.error(
                f"❌ 请求异常: {request.method} {request.url.path} "
                f"耗时: {total_time:.3f}s, 错误: {str(e)}"
            )
            raise
    
    def get_slow_requests(self, limit: int = 10) -> list:
        """获取最近的慢请求"""
        return sorted(
            self.slow_requests[-limit:], 
            key=lambda x: x["total_time"], 
            reverse=True
        )
    
    def get_request_stats(self) -> Dict[str, Any]:
        """获取请求统计信息"""
        return self.request_stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.request_stats.clear()
        self.slow_requests.clear()


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def log_timing(request: Request, stage: str):
    """记录时间点"""
    if hasattr(request.state, 'timings'):
        request.state.timings[stage] = time.time()


async def add_performance_headers(request: Request, response, total_time: float):
    """添加性能相关的响应头"""
    response.headers["X-Process-Time"] = f"{total_time:.3f}"
    response.headers["X-Server-Time"] = str(int(time.time()))
    
    # 如果有详细时间记录，添加到响应头
    if hasattr(request.state, 'timings'):
        timings = request.state.timings
        if timings.get("db_start") and timings.get("db_end"):
            db_time = timings["db_end"] - timings["db_start"]
            response.headers["X-DB-Time"] = f"{db_time:.3f}"
        
        if timings.get("auth_start") and timings.get("auth_end"):
            auth_time = timings["auth_end"] - timings["auth_start"]
            response.headers["X-Auth-Time"] = f"{auth_time:.3f}"
