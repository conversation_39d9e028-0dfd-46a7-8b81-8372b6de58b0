<template>
  <div class="dashboard">
    <!-- 系统运行状态 -->
    <div class="system-status-section">
      <h2 class="section-title">系统运行状态</h2>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="status-card nodes-status">
            <div class="status-content">
              <div class="status-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="status-data">
                <div class="status-number">{{ systemStatus.onlineNodes }}/{{ systemStatus.totalNodes }}</div>
                <div class="status-label">MinerU 在线节点</div>
                <div class="status-detail">
                  <span class="detail-item">文档: {{ systemStatus.documentNodes }}</span>
                  <span class="detail-item">知识库: {{ systemStatus.knowledgeNodes }}</span>
                  <span class="detail-item">通用: {{ systemStatus.universalNodes }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card queue-status">
            <div class="status-content">
              <div class="status-icon">
                <el-icon><List /></el-icon>
              </div>
              <div class="status-data">
                <div class="status-number">{{ systemStatus.queueTasks }}</div>
                <div class="status-label">队列中任务</div>
                <div class="status-detail">
                  <span class="detail-item">处理中: {{ systemStatus.processingTasks }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card success-status">
            <div class="status-content">
              <div class="status-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="status-data">
                <div class="status-number">{{ systemStatus.successRate }}%</div>
                <div class="status-label">今日成功率</div>
                <div class="status-detail">
                  <span class="detail-item">成功: {{ systemStatus.successTasks }}</span>
                  <span class="detail-item">失败: {{ systemStatus.failedTasks }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card load-status">
            <div class="status-content">
              <div class="status-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="status-data">
                <div class="status-number">{{ systemStatus.avgLoad }}%</div>
                <div class="status-label">平均负载</div>
                <div class="status-detail">
                  <el-progress :percentage="systemStatus.avgLoad" :show-text="false" />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 今日概况 -->
    <div class="today-overview-section">
      <h2 class="section-title">今日概况</h2>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon">
                <el-icon><Upload /></el-icon>
              </div>
              <div class="overview-data">
                <div class="overview-number">{{ todayStats.submittedDocs }}</div>
                <div class="overview-label">今日提交文档数</div>
                <div class="overview-trend">
                  <el-tag :type="todayStats.docsTrend >= 0 ? 'success' : 'danger'" size="small">
                    {{ todayStats.docsTrend >= 0 ? '+' : '' }}{{ todayStats.docsTrend }}
                  </el-tag>
                  <span class="trend-label">较昨日</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="overview-data">
                <div class="overview-number">{{ todayStats.completedDocs }}</div>
                <div class="overview-label">今日完成解析数</div>
                <div class="overview-trend">
                  <el-tag :type="todayStats.completedTrend >= 0 ? 'success' : 'danger'" size="small">
                    {{ todayStats.completedTrend >= 0 ? '+' : '' }}{{ todayStats.completedTrend }}
                  </el-tag>
                  <span class="trend-label">较昨日</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="overview-data">
                <div class="overview-number">{{ todayStats.avgDuration }}</div>
                <div class="overview-label">平均耗时</div>
                <div class="overview-trend">
                  <el-tag :type="todayStats.durationTrend <= 0 ? 'success' : 'warning'" size="small">
                    {{ todayStats.durationTrend >= 0 ? '+' : '' }}{{ todayStats.durationTrend }}s
                  </el-tag>
                  <span class="trend-label">较昨日</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 历史趋势 -->
    <div class="history-trends-section">
      <h2 class="section-title">历史趋势</h2>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>最近任务趋势</span>
                <el-radio-group v-model="trendDays" size="small" @change="updateTaskTrendChart">
                  <el-radio-button :label="7">7天</el-radio-button>
                  <el-radio-button :label="30">30天</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div ref="taskTrendChart" class="chart-container" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="chart-card">
            <template #header>
              <span>节点负载趋势</span>
            </template>
            <div ref="nodeLoadChart" class="chart-container" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 错误摘要 -->
    <div class="error-summary-section">
      <h2 class="section-title">错误摘要</h2>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="error-list-card">
            <template #header>
              <div class="card-header">
                <span>最近异常任务</span>
                <el-button size="small" @click="refreshErrorList">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </template>
            <div class="error-list">
              <div v-if="recentErrors.length === 0" class="no-errors">
                <el-empty description="暂无异常任务" />
              </div>
              <div v-else>
                <div v-for="error in recentErrors" :key="error.id" class="error-item">
                  <div class="error-info">
                    <div class="error-header">
                      <el-tag type="danger" size="small">{{ error.errorType }}</el-tag>
                      <span class="error-time">{{ formatTime(error.timestamp) }}</span>
                    </div>
                    <div class="error-details">
                      <div class="error-file">
                        <el-icon><Document /></el-icon>
                        <span>{{ error.filename }}</span>
                      </div>
                      <div class="error-message">{{ error.message }}</div>
                      <div class="error-node" v-if="error.nodeName">
                        <el-icon><Connection /></el-icon>
                        <span>节点: {{ error.nodeName }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="error-actions">
                    <el-button size="small" @click="retryTask(error.taskId)">重试</el-button>
                    <el-button size="small" @click="viewErrorDetail(error)">详情</el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="chart-card">
            <template #header>
              <span>Top5 错误原因分布</span>
            </template>
            <div ref="errorDistributionChart" class="chart-container" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷入口 -->
    <div class="quick-actions-section">
      <h2 class="section-title">快捷入口</h2>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="quick-action-card upload-card">
            <div class="quick-action-content">
              <div class="action-icon">
                <el-icon><Upload /></el-icon>
              </div>
              <div class="action-info">
                <h3>上传文档</h3>
                <p>快速上传文档进行解析</p>
                <el-button type="primary" @click="goToUpload">
                  立即上传
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="quick-action-card config-card">
            <div class="quick-action-content">
              <div class="action-icon">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="action-info">
                <h3>系统配置</h3>
                <p>管理节点、API密钥等配置</p>
                <el-button-group>
                  <el-button @click="goToNodes">节点管理</el-button>
                  <el-button @click="goToApiKeys">API密钥</el-button>
                </el-button-group>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="quick-action-card logs-card">
            <div class="quick-action-content">
              <div class="action-icon">
                <el-icon><View /></el-icon>
              </div>
              <div class="action-info">
                <h3>查看日志</h3>
                <p>查看系统运行日志和统计</p>
                <el-button-group>
                  <el-button @click="goToTasks">解析任务</el-button>
                  <el-button @click="goToStatistics">统计分析</el-button>
                </el-button-group>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="showErrorDetail"
      title="错误详情"
      width="600px"
    >
      <div v-if="selectedError" class="error-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedError.taskId }}</el-descriptions-item>
          <el-descriptions-item label="文件名">{{ selectedError.filename }}</el-descriptions-item>
          <el-descriptions-item label="错误类型">{{ selectedError.errorType }}</el-descriptions-item>
          <el-descriptions-item label="发生时间">{{ formatTime(selectedError.timestamp) }}</el-descriptions-item>
          <el-descriptions-item label="处理节点" v-if="selectedError.nodeName">{{ selectedError.nodeName }}</el-descriptions-item>
          <el-descriptions-item label="错误信息" :span="2">
            <pre class="error-message-detail">{{ selectedError.message }}</pre>
          </el-descriptions-item>
          <el-descriptions-item label="堆栈信息" :span="2" v-if="selectedError.stackTrace">
            <pre class="stack-trace">{{ selectedError.stackTrace }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showErrorDetail = false">关闭</el-button>
        <el-button type="primary" @click="retryTask(selectedError?.taskId)">重试任务</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Connection,
  List,
  CircleCheck,
  Monitor,
  Upload,
  Document,
  Timer,
  Refresh,
  Setting,
  View
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { dashboardApi } from '@/api/dashboard'
import type { RecentError } from '@/api/dashboard'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const trendDays = ref(7)
const showErrorDetail = ref(false)
const selectedError = ref<any>(null)

// 图表实例
const taskTrendChart = ref<HTMLElement>()
const nodeLoadChart = ref<HTMLElement>()
const errorDistributionChart = ref<HTMLElement>()
let taskTrendChartInstance: echarts.ECharts | null = null
let nodeLoadChartInstance: echarts.ECharts | null = null
let errorDistributionChartInstance: echarts.ECharts | null = null

// 系统运行状态数据
const systemStatus = reactive({
  onlineNodes: 0,
  totalNodes: 0,
  documentNodes: 0,
  knowledgeNodes: 0,
  universalNodes: 0,
  queueTasks: 0,
  processingTasks: 0,
  successRate: 0,
  successTasks: 0,
  failedTasks: 0,
  avgLoad: 0
})

// 今日概况数据
const todayStats = reactive({
  submittedDocs: 0,
  docsTrend: 0,
  completedDocs: 0,
  completedTrend: 0,
  avgDuration: '0s',
  durationTrend: 0
})

// 最近错误列表
const recentErrors = ref<RecentError[]>([])

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const loadDashboardData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadSystemStatus(),
      loadTodayStats(),
      loadRecentErrors(),
      updateTaskTrendChart(),
      updateNodeLoadChart()
    ])
  } catch (error) {
    console.error('加载Dashboard数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadSystemStatus = async () => {
  try {
    const response = await dashboardApi.getSystemStatus()
    Object.assign(systemStatus, response)
  } catch (error) {
    console.error('加载系统状态失败:', error)
    // 使用模拟数据作为fallback
    Object.assign(systemStatus, {
      onlineNodes: 0,
      totalNodes: 0,
      documentNodes: 0,
      knowledgeNodes: 0,
      universalNodes: 0,
      queueTasks: 0,
      processingTasks: 0,
      successRate: 0,
      successTasks: 0,
      failedTasks: 0,
      avgLoad: 0
    })
  }
}

const loadTodayStats = async () => {
  try {
    const response = await dashboardApi.getTodayStats()
    Object.assign(todayStats, response)
  } catch (error) {
    console.error('加载今日统计失败:', error)
    // 使用模拟数据作为fallback
    Object.assign(todayStats, {
      submittedDocs: 0,
      docsTrend: 0,
      completedDocs: 0,
      completedTrend: 0,
      avgDuration: '',
      durationTrend: 0
    })
  }
}

const loadRecentErrors = async () => {
  try {
    recentErrors.value = await dashboardApi.getRecentErrors(10)
  } catch (error) {
    console.error('加载错误列表失败:', error)
    // 使用模拟数据作为fallback
    recentErrors.value = [
      {
        id: 1,
        taskId: 'task_001',
        filename: 'document1.pdf',
        errorType: '解析超时',
        message: '文档解析超过最大时间限制',
        nodeName: 'node-001',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        stackTrace: 'TimeoutError: Task timeout after 300 seconds...'
      },
      {
        id: 2,
        taskId: 'task_002',
        filename: 'report.docx',
        errorType: '格式错误',
        message: '不支持的文档格式',
        nodeName: 'node-002',
        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString()
      }
    ]
  }
}

const initCharts = async () => {
  await nextTick()

  // 初始化任务趋势图表
  if (taskTrendChart.value) {
    taskTrendChartInstance = echarts.init(taskTrendChart.value)
    await updateTaskTrendChart()
  }

  // 初始化节点负载图表
  if (nodeLoadChart.value) {
    nodeLoadChartInstance = echarts.init(nodeLoadChart.value)
    await updateNodeLoadChart()
  }

  // 初始化错误分布图表
  if (errorDistributionChart.value) {
    errorDistributionChartInstance = echarts.init(errorDistributionChart.value)
    updateErrorDistributionChart()
  }
}

const updateTaskTrendChart = async () => {
  if (!taskTrendChartInstance) return

  try {
    const trendData = await dashboardApi.getTaskTrend(trendDays.value)
    
    // 格式化日期，只显示月/日
    const dates = trendData.map(item => {
      const date = new Date(item.date)
      return `${date.getMonth() + 1}/${date.getDate()}`
    })
    const successData = trendData.map(item => item.successCount)
    const failedData = trendData.map(item => item.failedCount)

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params: any[]) {
          const date = params[0].axisValue
          let html = `${date}<br/>`
          params.forEach(param => {
            const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
            html += `${marker}${param.seriesName}: ${param.value}<br/>`
          })
          return html
        }
      },
      legend: {
        data: ['成功任务', '失败任务']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          interval: Math.floor(dates.length / 8), // 根据数据量自动计算间隔
          rotate: dates.length > 15 ? 45 : 0 // 数据量大时自动旋转
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        name: '任务数量',
        minInterval: 1 // 保证坐标轴刻度为整数
      },
      series: [
        {
          name: '成功任务',
          type: 'line',
          data: successData,
          smooth: true,
          itemStyle: { color: '#67C23A' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(103, 194, 58, 0.2)'
              }, {
                offset: 1,
                color: 'rgba(103, 194, 58, 0)'
              }]
            }
          }
        },
        {
          name: '失败任务',
          type: 'line',
          data: failedData,
          smooth: true,
          itemStyle: { color: '#F56C6C' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(245, 108, 108, 0.2)'
              }, {
                offset: 1,
                color: 'rgba(245, 108, 108, 0)'
              }]
            }
          }
        }
      ]
    }

    taskTrendChartInstance.setOption(option)
  } catch (error) {
    console.error('加载任务趋势数据失败:', error)
    ElMessage.error('加载任务趋势数据失败')
  }
}

const updateNodeLoadChart = async () => {
  if (!nodeLoadChartInstance) return

  try {
    const { times, loadData } = await dashboardApi.getNodeLoad(24)
    
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any[]) {
          const time = params[0].axisValue
          const load = params[0].value
          return `${time}<br/>负载: ${load}%`
        }
      },
      xAxis: {
        type: 'category',
        data: times.map(time => {
          const date = new Date(time)
          return `${date.getHours().toString().padStart(2, '0')}:00`
        }),
        axisLabel: {
          interval: Math.floor(times.length / 8),
          rotate: 0
        }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      series: [
        {
          name: '平均负载',
          type: 'line',
          data: loadData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(64, 158, 255, 0.2)'
              }, {
                offset: 1,
                color: 'rgba(64, 158, 255, 0)'
              }]
            }
          },
          itemStyle: { color: '#409EFF' }
        }
      ]
    }

    nodeLoadChartInstance.setOption(option)
  } catch (error) {
    console.error('加载节点负载数据失败:', error)
    ElMessage.error('加载节点负载数据失败')
  }
}

const updateErrorDistributionChart = () => {
  if (!errorDistributionChartInstance) return

  // 模拟错误分布数据
  const errorData = [
    { value: 35, name: '解析超时' },
    { value: 25, name: '格式错误' },
    { value: 20, name: '网络异常' },
    { value: 12, name: '内存不足' },
    { value: 8, name: '其他错误' }
  ]

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '错误类型',
        type: 'pie',
        radius: ['40%', '70%'],
        data: errorData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  errorDistributionChartInstance.setOption(option)
}

// 工具方法
const formatTime = (time: Date | string) => {
  const date = new Date(time)
  return date.toLocaleString()
}

// 事件处理方法
const refreshErrorList = async () => {
  await loadRecentErrors()
  ElMessage.success('错误列表已刷新')
}

const retryTask = async (taskId: string) => {
  try {
    await dashboardApi.retryTask(taskId)
    ElMessage.success(`任务 ${taskId} 已重新提交`)
    await loadDashboardData()
  } catch (error) {
    console.error('重试任务失败:', error)
    ElMessage.error('重试任务失败')
  }
}

const viewErrorDetail = (error: any) => {
  selectedError.value = error
  showErrorDetail.value = true
}

// 快捷入口方法
const goToUpload = () => {
  router.push('/dashboard/documents')
}

const goToNodes = () => {
  router.push('/dashboard/mineru-nodes')
}

const goToApiKeys = () => {
  router.push('/dashboard/api-keys')
}

const goToTasks = () => {
  router.push('/dashboard/parse-tasks')
}

const goToStatistics = () => {
  router.push('/dashboard/statistics')
}

// 生命周期
onMounted(async () => {
  await loadDashboardData()
  await initCharts()

  // 设置定时刷新
  refreshTimer = setInterval(() => {
    loadDashboardData()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }

  // 销毁图表实例
  if (taskTrendChartInstance) {
    taskTrendChartInstance.dispose()
  }
  if (nodeLoadChartInstance) {
    nodeLoadChartInstance.dispose()
  }
  if (errorDistributionChartInstance) {
    errorDistributionChartInstance.dispose()
  }
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 系统运行状态样式 */
.system-status-section {
  margin-bottom: 30px;
}

.status-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.status-icon {
  font-size: 32px;
  margin-right: 15px;
  color: #409EFF;
}

.nodes-status .status-icon {
  color: #67C23A;
}

.queue-status .status-icon {
  color: #E6A23C;
}

.success-status .status-icon {
  color: #67C23A;
}

.load-status .status-icon {
  color: #409EFF;
}

.status-data {
  flex: 1;
}

.status-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.status-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.status-detail {
  display: flex;
  gap: 10px;
}

.detail-item {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 今日概况样式 */
.today-overview-section {
  margin-bottom: 30px;
}

.overview-card {
  height: 140px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.overview-icon {
  font-size: 36px;
  margin-right: 15px;
  color: #409EFF;
}

.overview-data {
  flex: 1;
}

.overview-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.overview-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.overview-trend {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trend-label {
  font-size: 12px;
  color: #909399;
}

/* 历史趋势样式 */
.history-trends-section {
  margin-bottom: 30px;
}

.chart-card {
  height: 380px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
}

/* 错误摘要样式 */
.error-summary-section {
  margin-bottom: 30px;
}

.error-list-card {
  height: 380px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-list {
  max-height: 300px;
  overflow-y: auto;
}

.error-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s ease;
}

.error-item:hover {
  background-color: #f5f7fa;
}

.error-item:last-child {
  border-bottom: none;
}

.error-info {
  flex: 1;
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.error-time {
  font-size: 12px;
  color: #909399;
}

.error-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.error-file,
.error-node {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #606266;
}

.error-message {
  font-size: 13px;
  color: #303133;
  margin: 5px 0;
}

.error-actions {
  display: flex;
  gap: 8px;
  margin-left: 15px;
}

/* 快捷入口样式 */
.quick-actions-section {
  margin-bottom: 30px;
}

.quick-action-card {
  height: 160px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 20px;
}

.action-icon {
  font-size: 40px;
  margin-bottom: 15px;
  color: #409EFF;
}

.upload-card .action-icon {
  color: #67C23A;
}

.config-card .action-icon {
  color: #E6A23C;
}

.logs-card .action-icon {
  color: #909399;
}

.action-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.action-info p {
  margin: 0 0 15px 0;
  font-size: 12px;
  color: #606266;
}

/* 错误详情对话框样式 */
.error-detail {
  max-height: 400px;
  overflow-y: auto;
}

.error-message-detail,
.stack-trace {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.no-errors {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .status-card,
  .overview-card,
  .quick-action-card {
    height: auto;
    min-height: 100px;
  }

  .status-content,
  .overview-content {
    flex-direction: column;
    text-align: center;
  }

  .status-icon,
  .overview-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .chart-card {
    height: 300px;
  }

  .error-list-card {
    height: 300px;
  }
}
</style>
