"""描述信息

Revision ID: 329d154bf4e2
Revises: 
Create Date: 2025-07-31 16:01:04.153223

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '329d154bf4e2'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('remarks', sa.Text(), nullable=True, comment='备注信息'))
    op.add_column('documents', sa.Column('extra_info', sa.JSON(), nullable=True, comment='扩展信息(JSON格式)'))
    op.alter_column('mineru_nodes', 'sglang_status',
               existing_type=mysql.ENUM('ONLINE', 'OFFLINE', 'ERROR', 'UNKNOWN', 'RESTARTING'),
               server_default=None,
               existing_comment='SGLang服务状态',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_consecutive_failures',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_comment='SGLang连续失败次数',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_restart_count',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_comment='SGLang重启次数',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_current_cycle_restarts',
               existing_type=mysql.INTEGER(),
               server_default=None,
               existing_comment='当前重启周期的重启次数',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_alert_sent',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=None,
               existing_comment='是否已发送告警',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('mineru_nodes', 'sglang_alert_sent',
               existing_type=mysql.TINYINT(display_width=1),
               server_default=sa.text("'0'"),
               existing_comment='是否已发送告警',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_current_cycle_restarts',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               existing_comment='当前重启周期的重启次数',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_restart_count',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               existing_comment='SGLang重启次数',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_consecutive_failures',
               existing_type=mysql.INTEGER(),
               server_default=sa.text("'0'"),
               existing_comment='SGLang连续失败次数',
               existing_nullable=True)
    op.alter_column('mineru_nodes', 'sglang_status',
               existing_type=mysql.ENUM('ONLINE', 'OFFLINE', 'ERROR', 'UNKNOWN', 'RESTARTING'),
               server_default=sa.text("'UNKNOWN'"),
               existing_comment='SGLang服务状态',
               existing_nullable=True)
    op.drop_column('documents', 'extra_info')
    op.drop_column('documents', 'remarks')
    # ### end Alembic commands ###
