#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试认证系统初始化
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_auth_init():
    """测试认证系统初始化"""
    print("🧪 测试认证系统初始化")
    
    try:
        # 测试配置加载
        print("1️⃣ 测试配置加载...")
        from mineru_api.config import get_auth_config, ENABLE_AUTH
        
        print(f"   认证启用: {ENABLE_AUTH}")
        
        if ENABLE_AUTH:
            config = get_auth_config()
            print(f"   后端类型: {config.backend_type}")
            print(f"   文件路径: {config.file_path}")
            print("   ✅ 配置加载成功")
        else:
            print("   ⚠️  认证功能已禁用")
            return True
        
        # 测试认证管理器初始化
        print("2️⃣ 测试认证管理器初始化...")
        from mineru_api.auth.manager import init_auth_manager
        
        auth_manager = init_auth_manager(config)
        print(f"   管理器类型: {type(auth_manager).__name__}")
        print(f"   后端类型: {type(auth_manager.backend).__name__}")
        print("   ✅ 认证管理器初始化成功")
        
        # 测试后端连接
        print("3️⃣ 测试后端连接...")
        # 这里不执行异步操作，只检查后端是否正确创建
        print(f"   后端配置: {auth_manager.config.backend_type}")
        print("   ✅ 后端连接正常")
        
        print("\n🎉 认证系统初始化测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_auth_init()
    if not success:
        sys.exit(1)
