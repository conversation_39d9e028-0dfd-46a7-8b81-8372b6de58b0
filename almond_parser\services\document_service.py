# -*- encoding: utf-8 -*-
"""
文档服务
"""
import uuid
import json
from datetime import datetime
from typing import List, Optional, Dict, Any

import aiohttp
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, desc, asc
from loguru import logger

from almond_parser.db.models import Document, DocumentLog, BatchTask, DocumentStatus
from almond_parser.db.models.mineru_node import MinerUNode
from almond_parser.schemas.document import (
    DocumentResponse, DocumentQueryParams, DocumentListResponse,
    BatchStatusResponse, DocumentLogLine, NodeInfo
)
from almond_parser.schemas.base import PaginationResponse
from almond_parser.config import settings
from almond_parser.tools.get_pdf_page import check_pdf_page_count


class DocumentService:
    """文档服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_document(
        self,
        batch_id: str,
        user_id: str,
        filename: str,
        file_size: int,
        file_type: str,
        file_path: str,
        parse_config: Optional[str] = None,
        parse_mode: Optional[str] = None,
        service_type: Optional[str] = None,
        max_retries: Optional[int] = None,
        remarks: Optional[str] = None,
        extra_info: Optional[Dict[str, Any]] = None
    ) -> DocumentResponse:
        """创建文档记录"""
        try:
            # 生成文档ID
            document_id = str(uuid.uuid4()).replace("-", "")

            # 解析配置
            config_data = None
            if parse_config:
                try:
                    config_data = json.loads(parse_config)
                except json.JSONDecodeError:
                    logger.warning(f"解析配置JSON格式错误: {parse_config}")

            # 如果PDF的页数大于500，直接标记失败
            is_valid, page_count, error = check_pdf_page_count(file_path, 500)
            if not error:
                error_message = error
                status = DocumentStatus.FAILED
            elif not is_valid:
                status = DocumentStatus.FAILED
                error_message = "PDF页数超过500页"
            else:
                status = DocumentStatus.UPLOADED
                error_message = ""

            # 创建文档记录
            document = Document(
                document_id=document_id,
                batch_id=batch_id,
                file_name=filename,
                file_size=file_size,
                file_type=file_type,
                file_path=file_path,
                user_id=user_id,
                status=status,
                progress=0,
                result_data=config_data,
                original_parse_mode=parse_mode,
                current_parse_mode=parse_mode,
                max_retries=max_retries or 2,
                remarks=remarks,
                extra_info=extra_info,
                pdf_page_count=page_count,
                error_message=error_message
            )

            self.db.add(document)
            await self.db.commit()
            await self.db.refresh(document)

            # 记录创建日志
            await self.add_document_log(
                document_id, "INFO",
                f"文档创建成功: {filename} ({file_size} bytes)"
            )

            logger.info(f"创建文档记录成功: {document_id}")
            return DocumentResponse.model_validate(document)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建文档记录失败: {e}")
            raise

    async def get_documents(self, query_params: DocumentQueryParams) -> DocumentListResponse:
        """获取文档列表"""
        try:
            # 构建查询条件
            conditions = []

            if query_params.user_id:
                conditions.append(Document.user_id == query_params.user_id)
            if query_params.document_id:
                conditions.append(Document.document_id.like(f"%{query_params.document_id}%"))
            if query_params.batch_id:
                conditions.append(Document.batch_id == query_params.batch_id)
            if query_params.status:
                conditions.append(Document.status == query_params.status)
            if query_params.file_name:
                conditions.append(Document.file_name.like(f"%{query_params.file_name}%"))

            # 构建基础查询
            base_query = select(Document).where(and_(*conditions))

            # 获取总数
            count_query = select(func.count()).select_from(
                base_query.subquery()
            )
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 添加排序
            if query_params.sort_by == "created_at":
                sort_column = Document.created_at
            elif query_params.sort_by == "updated_at":
                sort_column = Document.updated_at
            elif query_params.sort_by == "file_name":
                sort_column = Document.file_name
            else:
                sort_column = Document.created_at

            if query_params.sort_order == "desc":
                base_query = base_query.order_by(desc(sort_column))
            else:
                base_query = base_query.order_by(asc(sort_column))

            # 添加分页
            base_query = base_query.offset(query_params.skip).limit(query_params.limit)

            # 执行查询
            result = await self.db.execute(base_query)
            documents = result.scalars().all()

            # 转换为响应模型并关联节点信息
            items = []
            for doc in documents:
                doc_response = DocumentResponse.model_validate(doc)

                # 如果有节点ID，查询节点信息
                if doc.node_id:
                    node_result = await self.db.execute(
                        select(MinerUNode).where(MinerUNode.id == doc.node_id)
                    )
                    node = node_result.scalar_one_or_none()
                    if node:
                        doc_response.node_info = NodeInfo(
                            id=node.id,
                            name=node.name,
                            host=node.host,
                            port=node.port,
                            status=node.status.value,
                            parse_mode=node.parse_mode.value,
                            service_type=node.service_type.value
                        )

                items.append(doc_response)

            # 创建分页信息
            pagination = PaginationResponse.create(
                total=total,
                page=query_params.page,
                page_size=query_params.page_size
            )

            return DocumentListResponse(
                success=True,
                message="获取文档列表成功",
                items=items,
                pagination=pagination
            )

        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            raise

    async def get_document(self, document_id: str, user_id: str=None) -> Optional[DocumentResponse]:
        """获取单个文档"""
        try:
            conditions = [Document.document_id == document_id]

            # 如果提供了user_id，则进行权限检查
            if user_id:
                conditions.append(Document.user_id == user_id)

            result = await self.db.execute(
                select(Document).where(and_(*conditions))
            )
            document = result.scalar_one_or_none()

            if document:
                doc_response = DocumentResponse.model_validate(document)

                # 如果有节点ID，查询节点信息
                if document.node_id:
                    node_result = await self.db.execute(
                        select(MinerUNode).where(MinerUNode.id == document.node_id)
                    )
                    node = node_result.scalar_one_or_none()
                    if node:
                        doc_response.node_info = NodeInfo(
                            id=node.id,
                            name=node.name,
                            host=node.host,
                            port=node.port,
                            status=node.status.value,
                            parse_mode=node.parse_mode.value,
                            service_type=node.service_type.value
                        )

                return doc_response
            return None

        except Exception as e:
            logger.error(f"获取文档失败: {e}")
            raise

    async def get_document_by_task_id(self, task_id: str) -> Optional[Document]:
        """根据task_id获取文档（返回原始Document对象）"""
        try:
            result = await self.db.execute(
                select(Document).where(Document.task_id == task_id)
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"根据task_id获取文档失败: {e}")
            raise

    async def delete_document(self, document_id: str, user_id: str) -> bool:
        """删除文档"""
        try:
            result = await self.db.execute(
                select(Document)
                .where(and_(
                    Document.document_id == document_id,
                    Document.user_id == user_id
                ))
            )
            document = result.scalar_one_or_none()

            if not document:
                return False

            # 删除相关日志
            await self.db.execute(
                select(DocumentLog)
                .where(DocumentLog.document_id == document_id)
            )

            # 删除文档
            await self.db.delete(document)
            await self.db.commit()

            logger.info(f"删除文档成功: {document_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除文档失败: {e}")
            raise

    async def get_batch_status(self, batch_id: str, user_id: str) -> Optional[BatchStatusResponse]:
        """获取批次状态"""
        try:
            # 查询批次下的所有文档
            result = await self.db.execute(
                select(Document)
                .where(and_(
                    Document.batch_id == batch_id,
                    Document.user_id == user_id
                ))
                .order_by(Document.created_at)
            )
            documents = result.scalars().all()

            if not documents:
                return None

            # 统计信息
            total = len(documents)
            completed = sum(1 for doc in documents if doc.status == DocumentStatus.COMPLETED)
            failed = sum(1 for doc in documents if doc.status == DocumentStatus.FAILED)
            progress = (completed / total * 100) if total > 0 else 0

            # 转换文档信息
            document_responses = [DocumentResponse.model_validate(doc) for doc in documents]

            return BatchStatusResponse(
                success=True,
                message="获取批次状态成功",
                batch_id=batch_id,
                total=total,
                completed=completed,
                failed=failed,
                progress=progress,
                documents=document_responses
            )

        except Exception as e:
            logger.error(f"获取批次状态失败: {e}")
            raise

    async def get_document_logs(
        self,
        document_id: str,
        limit: int = 100,
        offset: int = 0,
        level: Optional[str] = None
    ) -> List[DocumentLogLine]:
        """获取文档日志"""
        try:
            conditions = [DocumentLog.document_id == document_id]

            if level:
                conditions.append(DocumentLog.level == level.upper())

            result = await self.db.execute(
                select(DocumentLog)
                .where(and_(*conditions))
                .order_by(desc(DocumentLog.created_at))
                .offset(offset)
                .limit(limit)
            )
            logs = result.scalars().all()

            return [
                DocumentLogLine(
                    timestamp=log.created_at,
                    level=log.level,
                    message=log.message,
                    source=log.source
                )
                for log in logs
            ]

        except Exception as e:
            logger.error(f"获取文档日志失败: {e}")
            raise

    async def add_document_log(
        self,
        document_id: str,
        level: str,
        message: str,
        source: str = None
    ):
        """添加文档日志"""
        try:
            log_entry = DocumentLog(
                document_id=document_id,
                level=level.upper(),
                message=message,
                source=source
            )

            self.db.add(log_entry)
            await self.db.commit()

            # 推送到 WebSocket 客户端
            from almond_parser.api.websocket import push_document_log
            await push_document_log(document_id, level, message, source)

        except Exception as e:
            logger.error(f"添加文档日志失败: {e}")
            raise

    async def _handle_task_completion(self, document: Document, success: bool):
        """处理任务完成事件"""
        try:
            # 释放节点资源并触发新任务分配
            from almond_parser.services.task_allocation_service import task_allocation_service

            node_id = int(document.node_id) if document.node_id else None
            await task_allocation_service.handle_task_completion(
                document_id=document.document_id,
                success=success,
                node_id=node_id
            )

        except Exception as e:
            logger.error(f"处理任务完成事件失败: {document.document_id}, 错误: {e}")

    async def update_document_result(
            self,
            task_id: str,
            result_data: Dict[str, Any]
    ) -> Optional[bool]:
        """下载并保存 md 文件内容，更新文档结果"""
        document = None
        try:
            result = await self.db.execute(
                select(Document).where(Document.task_id == task_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                return False

            files = result_data.get("result", {}).get("files", {})
            md_url = files.get("markdown")

            if not md_url:
                document.status = DocumentStatus.FAILED
                document.error_message = "回调结果中未包含 Markdown 文件"
                document.result_data = result_data
                await self.add_document_log(
                    document.document_id, "ERROR",
                    "文档解析失败：未包含 Markdown 文件"
                )
                await self.db.commit()
                return True

            # 创建保存目录
            output_dir = Path(settings.OUTPUT_DIR) / document.document_id
            output_dir.mkdir(parents=True, exist_ok=True)
            md_file_name = document.file_name + ".md"
            md_path = output_dir / md_file_name

            # 下载 md 内容
            async with aiohttp.ClientSession() as session:
                async with session.get(md_url) as response:
                    if response.status != 200:
                        raise RuntimeError(f"下载失败: 状态码 {response.status}")
                    md_content = await response.text()

            # 写入本地文件
            md_path.write_text(md_content, encoding="utf-8")

            # 更新文档状态
            document.status = DocumentStatus.COMPLETED
            document.progress = 100
            document.completed_at = datetime.now()
            document.result_data = result_data
            document.output_path = str(md_path)

            await self.add_document_log(
                document.document_id, "INFO",
                f"文档解析完成，Markdown 已保存: {md_path}"
            )
            await self.db.commit()
            logger.info(f"文档解析完成: {document.document_id}, 文件保存路径: {md_path}")

            # 触发任务完成事件处理
            await self._handle_task_completion(document, success=True)

            return True

        except Exception as e:
            if document:
                document.status = DocumentStatus.FAILED
                document.error_message = f"更新解析结果失败: {str(e)}"
                try:
                    await self.add_document_log(
                        document.document_id, "ERROR",
                        f"更新解析结果失败: {str(e)}"
                    )
                    await self.db.commit()
                except Exception as log_error:
                    logger.error(f"记录错误日志失败: {log_error}")
                    await self.db.rollback()
            logger.error(f"更新文档解析结果失败: {e}")
            return False

    async def update_document_status(
        self,
        document_id: str,
        status: DocumentStatus,
        progress: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新文档状态（通过document_id）"""
        try:
            result = await self.db.execute(
                select(Document).where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                return False

            return await self._update_document_status_internal(document, status, progress, error_message)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新文档状态失败: {e}")
            raise

    async def update_document_status_by_task_id(
        self,
        task_id: str,
        status: DocumentStatus,
        progress: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新文档状态（通过task_id）"""
        try:
            result = await self.db.execute(
                select(Document).where(Document.task_id == task_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                return False

            return await self._update_document_status_internal(document, status, progress, error_message)

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新文档状态失败: {e}")
            raise

    async def _update_document_status_internal(
        self,
        document: Document,
        status: DocumentStatus,
        progress: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """内部方法：更新文档状态"""
        try:
            # 更新状态
            document.status = status
            if progress is not None:
                document.progress = progress
            if error_message:
                document.error_message = error_message

            # 设置开始和完成时间
            if status == DocumentStatus.PARSING and not document.started_at:
                document.started_at = datetime.now()
            elif status == DocumentStatus.COMPLETED and not document.completed_at:
                document.completed_at = datetime.now()

            await self.db.commit()

            # 记录状态变更日志
            await self.add_document_log(
                document.document_id, "INFO",
                f"状态更新: {status.value}" + (f", 进度: {progress}%" if progress is not None else "")
            )

            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新文档状态失败: {e}")
            raise

    async def get_batch_documents(self, batch_id: str) -> List[DocumentResponse]:
        """获取批次下的所有文档"""
        try:
            result = await self.db.execute(
                select(Document)
                .where(Document.batch_id == batch_id)
                .order_by(Document.created_at)
            )
            documents = result.scalars().all()

            return [DocumentResponse.model_validate(doc) for doc in documents]

        except Exception as e:
            logger.error(f"获取批次文档失败: {e}")
            raise

    async def get_document_by_id(self, document_id: str) -> Document:
        """获取文档详情"""
        try:
            result = await self.db.execute(
                select(Document)
                .where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                raise f"文档 {document_id} 不存在"

            return document

        except Exception as e:
            logger.error(f"获取文档详情失败: {e}")
            raise e
