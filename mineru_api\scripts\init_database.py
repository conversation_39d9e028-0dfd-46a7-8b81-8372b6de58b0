#!/usr/bin/env python3
"""
数据库初始化脚本
用于在部署时预先创建 mineru_api 的所有数据库和目录
"""

import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from mineru_api.config import get_auth_config, BASE_DIR, ENABLE_AUTH
from mineru_api.services.history_service import TaskHistoryService


def init_directories():
    """初始化必要的目录"""
    logger.info("初始化目录结构...")
    
    directories = [
        BASE_DIR / "data",
        BASE_DIR / "logs", 
        BASE_DIR / "output",
        BASE_DIR / "temp"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ 目录已创建: {directory}")


def init_auth_database():
    """初始化认证数据库"""
    if not ENABLE_AUTH:
        logger.info("认证功能已禁用，跳过认证数据库初始化")
        return
    
    try:
        logger.info("初始化认证数据库...")
        
        from mineru_api.auth.backends import get_auth_backend
        
        auth_config = get_auth_config()
        logger.info(f"使用认证后端: {auth_config.backend_type.value}")
        
        # 初始化认证后端（会自动创建数据库）
        backend = get_auth_backend(auth_config)
        
        if auth_config.backend_type.value == "sqlite":
            db_path = Path(auth_config.sqlite_path)
            logger.info(f"✅ 认证数据库已初始化: {db_path}")
        else:
            logger.info(f"✅ 认证后端 {auth_config.backend_type.value} 已初始化")
            
    except Exception as e:
        logger.error(f"❌ 认证数据库初始化失败: {e}")
        raise


def init_history_database():
    """初始化任务历史数据库"""
    try:
        logger.info("初始化任务历史数据库...")
        
        # 初始化历史服务（会自动创建数据库）
        history_service = TaskHistoryService()
        
        logger.info(f"✅ 任务历史数据库已初始化: {history_service.db_path}")
        
    except Exception as e:
        logger.error(f"❌ 任务历史数据库初始化失败: {e}")
        raise


def main():
    """主函数"""
    logger.info("🚀 开始初始化 MineruAPI 数据库...")
    
    try:
        # 初始化目录
        init_directories()
        
        # 初始化认证数据库
        init_auth_database()
        
        # 初始化任务历史数据库
        init_history_database()
        
        logger.info("🎉 数据库初始化完成！")
        print("\n✅ MineruAPI 数据库初始化成功")
        print("现在可以正常启动服务了")
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        print(f"\n❌ 初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
