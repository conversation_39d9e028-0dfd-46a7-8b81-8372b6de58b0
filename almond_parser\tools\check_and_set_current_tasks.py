#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
检查和设置节点的 current_tasks 值
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.mineru_node import MinerUNode
from sqlalchemy import select


async def check_and_set_current_tasks():
    """检查和设置节点的 current_tasks 值"""
    print("=" * 60)
    print("检查和设置节点的 current_tasks 值")
    print("=" * 60)
    
    async with get_async_session() as db:
        # 1. 查询所有节点
        print("\n1. 当前所有节点状态:")
        nodes_result = await db.execute(
            select(MinerUNode).order_by(MinerUNode.id)
        )
        nodes = nodes_result.scalars().all()
        
        print(f"{'节点ID':<8} {'名称':<20} {'状态':<10} {'启用':<6} {'当前任务':<10} {'最大任务':<10}")
        print("-" * 70)
        
        for node in nodes:
            print(f"{node.id:<8} {node.name:<20} {node.status.value:<10} {node.is_enabled:<6} "
                  f"{node.current_tasks:<10} {node.max_concurrent_tasks:<10}")
        
        # 2. 手动设置第一个启用节点的 current_tasks 为 3
        enabled_nodes = [n for n in nodes if n.is_enabled]
        
        if not enabled_nodes:
            print("\n❌ 没有找到启用的节点")
            return
        
        target_node = enabled_nodes[0]
        print(f"\n2. 设置节点 {target_node.id} ({target_node.name}) 的 current_tasks 为 3")
        
        # 更新 current_tasks 和 last_task_assigned_at
        from datetime import datetime
        target_node.current_tasks = 3
        target_node.last_task_assigned_at = datetime.now()  # 🔑 关键：同时更新时间戳
        await db.commit()

        print(f"✅ 已设置节点 {target_node.id} 的 current_tasks = 3")
        print(f"✅ 已更新节点 {target_node.id} 的 last_task_assigned_at = {target_node.last_task_assigned_at}")
        
        # 3. 验证设置结果
        print("\n3. 验证设置结果:")
        verification_result = await db.execute(
            select(MinerUNode).where(MinerUNode.id == target_node.id)
        )
        updated_node = verification_result.scalar_one()
        
        print(f"节点 {updated_node.id} ({updated_node.name}):")
        print(f"  current_tasks: {updated_node.current_tasks}")
        print(f"  max_concurrent_tasks: {updated_node.max_concurrent_tasks}")
        print(f"  status: {updated_node.status.value}")
        print(f"  is_enabled: {updated_node.is_enabled}")
        
        # 4. 测试容量查询
        print("\n4. 测试容量查询:")
        from almond_parser.db.models.mineru_node import NodeStatus
        from sqlalchemy import func
        
        capacity_result = await db.execute(
            select(
                func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                func.sum(MinerUNode.current_tasks).label("current_load"),
                func.count(MinerUNode.id).label("active_nodes")
            ).where(
                MinerUNode.is_enabled == True
            )
        )
        capacity_row = capacity_result.first()
        total_capacity = capacity_row.total_capacity or 0
        current_load = capacity_row.current_load or 0
        active_nodes = capacity_row.active_nodes or 0
        available_capacity = total_capacity - current_load
        
        print(f"  查询条件: is_enabled=True")
        print(f"  匹配节点: {active_nodes}")
        print(f"  总容量: {total_capacity}")
        print(f"  当前负载: {current_load}")
        print(f"  可用容量: {available_capacity}")
        
        if current_load == 3:
            print("✅ current_tasks 设置成功，查询结果正确")
        else:
            print(f"❌ current_tasks 设置失败，期望 3，实际 {current_load}")


async def reset_current_tasks():
    """重置所有节点的 current_tasks 为 0"""
    print("=" * 60)
    print("重置所有节点的 current_tasks 为 0")
    print("=" * 60)
    
    async with get_async_session() as db:
        nodes_result = await db.execute(
            select(MinerUNode)
        )
        nodes = nodes_result.scalars().all()
        
        for node in nodes:
            print(f"重置节点 {node.id} ({node.name}) current_tasks: {node.current_tasks} -> 0")
            node.current_tasks = 0
        
        await db.commit()
        print("✅ 所有节点的 current_tasks 已重置为 0")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="检查和设置节点的 current_tasks 值")
    parser.add_argument("--reset", action="store_true", help="重置所有节点的 current_tasks 为 0")
    
    args = parser.parse_args()
    
    if args.reset:
        await reset_current_tasks()
    else:
        await check_and_set_current_tasks()


if __name__ == "__main__":
    asyncio.run(main())
