# -*- encoding: utf-8 -*-
"""
@File   :mineru_server.py
@Time   :2025/6/25 14:30
<AUTHOR>
"""
import base64
from typing import Dict, Any
import asyncio

import litserve as ls
from fastapi import HTTPException
from loguru import logger

from mineru_api.models import (
    OCRRequest, TaskResponse,
    generate_task_id, TaskStatus
)
from mineru_api.services.callback_service import callback_service
from mineru_api.services.ocr_service import ocr_service
from mineru_api.services.task_manager import task_manager
from mineru_api.utils.async_utils import create_background_task


class MineruAPI(ls.LitAPI):
    """Mineru OCR API 服务"""

    def setup(self, device):
        """初始化设置"""
        logger.info("MineruAPI 服务初始化中...")
        self.device = device
        logger.info("MineruAPI 服务初始化完成")

    def decode_request(self, request: Dict[str, Any], **kwargs) -> OCRRequest:
        """解码请求"""
        try:
            # 记录请求信息（不包含文件内容以避免日志过长）
            request_info = {k: v for k, v in request.items() if k != "file_content"}
            if "file_content" in request:
                request_info["file_content_size"] = len(request["file_content"]) if isinstance(request["file_content"], str) else len(str(request["file_content"]))

            logger.debug(f"解码请求: {request_info}")

            # 处理文件上传
            if "file_content" in request and isinstance(request["file_content"], str):
                # Base64 解码
                file_content = base64.b64decode(request["file_content"])
                request["file_content"] = file_content

            return OCRRequest(**request)
        except Exception as e:
            logger.error(f"请求解码失败: {e}")
            raise HTTPException(status_code=400, detail=f"请求格式错误: {str(e)}")

    def predict(self, request: OCRRequest, **kwargs) -> TaskResponse:
        """处理 OCR 解析请求 - 快速返回任务ID"""
        # 生成任务ID
        task_id = generate_task_id()

        try:
            # 创建任务
            task_manager.create_task(task_id, request)

            # 使用线程池提交异步任务（避免事件循环问题）
            import threading

            def submit_async_task():
                """在新线程中运行异步任务"""
                import asyncio

                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # 运行异步任务
                    loop.run_until_complete(
                        task_manager.submit_task(
                            task_id, ocr_service, request, callback_service
                        )
                    )
                finally:
                    loop.close()

            # 在后台线程中启动任务
            thread = threading.Thread(target=submit_async_task, daemon=True)
            thread.start()

            logger.info(f"任务 {task_id} 已提交，文件: {request.file_name}")

            return TaskResponse(
                task_id=task_id,
                status=TaskStatus.PENDING,
                message="任务已提交，正在处理中"
            )

        except Exception as e:
            logger.error(f"任务提交失败: {e}")
            raise HTTPException(status_code=500, detail=f"任务提交失败: {str(e)}")

    def encode_response(self, output: TaskResponse, **kwargs) -> Dict[str, Any]:
        """编码响应"""
        return output.model_dump()


api = MineruAPI()
