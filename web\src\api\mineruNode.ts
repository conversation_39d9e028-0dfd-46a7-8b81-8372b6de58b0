import request from '@/utils/request'
import type { 
  MinerUN<PERSON>, 
  MinerUNodeCreate, 
  MinerUNodeUpdate, 
  MinerUNodeStats,
  NodeHealthStatus 
} from '@/types/mineruNode'

export interface GetNodesParams {
  skip?: number
  limit?: number
  status?: string
  parse_mode?: string
  service_type?: string
  is_enabled?: boolean
}

export interface HealthCheckResult {
  message: string
  is_healthy: boolean
}

export interface AutoDetectResult {
  message: string
  detected_mode: string | null
  updated: boolean
}

export const mineruNodeApi = {
  // 创建节点
  createNode(data: MinerUNodeCreate): Promise<MinerUNode> {
    return request.post('/mineru-nodes/', data)
  },

  // 获取节点列表
  getNodes(params?: GetNodesParams): Promise<MinerUNode[]> {
    return request.get('/mineru-nodes/', {
      params
    })
  },

  // 获取节点统计
  getStats(): Promise<MinerUNodeStats> {
    return request.get('/mineru/stats')
  },

  // 获取单个节点
  getNode(nodeId: number): Promise<MinerUNode> {
    return request.get(`/mineru-nodes/${nodeId}`)
  },

  // 更新节点
  updateNode(nodeId: number, data: MinerUNodeUpdate): Promise<MinerUNode> {
    return request.put(`/mineru-nodes/${nodeId}`, data)
  },

  // 删除节点
  deleteNode(nodeId: number): Promise<void> {
    return request.delete(`/mineru-nodes/${nodeId}`)
  },

  // 触发所有节点健康检查
  triggerHealthCheck(): Promise<void> {
    return request.post('/mineru-nodes/health-check')
  },

  // 获取节点健康状态
  getNodeHealth(nodeId: number): Promise<NodeHealthStatus> {
    return request.get(`/mineru-nodes/${nodeId}/health`)
  },

  // 触发单个节点健康检查
  triggerSingleHealthCheck(nodeId: number): Promise<HealthCheckResult> {
    return request.post(`/mineru-nodes/${nodeId}/health-check`)
  },

  // 自动检测节点解析模式
  autoDetectParseMode(nodeId: number): Promise<AutoDetectResult> {
    return request.get(`/mineru-nodes/${nodeId}/auto-detect-mode`)
  },

  // 切换节点启用状态
  toggleNodeStatus(nodeId: number, enabled: boolean): Promise<MinerUNode> {
    return request.put(`/mineru-nodes/${nodeId}/toggle-status`, { is_enabled: enabled })
  },

  // SGLang服务相关接口
  // 获取节点SGLang服务状态
  getSglangStatus(nodeId: number): Promise<any> {
    return request.get(`/mineru-nodes/${nodeId}/sglang/status`)
  },

  // 重启节点SGLang服务
  restartSglang(nodeId: number): Promise<any> {
    return request.post(`/mineru-nodes/${nodeId}/sglang/restart`)
  },

  // 监控所有SGLang服务
  monitorAllSglang(): Promise<any> {
    return request.post('/mineru-nodes/sglang/monitor-all')
  },

  // 重启失败的SGLang服务
  restartFailedSglang(): Promise<any> {
    return request.post('/mineru-nodes/sglang/restart-failed')
  },

  // 获取节点系统信息
  getNodeSystemInfo(nodeId: number): Promise<any> {
    return request.get(`/mineru-nodes/${nodeId}/system-info`)
  },

  // 获取节点系统资源使用情况
  getNodeSystemResources(nodeId: number): Promise<any> {
    return request.get(`/mineru-nodes/${nodeId}/system-resources`)
  }
}
