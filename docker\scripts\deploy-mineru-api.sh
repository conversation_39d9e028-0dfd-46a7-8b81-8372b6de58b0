#!/bin/bash

# 部署MinerU API解析引擎的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查镜像是否存在
    if ! docker images | grep -q "mineru-api"; then
        log_error "mineru-api 镜像不存在，请先运行 ./scripts/build.sh"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查GPU支持
check_gpu() {
    log_info "检查GPU支持"
    
    if command -v nvidia-smi &> /dev/null; then
        log_info "检测到NVIDIA GPU："
        nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
        
        # 检查Docker GPU支持
        if docker run --rm --gpus all nvidia/cuda:11.8-base nvidia-smi &> /dev/null; then
            log_success "Docker GPU支持正常"
            export GPU_ENABLED=true
        else
            log_warning "Docker GPU支持异常，将使用CPU模式"
            export GPU_ENABLED=false
        fi
    else
        log_warning "未检测到NVIDIA GPU，将使用CPU模式"
        export GPU_ENABLED=false
    fi
}

# 检查网络
check_network() {
    log_info "检查Docker网络"
    
    if ! docker network ls | grep -q "parserflow-network"; then
        log_info "创建网络 parserflow-network"
        docker network create \
            --driver bridge \
            --subnet 172.20.0.0/16 \
            parserflow-network
        log_success "网络创建成功"
    else
        log_success "网络已存在"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录"
    
    mkdir -p data/mineru/temp
    mkdir -p data/mineru/output
    mkdir -p data/mineru/models
    mkdir -p data/mineru/config
    mkdir -p logs/mineru
    
    log_success "目录创建完成"
}

# 创建配置文件
create_config() {
    log_info "创建MinerU配置文件"
    
    config_file="data/mineru/config/mineru_config.json"
    
    if [ ! -f "$config_file" ]; then
        cat > "$config_file" << EOF
{
    "model": {
        "ocr": {
            "provider": "paddle",
            "config": {
                "use_gpu": ${GPU_ENABLED:-false}
            }
        },
        "layout": {
            "provider": "layoutlmv3",
            "config": {
                "use_gpu": ${GPU_ENABLED:-false}
            }
        }
    },
    "output": {
        "format": "markdown",
        "save_images": true
    },
    "processing": {
        "max_workers": 4,
        "chunk_size": 1024
    }
}
EOF
        log_success "配置文件创建完成"
    else
        log_info "配置文件已存在"
    fi
}

# 部署MinerU API
deploy_mineru_api() {
    log_info "部署MinerU API服务"
    
    # 加载环境变量
    if [ -f "config/.env.mineru-api" ]; then
        export $(cat config/.env.mineru-api | grep -v '^#' | xargs)
    fi
    
    # 根据GPU支持选择compose文件
    if [ "$GPU_ENABLED" = "true" ]; then
        log_info "使用GPU模式部署"
        docker-compose -f compose/mineru-api.yml up -d
    else
        log_info "使用CPU模式部署"
        # 创建CPU版本的compose文件
        sed 's/nvidia/# nvidia/g' compose/mineru-api.yml > compose/mineru-api-cpu.yml
        docker-compose -f compose/mineru-api-cpu.yml up -d
    fi
    
    log_success "MinerU API服务启动成功"
}

# 等待服务就绪
wait_for_service() {
    log_info "等待MinerU API服务就绪..."
    
    timeout=180  # GPU模式需要更长时间
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8001/health &> /dev/null; then
            log_success "MinerU API服务已就绪"
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "MinerU API服务启动超时"
        log_info "查看日志："
        docker-compose -f compose/mineru-api.yml logs --tail=20
        exit 1
    fi
}

# 测试服务
test_service() {
    log_info "测试MinerU API服务"
    
    # 测试健康检查
    if curl -f http://localhost:8001/health; then
        log_success "健康检查通过"
    else
        log_warning "健康检查失败"
    fi
    
    # 测试API文档
    if curl -f http://localhost:8001/docs &> /dev/null; then
        log_success "API文档可访问"
    else
        log_warning "API文档不可访问"
    fi
}

# 显示服务状态
show_status() {
    log_info "=== 服务状态 ==="
    docker-compose -f compose/mineru-api.yml ps
    
    log_info "=== 服务访问地址 ==="
    echo "MinerU API:    http://localhost:8001"
    echo "API 文档:      http://localhost:8001/docs"
    echo "健康检查:      http://localhost:8001/health"
    
    log_info "=== GPU信息 ==="
    if [ "$GPU_ENABLED" = "true" ]; then
        echo "GPU模式: 启用"
        docker exec parserflow-mineru-api nvidia-smi --query-gpu=name,utilization.gpu,memory.used,memory.total --format=csv,noheader 2>/dev/null || echo "GPU信息获取失败"
    else
        echo "GPU模式: 禁用 (CPU模式)"
    fi
    
    log_info "=== 服务日志 ==="
    echo "查看日志: docker-compose -f compose/mineru-api.yml logs -f"
}

# 主函数
main() {
    log_info "开始部署MinerU API解析引擎"
    
    # 切换到docker目录
    cd "$(dirname "$0")/.."
    
    # 执行部署步骤
    check_environment
    check_gpu
    check_network
    create_directories
    create_config
    deploy_mineru_api
    wait_for_service
    test_service
    show_status
    
    log_success "MinerU API部署完成！"
    log_info "现在可以部署其他服务："
    echo "  ./scripts/deploy-almond-parser.sh  # 部署杏仁解析服务"
    echo "  ./scripts/deploy-web.sh            # 部署Web前端"
}

# 执行主函数
main "$@"
