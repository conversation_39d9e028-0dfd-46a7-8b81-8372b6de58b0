import React, { useState, useEffect } from 'react';
import { Spin, Alert } from 'antd';
import ReactMarkdown from 'react-markdown';
import type { DocumentParseResult as IDocumentParseResult } from '../types/document';
import request from '../utils/request';

interface DocumentParseResultProps {
  documentId: string;
}

const DocumentParseResult: React.FC<DocumentParseResultProps> = ({ documentId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<IDocumentParseResult | null>(null);

  useEffect(() => {
    const fetchResult = async () => {
      try {
        setLoading(true);
        setError(null);
        const { data } = await request.get<IDocumentParseResult>(`/api/documents/${documentId}/result`);
        setResult(data);
      } catch (err: any) {
        setError(err.message || '获取解析结果失败');
      } finally {
        setLoading(false);
      }
    };

    if (documentId) {
      fetchResult();
    }
  }, [documentId]);

  if (loading) {
    return <Spin tip="加载中..." />;
  }

  if (error) {
    return <Alert type="error" message={error} />;
  }

  if (!result) {
    return <Alert type="info" message="暂无解析结果" />;
  }

  return (
    <div className="document-parse-result">
      <div className="markdown-content">
        <ReactMarkdown>{result.result.markdown_text}</ReactMarkdown>
      </div>
      <style>
        {`
          .document-parse-result {
            padding: 16px;
          }
          .markdown-content {
            background: #fff;
            padding: 24px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
          .markdown-content img {
            max-width: 100%;
          }
          .markdown-content pre {
            background: #f6f8fa;
            padding: 16px;
            border-radius: 4px;
            overflow-x: auto;
          }
          .markdown-content code {
            background: #f6f8fa;
            padding: 2px 6px;
            border-radius: 3px;
          }
        `}
      </style>
    </div>
  );
};

export default DocumentParseResult; 