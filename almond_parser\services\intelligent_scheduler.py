"""
智能任务调度服务
实现动态负载感知的任务分配机制
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from almond_parser.db.models.mineru_node import MinerUNode
from almond_parser.services.task_allocation_service import task_allocation_service
from almond_parser.utils.logger import logger


class IntelligentScheduler:
    """智能任务调度器"""
    
    def __init__(self):
        self._last_check_time = datetime.now()
        self._last_total_capacity = 0
        self._last_current_load = 0
        self._consecutive_empty_checks = 0
        self._max_empty_checks = 10  # 连续10次空检查后降低频率
        
    async def smart_allocate_tasks(self, force_check: bool = False) -> Dict[str, Any]:
        """
        智能分配任务 - 基于当前负载动态决定是否需要分配
        
        Args:
            force_check: 强制检查，忽略优化策略
            
        Returns:
            分配统计信息
        """
        try:
            async with get_async_session() as db:
                # 1. 获取当前系统负载信息
                load_info = await self._get_system_load_info(db)
                
                # 2. 判断是否需要分配任务
                should_allocate, allocation_count = await self._should_allocate_tasks(
                    db, load_info, force_check
                )
                
                if not should_allocate:
                    self._consecutive_empty_checks += 1
                    return {
                        "allocated": 0,
                        "reason": "no_allocation_needed",
                        "load_info": load_info,
                        "consecutive_empty": self._consecutive_empty_checks
                    }
                
                # 3. 执行任务分配 - 使用按节点类型分配
                self._consecutive_empty_checks = 0
                stats = await task_allocation_service.allocate_pending_tasks_by_node_type(
                    max_allocations=allocation_count
                )
                
                # 4. 更新缓存信息
                self._last_total_capacity = load_info["total_capacity"]
                self._last_current_load = load_info["current_load"]
                self._last_check_time = datetime.now()
                
                # 5. 增强统计信息
                stats.update({
                    "load_info": load_info,
                    "allocation_count": allocation_count,
                    "consecutive_empty": 0
                })
                
                if stats.get("allocated", 0) > 0:
                    logger.info(f"智能调度完成: {stats}")
                
                return stats
                
        except Exception as e:
            logger.error(f"智能任务分配失败: {e}")
            return {"error": str(e)}
    
    async def _get_system_load_info(self, db: AsyncSession) -> Dict[str, Any]:
        """获取系统负载信息"""
        try:
            # 查询所有活跃节点的负载信息
            result = await db.execute(
                select(
                    func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                    func.sum(MinerUNode.current_tasks).label("current_load"),
                    func.sum(MinerUNode.reserved_tasks).label("reserved_load"),
                    func.count(MinerUNode.id).label("active_nodes")
                ).where(
                    MinerUNode.is_active == True,
                    MinerUNode.status == "healthy"
                )
            )
            
            row = result.first()
            total_capacity = row.total_capacity or 0
            current_load = row.current_load or 0
            reserved_load = row.reserved_load or 0
            active_nodes = row.active_nodes or 0
            
            # 计算可用容量
            available_capacity = total_capacity - current_load - reserved_load
            load_percentage = (current_load / total_capacity * 100) if total_capacity > 0 else 0
            
            # 查询等待任务数量
            pending_result = await db.execute(
                select(func.count(Document.id)).where(
                    Document.status == DocumentStatus.RETRY_PENDING
                )
            )
            pending_tasks = pending_result.scalar() or 0
            
            return {
                "total_capacity": total_capacity,
                "current_load": current_load,
                "reserved_load": reserved_load,
                "available_capacity": available_capacity,
                "load_percentage": round(load_percentage, 2),
                "active_nodes": active_nodes,
                "pending_tasks": pending_tasks,
                "timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"获取系统负载信息失败: {e}")
            return {
                "total_capacity": 0,
                "current_load": 0,
                "available_capacity": 0,
                "load_percentage": 0,
                "active_nodes": 0,
                "pending_tasks": 0,
                "error": str(e)
            }


# 全局智能调度器实例
intelligent_scheduler = IntelligentScheduler()
