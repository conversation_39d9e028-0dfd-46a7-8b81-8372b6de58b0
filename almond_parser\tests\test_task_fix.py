#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试 ARQ 任务修复
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.tasks.node_tasks import health_check_all_nodes
from almond_parser.db import init_database
from almond_parser.utils.logger import setup_logger
from loguru import logger


async def test_health_check_task():
    """测试健康检查任务"""
    logger.info("🧪 测试健康检查任务...")
    
    try:
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库初始化完成")
        
        # 模拟 ARQ 上下文
        ctx = {"job_id": "test_job", "job_try": 1}
        
        # 执行健康检查任务
        result = await health_check_all_nodes(ctx)
        
        logger.info(f"✅ 任务执行成功: {result}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 任务执行失败: {e}")
        return False


async def test_database_session():
    """测试数据库会话"""
    logger.info("🧪 测试数据库会话...")
    
    try:
        from almond_parser.db import db_manager
        
        async with db_manager.session_factory() as db:
            # 测试简单查询
            from sqlalchemy import text
            result = await db.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            logger.info(f"✅ 数据库查询成功: {row}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库会话测试失败: {e}")
        return False


async def main():
    """主函数"""
    setup_logger()
    
    logger.info("🔧 ARQ 任务修复测试")
    logger.info("=" * 40)
    
    success = True
    
    # 测试数据库会话
    if not await test_database_session():
        success = False
    
    # 测试健康检查任务
    if not await test_health_check_task():
        success = False
    
    if success:
        logger.info("\n🎉 所有测试通过！")
        logger.info("现在可以重新启动 Worker 了")
    else:
        logger.info("\n❌ 测试失败，请检查错误信息")


if __name__ == "__main__":
    asyncio.run(main())
