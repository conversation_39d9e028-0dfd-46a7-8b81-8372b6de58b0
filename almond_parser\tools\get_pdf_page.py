# -*- encoding: utf-8 -*-
"""
@File   :get_pdf_page.py
@Time   :2025/8/27 18:27
<AUTHOR>
"""

from typing import Tuple, Optional

from PyPDF2 import PdfReader
from loguru import logger


def check_pdf_page_count(file_path: str, max_pages: int = 500) -> Tuple[bool, int, Optional[str]]:
    """
    判断PDF页数是否超过指定值

    Args:
        file_path (str): PDF 文件路径
        max_pages (int): 最大允许页数，默认500页

    Returns:
        Tuple[bool, int, Optional[str]]:
            - bool: 页数是否 <= max_pages（仅在读取成功时有效）
            - int: 实际页数（读取失败时为 0）
            - Optional[str]: 错误信息，如果读取成功则为 None
    """
    try:
        reader = PdfReader(file_path)
        num_pages = len(reader.pages)
        return num_pages <= max_pages, num_pages, None
    except Exception as e:
        logger.error(f"检查PDF页数失败: {e}")
        return False, 0, str(e)
