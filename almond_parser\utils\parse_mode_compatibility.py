# -*- encoding: utf-8 -*-
"""
解析模式兼容性管理
"""
from typing import Dict, List, Set
from enum import Enum
from loguru import logger


class ParseModeCompatibility:
    """解析模式兼容性管理器"""
    
    # 解析模式兼容性矩阵
    # 键：节点支持的模式，值：可以处理的请求模式列表
    COMPATIBILITY_MATRIX = {
        # VLM/SGLang 节点可以处理所有模式的请求
        "sglang": ["sglang", "vlm", "vlm-sglang-client", "pipeline", "auto"],
        "vlm": ["sglang", "vlm", "vlm-sglang-client", "pipeline", "auto"],
        "vlm-sglang-client": ["sglang", "vlm", "vlm-sglang-client", "pipeline", "auto"],
        "vlm-transformers": ["vlm-transformers", "pipeline", "auto"],
        
        # Pipeline 节点只能处理 pipeline 模式
        "pipeline": ["pipeline", "auto"],
        
        # Auto 模式可以处理所有请求
        "auto": ["sglang", "vlm", "vlm-sglang-client", "vlm-transformers", "pipeline", "auto"],
    }
    
    # 模式优先级（数字越大优先级越高）
    MODE_PRIORITY = {
        "sglang": 100,
        "vlm": 95,
        "vlm-sglang-client": 90,
        "vlm-transformers": 80,
        "pipeline": 70,
        "auto": 60,
    }
    
    @classmethod
    def can_handle_request(cls, node_mode: str, request_mode: str) -> bool:
        """
        检查节点是否可以处理指定的请求模式
        
        Args:
            node_mode: 节点配置的解析模式
            request_mode: 请求的解析模式
            
        Returns:
            是否兼容
        """
        if not node_mode or not request_mode:
            return False
            
        # 标准化模式名称
        node_mode = cls._normalize_mode(node_mode)
        request_mode = cls._normalize_mode(request_mode)
        
        # 检查兼容性矩阵
        compatible_modes = cls.COMPATIBILITY_MATRIX.get(node_mode, [])
        is_compatible = request_mode in compatible_modes
        
        logger.debug(f"兼容性检查: 节点模式={node_mode}, 请求模式={request_mode}, 兼容={is_compatible}")
        
        return is_compatible
    
    @classmethod
    def get_compatible_nodes_modes(cls, request_mode: str) -> List[str]:
        """
        获取可以处理指定请求模式的所有节点模式
        
        Args:
            request_mode: 请求的解析模式
            
        Returns:
            兼容的节点模式列表，按优先级排序
        """
        request_mode = cls._normalize_mode(request_mode)
        compatible_modes = []
        
        for node_mode, supported_requests in cls.COMPATIBILITY_MATRIX.items():
            if request_mode in supported_requests:
                compatible_modes.append(node_mode)
        
        # 按优先级排序
        compatible_modes.sort(key=lambda x: cls.MODE_PRIORITY.get(x, 0), reverse=True)
        
        logger.debug(f"请求模式 {request_mode} 的兼容节点模式: {compatible_modes}")
        
        return compatible_modes
    
    @classmethod
    def get_best_execution_mode(cls, node_mode: str, request_mode: str) -> str:
        """
        获取最佳执行模式
        
        当节点支持多种模式时，选择最适合的执行模式
        
        Args:
            node_mode: 节点配置的解析模式
            request_mode: 请求的解析模式
            
        Returns:
            最佳执行模式
        """
        node_mode = cls._normalize_mode(node_mode)
        request_mode = cls._normalize_mode(request_mode)
        
        # 如果请求模式是 auto，使用节点的默认模式
        if request_mode == "auto":
            return node_mode
        
        # 如果节点可以处理请求模式，优先使用请求模式
        if cls.can_handle_request(node_mode, request_mode):
            # 对于VLM节点，如果请求pipeline，实际执行pipeline
            if node_mode in ["sglang", "vlm", "vlm-sglang-client"] and request_mode == "pipeline":
                return "pipeline"
            # 否则使用请求的模式
            return request_mode
        
        # 不兼容的情况，返回节点默认模式
        return node_mode
    
    @classmethod
    def _normalize_mode(cls, mode: str) -> str:
        """标准化模式名称"""
        if not mode:
            return "auto"
        
        mode = mode.lower().strip()
        
        # 模式别名映射
        alias_mapping = {
            "vlm": "vlm-sglang-client",
            "sglang": "vlm-sglang-client",
        }
        
        return alias_mapping.get(mode, mode)
    
    @classmethod
    def get_mode_description(cls, mode: str) -> str:
        """获取模式描述"""
        descriptions = {
            "pipeline": "传统Pipeline模式，兼容性最好",
            "vlm-sglang-client": "VLM SGLang客户端模式，性能最佳",
            "vlm-transformers": "VLM Transformers模式，中等性能",
            "auto": "自动选择模式",
        }
        
        normalized_mode = cls._normalize_mode(mode)
        return descriptions.get(normalized_mode, f"未知模式: {mode}")
    
    @classmethod
    def validate_mode(cls, mode: str) -> bool:
        """验证模式是否有效"""
        normalized_mode = cls._normalize_mode(mode)
        return normalized_mode in cls.COMPATIBILITY_MATRIX
    
    @classmethod
    def get_all_supported_modes(cls) -> List[str]:
        """获取所有支持的模式"""
        return list(cls.COMPATIBILITY_MATRIX.keys())
    
    @classmethod
    def get_compatibility_info(cls) -> Dict[str, Dict]:
        """获取完整的兼容性信息"""
        info = {}
        
        for node_mode, compatible_requests in cls.COMPATIBILITY_MATRIX.items():
            info[node_mode] = {
                "description": cls.get_mode_description(node_mode),
                "priority": cls.MODE_PRIORITY.get(node_mode, 0),
                "can_handle": compatible_requests,
                "is_vlm_capable": node_mode in ["sglang", "vlm", "vlm-sglang-client", "auto"],
                "is_pipeline_capable": "pipeline" in compatible_requests
            }
        
        return info


# 便捷函数
def can_node_handle_request(node_mode: str, request_mode: str) -> bool:
    """检查节点是否可以处理请求"""
    return ParseModeCompatibility.can_handle_request(node_mode, request_mode)


def get_best_execution_mode(node_mode: str, request_mode: str) -> str:
    """获取最佳执行模式"""
    return ParseModeCompatibility.get_best_execution_mode(node_mode, request_mode)


def find_compatible_node_modes(request_mode: str) -> List[str]:
    """查找兼容的节点模式"""
    return ParseModeCompatibility.get_compatible_nodes_modes(request_mode)


# 示例用法和测试
if __name__ == "__main__":
    # 测试兼容性
    test_cases = [
        ("sglang", "pipeline"),  # VLM节点处理pipeline请求
        ("sglang", "sglang"),    # VLM节点处理VLM请求
        ("pipeline", "sglang"),  # Pipeline节点处理VLM请求（不兼容）
        ("pipeline", "pipeline"), # Pipeline节点处理pipeline请求
        ("auto", "pipeline"),    # Auto节点处理任何请求
    ]
    
    print("🧪 兼容性测试:")
    for node_mode, request_mode in test_cases:
        compatible = can_node_handle_request(node_mode, request_mode)
        best_mode = get_best_execution_mode(node_mode, request_mode)
        print(f"  节点:{node_mode:15} 请求:{request_mode:15} 兼容:{compatible} 执行模式:{best_mode}")
    
    print(f"\n📋 兼容性矩阵:")
    for node_mode, compatible_requests in ParseModeCompatibility.COMPATIBILITY_MATRIX.items():
        print(f"  {node_mode:20} -> {compatible_requests}")
