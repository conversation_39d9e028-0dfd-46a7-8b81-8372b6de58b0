"""
sglang 健康监控和自动恢复服务
解决 sglang 服务僵死和进程管理问题
"""
import asyncio
import subprocess
import time
import psutil
import httpx
import os
import signal
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Set
from loguru import logger

from ..config import SGLANG_HOST, SGLANG_PORT, SGLANG_URL, SGLANG_HEALTH_CHECK_TIMEOUT
from ..utils.async_utils import StructuredLogger


class SglangHealthMonitor:
    """sglang 健康监控器"""
    
    def __init__(self):
        self.host = SGLANG_HOST
        self.port = SGLANG_PORT
        self.url = SGLANG_URL
        self.health_check_interval = 30  # 30秒检查一次
        self.max_consecutive_failures = 3  # 连续失败3次后重启
        self.consecutive_failures = 0
        self.last_health_check = None
        self.last_restart_time = None
        self.min_restart_interval = 300  # 最小重启间隔5分钟
        
        # 进程跟踪
        self.tracked_pids: Set[int] = set()
        self.main_process_pid: Optional[int] = None
    
    async def health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        check_result = {
            "timestamp": datetime.now().isoformat(),
            "healthy": False,
            "response_time": None,
            "error": None,
            "process_info": {},
            "port_status": self._check_port_status()
        }
        
        try:
            start_time = time.time()
            
            # HTTP 健康检查
            timeout = httpx.Timeout(connect=5.0, read=10.0, write=5.0, pool=5.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(f"{self.url}/health")
                
                response_time = time.time() - start_time
                check_result["response_time"] = response_time
                
                if response.status_code == 200:
                    check_result["healthy"] = True
                    self.consecutive_failures = 0
                    logger.debug(f"sglang 健康检查通过，响应时间: {response_time:.2f}s")
                else:
                    check_result["error"] = f"HTTP {response.status_code}"
                    self.consecutive_failures += 1
                    logger.warning(f"sglang 健康检查失败: HTTP {response.status_code}")
        
        except asyncio.TimeoutError:
            check_result["error"] = "请求超时"
            self.consecutive_failures += 1
            logger.warning("sglang 健康检查超时")
        
        except Exception as e:
            check_result["error"] = str(e)
            self.consecutive_failures += 1
            logger.warning(f"sglang 健康检查异常: {e}")
        
        # 获取进程信息
        check_result["process_info"] = self._get_process_info()
        
        self.last_health_check = datetime.now()
        
        # 记录结构化日志
        StructuredLogger.log_task_event(
            "sglang_health_monitor", "health_check_completed",
            healthy=check_result["healthy"],
            consecutive_failures=self.consecutive_failures,
            response_time=check_result["response_time"],
            error=check_result["error"]
        )
        
        return check_result
    
    def _check_port_status(self) -> Dict[str, Any]:
        """检查端口状态"""
        port_info = {
            "listening": False,
            "connections": 0,
            "process_pids": []
        }
        
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == self.port:
                    if conn.status == psutil.CONN_LISTEN:
                        port_info["listening"] = True
                        if conn.pid:
                            port_info["process_pids"].append(conn.pid)
                    elif conn.status in [psutil.CONN_ESTABLISHED, psutil.CONN_TIME_WAIT]:
                        port_info["connections"] += 1
        
        except Exception as e:
            logger.error(f"检查端口状态失败: {e}")
        
        return port_info
    
    def _get_process_info(self) -> Dict[str, Any]:
        """获取 sglang 相关进程信息"""
        process_info = {
            "main_process": None,
            "child_processes": [],
            "total_processes": 0,
            "memory_usage": 0,
            "cpu_usage": 0
        }
        
        try:
            sglang_processes = self._find_sglang_processes()
            process_info["total_processes"] = len(sglang_processes)
            
            total_memory = 0
            total_cpu = 0
            
            for proc_info in sglang_processes:
                try:
                    proc = psutil.Process(proc_info["pid"])
                    memory_mb = proc.memory_info().rss / 1024 / 1024
                    cpu_percent = proc.cpu_percent()
                    
                    total_memory += memory_mb
                    total_cpu += cpu_percent
                    
                    proc_data = {
                        "pid": proc_info["pid"],
                        "ppid": proc.ppid(),
                        "memory_mb": round(memory_mb, 1),
                        "cpu_percent": round(cpu_percent, 1),
                        "create_time": proc.create_time(),
                        "cmdline": proc_info["cmdline"][:100] + "..." if len(proc_info["cmdline"]) > 100 else proc_info["cmdline"]
                    }
                    
                    if "mineru-sglang-server" in proc_info["cmdline"]:
                        process_info["main_process"] = proc_data
                        self.main_process_pid = proc_info["pid"]
                    else:
                        process_info["child_processes"].append(proc_data)
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            process_info["memory_usage"] = round(total_memory, 1)
            process_info["cpu_usage"] = round(total_cpu, 1)
        
        except Exception as e:
            logger.error(f"获取进程信息失败: {e}")
        
        return process_info
    
    def _find_sglang_processes(self) -> List[Dict[str, Any]]:
        """查找所有 sglang 相关进程"""
        processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = " ".join(proc.info['cmdline'] or [])
                    if any(keyword in cmdline.lower() for keyword in [
                        'mineru-sglang-server', 'sglang', 'sgl-server'
                    ]):
                        processes.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "cmdline": cmdline
                        })
                        self.tracked_pids.add(proc.info['pid'])
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        
        except Exception as e:
            logger.error(f"查找 sglang 进程失败: {e}")
        
        return processes
    
    async def force_kill_all_sglang_processes(self) -> Dict[str, Any]:
        """强制杀死所有 sglang 相关进程"""
        logger.warning("🔥 强制杀死所有 sglang 相关进程...")
        
        kill_result = {
            "killed_processes": [],
            "failed_processes": [],
            "total_killed": 0
        }
        
        try:
            # 方法1: 通过进程列表查找并杀死
            sglang_processes = self._find_sglang_processes()
            
            for proc_info in sglang_processes:
                pid = proc_info["pid"]
                try:
                    proc = psutil.Process(pid)
                    proc.kill()  # 直接使用 SIGKILL
                    
                    # 等待进程结束
                    try:
                        proc.wait(timeout=3)
                        kill_result["killed_processes"].append({
                            "pid": pid,
                            "cmdline": proc_info["cmdline"][:50] + "..."
                        })
                        kill_result["total_killed"] += 1
                        logger.info(f"已杀死进程 {pid}")
                    
                    except psutil.TimeoutExpired:
                        kill_result["failed_processes"].append({
                            "pid": pid,
                            "error": "kill timeout"
                        })
                        logger.warning(f"杀死进程 {pid} 超时")
                
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    kill_result["failed_processes"].append({
                        "pid": pid,
                        "error": str(e)
                    })
            
            # 方法2: 使用系统命令作为备用
            if kill_result["total_killed"] == 0:
                logger.warning("通过进程列表未能杀死进程，尝试使用系统命令...")
                await self._kill_by_command()
            
            # 等待一段时间让进程完全清理
            await asyncio.sleep(2)
            
            # 验证是否还有残留进程
            remaining_processes = self._find_sglang_processes()
            if remaining_processes:
                logger.warning(f"仍有 {len(remaining_processes)} 个进程残留")
                kill_result["remaining_processes"] = len(remaining_processes)
            else:
                logger.success("✅ 所有 sglang 进程已清理完毕")
                kill_result["remaining_processes"] = 0
            
            # 清理跟踪的 PID
            self.tracked_pids.clear()
            self.main_process_pid = None
        
        except Exception as e:
            logger.error(f"强制杀死进程失败: {e}")
            kill_result["error"] = str(e)
        
        StructuredLogger.log_task_event(
            "sglang_health_monitor", "force_kill_completed",
            **kill_result
        )
        
        return kill_result
    
    async def _kill_by_command(self):
        """使用系统命令杀死进程"""
        try:
            # 使用你之前的命令
            cmd = "ps aux | grep mineru-sglang-s | grep -v grep | awk '{print $2}' | xargs kill -9"
            
            # 在 Windows 上使用不同的命令
            if os.name == 'nt':
                # Windows 命令
                result = subprocess.run([
                    "powershell", "-Command",
                    "Get-Process | Where-Object {$_.ProcessName -like '*sglang*' -or $_.CommandLine -like '*mineru-sglang*'} | Stop-Process -Force"
                ], capture_output=True, text=True, timeout=10)
            else:
                # Linux/Unix 命令
                result = subprocess.run(
                    cmd, shell=True, capture_output=True, text=True, timeout=10
                )
            
            if result.returncode == 0:
                logger.info("系统命令执行成功")
            else:
                logger.warning(f"系统命令执行失败: {result.stderr}")
        
        except Exception as e:
            logger.error(f"系统命令执行异常: {e}")
    
    async def auto_restart_if_needed(self) -> bool:
        """根据健康检查结果自动重启"""
        if self.consecutive_failures < self.max_consecutive_failures:
            return False
        
        # 检查重启间隔
        if (self.last_restart_time and 
            datetime.now() - self.last_restart_time < timedelta(seconds=self.min_restart_interval)):
            logger.warning(f"距离上次重启时间过短，跳过自动重启")
            return False
        
        logger.warning(f"🔄 sglang 连续失败 {self.consecutive_failures} 次，执行自动重启...")
        
        try:
            # 强制杀死所有进程
            kill_result = await self.force_kill_all_sglang_processes()
            
            # 等待清理完成
            await asyncio.sleep(3)
            
            # 重新启动服务
            from .sglang_manager import sglang_manager
            restart_success = await sglang_manager.start_server()
            
            if restart_success:
                logger.success("✅ sglang 自动重启成功")
                self.consecutive_failures = 0
                self.last_restart_time = datetime.now()
                
                StructuredLogger.log_task_event(
                    "sglang_health_monitor", "auto_restart_success",
                    kill_result=kill_result
                )
                
                return True
            else:
                logger.error("❌ sglang 自动重启失败")
                return False
        
        except Exception as e:
            logger.error(f"自动重启过程中出错: {e}")
            return False
    
    async def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细状态信息"""
        health_result = await self.health_check()
        
        status = {
            "health_check": health_result,
            "monitor_info": {
                "consecutive_failures": self.consecutive_failures,
                "max_consecutive_failures": self.max_consecutive_failures,
                "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
                "last_restart_time": self.last_restart_time.isoformat() if self.last_restart_time else None,
                "tracked_pids": list(self.tracked_pids),
                "main_process_pid": self.main_process_pid
            },
            "recommendations": []
        }
        
        # 生成建议
        if not health_result["healthy"]:
            if self.consecutive_failures >= self.max_consecutive_failures:
                status["recommendations"].append("建议执行自动重启")
            else:
                status["recommendations"].append(f"连续失败 {self.consecutive_failures} 次，再失败 {self.max_consecutive_failures - self.consecutive_failures} 次将自动重启")
        
        if health_result["process_info"]["total_processes"] > 10:
            status["recommendations"].append("进程数量过多，建议重启服务")
        
        if not health_result["port_status"]["listening"]:
            status["recommendations"].append("端口未监听，服务可能已停止")
        
        return status


# 全局健康监控器实例
sglang_health_monitor = SglangHealthMonitor()
