#!/bin/bash

# 部署管理平台的脚本（基础设施 + 杏仁解析 + Web前端）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查镜像是否存在
    missing_images=()
    if ! docker images | grep -q "almond-parser"; then
        missing_images+=("almond-parser")
    fi
    if ! docker images | grep -q "web-frontend"; then
        missing_images+=("web-frontend")
    fi
    
    if [ ${#missing_images[@]} -gt 0 ]; then
        log_error "缺少镜像: ${missing_images[*]}"
        log_error "请先运行 ./scripts/build.sh 构建镜像"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录"
    
    mkdir -p data/mysql
    mkdir -p data/redis
    mkdir -p data/uploads
    mkdir -p data/output
    mkdir -p logs/mysql
    mkdir -p logs/redis
    mkdir -p logs/almond
    mkdir -p logs/nginx
    
    log_success "目录创建完成"
}

# 部署管理平台
deploy_management() {
    log_info "部署管理平台服务"
    
    # 加载所有环境变量
    if [ -f "config/.env.infrastructure" ]; then
        export $(cat config/.env.infrastructure | grep -v '^#' | xargs)
    fi
    if [ -f "config/.env.almond-parser" ]; then
        export $(cat config/.env.almond-parser | grep -v '^#' | xargs)
    fi
    if [ -f "config/.env.web" ]; then
        export $(cat config/.env.web | grep -v '^#' | xargs)
    fi
    
    # 启动管理平台服务
    docker-compose -f compose/management.yml up -d
    
    log_success "管理平台服务启动成功"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待MySQL
    log_info "等待 MySQL 启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec parserflow-mysql mysqladmin ping -h localhost --silent 2>/dev/null; then
            log_success "MySQL 已就绪"
            break
        fi
        sleep 3
        timeout=$((timeout-3))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "MySQL 启动超时"
        exit 1
    fi
    
    # 等待Redis
    log_info "等待 Redis 启动..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if docker exec parserflow-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
            log_success "Redis 已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Redis 启动超时"
        exit 1
    fi
    
    # 等待杏仁解析服务
    log_info "等待杏仁解析服务启动..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            log_success "杏仁解析服务已就绪"
            break
        fi
        sleep 3
        timeout=$((timeout-3))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "杏仁解析服务启动超时"
        log_info "查看日志："
        docker-compose -f compose/management.yml logs almond-parser --tail=20
        exit 1
    fi
    
    # 等待Web前端
    log_info "等待Web前端启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost/ &> /dev/null; then
            log_success "Web前端已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_warning "Web前端启动超时，但可能正常运行"
    fi
}

# 初始化系统
init_system() {
    log_info "初始化系统"
    
    # 等待数据库迁移完成
    sleep 10
    
    # 检查数据库表是否创建
    if docker exec parserflow-mysql mysql -u parserflow -pparserflow123 parserflow -e "SHOW TABLES;" 2>/dev/null | grep -q "users"; then
        log_success "数据库初始化完成"
    else
        log_info "数据库正在初始化中..."
        sleep 20
    fi
}

# 测试系统功能
test_system() {
    log_info "测试系统功能"
    
    # 测试API健康检查
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "API健康检查通过"
    else
        log_warning "API健康检查失败"
    fi
    
    # 测试Web前端
    if curl -f http://localhost/ &> /dev/null; then
        log_success "Web前端访问正常"
    else
        log_warning "Web前端访问异常"
    fi
    
    # 测试API代理
    if curl -f http://localhost/api/health &> /dev/null; then
        log_success "API代理正常"
    else
        log_warning "API代理异常"
    fi
}

# 显示服务状态
show_status() {
    log_info "=== 管理平台服务状态 ==="
    docker-compose -f compose/management.yml ps
    
    log_info "=== 服务访问地址 ==="
    echo "Web管理平台:   http://localhost"
    echo "杏仁解析API:   http://localhost:8000"
    echo "API文档:       http://localhost:8000/docs"
    echo "MySQL:         localhost:3306"
    echo "Redis:         localhost:6379"
    
    log_info "=== 默认账户信息 ==="
    echo "数据库用户:    parserflow"
    echo "数据库密码:    parserflow123"
    echo "数据库名称:    parserflow"
    
    log_info "=== 数据目录 ==="
    echo "MySQL数据:     ./data/mysql"
    echo "Redis数据:     ./data/redis"
    echo "上传文件:      ./data/uploads"
    echo "输出文件:      ./data/output"
    echo "日志文件:      ./logs/"
    
    log_info "=== 管理命令 ==="
    echo "查看日志:      docker-compose -f compose/management.yml logs -f"
    echo "重启服务:      docker-compose -f compose/management.yml restart"
    echo "停止服务:      docker-compose -f compose/management.yml down"
    echo "添加解析节点:  ./scripts/deploy-mineru-api.sh"
}

# 主函数
main() {
    log_info "开始部署杏仁解析管理平台"
    log_info "包含服务: MySQL + Redis + 杏仁解析 + Web前端 + Nginx"
    
    # 切换到docker目录
    cd "$(dirname "$0")/.."
    
    # 执行部署步骤
    check_environment
    create_directories
    deploy_management
    wait_for_services
    init_system
    test_system
    show_status
    
    log_success "管理平台部署完成！"
    log_info "🎉 访问地址: http://localhost"
    log_info "📚 API文档: http://localhost:8000/docs"
    log_info ""
    log_info "💡 下一步操作："
    echo "  1. 访问 Web 管理平台进行系统配置"
    echo "  2. 运行 ./scripts/deploy-mineru-api.sh 添加解析节点"
    echo "  3. 运行 ./scripts/scale-mineru.sh 3 扩展解析集群"
}

# 执行主函数
main "$@"
