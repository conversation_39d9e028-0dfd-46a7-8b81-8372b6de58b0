#!/usr/bin/env python3
"""
GPU 管理工具
用于检查 GPU 状态和配置 CUDA 设备
"""

import sys
import subprocess
import argparse
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from mineru_api.services.sglang_manager import SglangManager
from mineru_api.config import CUDA_DEVICE_MODE, CUDA_VISIBLE_DEVICES, CUDA_AUTO_SELECT


def check_nvidia_driver():
    """检查 NVIDIA 驱动"""
    try:
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ NVIDIA 驱动正常")
            return True
        else:
            print("❌ NVIDIA 驱动异常")
            return False
    except FileNotFoundError:
        print("❌ 未找到 nvidia-smi 命令，请检查 NVIDIA 驱动安装")
        return False
    except Exception as e:
        print(f"❌ 检查 NVIDIA 驱动失败: {e}")
        return False


def show_gpu_info():
    """显示 GPU 信息"""
    try:
        print("\n🔍 GPU 详细信息:")
        result = subprocess.run([
            "nvidia-smi", 
            "--query-gpu=index,name,memory.used,memory.total,utilization.gpu,temperature.gpu",
            "--format=csv"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("❌ 获取 GPU 信息失败")
            
    except Exception as e:
        print(f"❌ 获取 GPU 信息异常: {e}")


def show_gpu_processes():
    """显示 GPU 进程"""
    try:
        print("\n🔍 GPU 进程信息:")
        result = subprocess.run(["nvidia-smi", "pmon", "-c", "1"], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("❌ 获取 GPU 进程失败")
            
    except Exception as e:
        print(f"❌ 获取 GPU 进程异常: {e}")


def check_sglang_status():
    """检查 sglang 状态"""
    import asyncio
    
    async def _check():
        manager = SglangManager()
        status = await manager.get_status()
        
        print("\n🔍 sglang 服务状态:")
        print(f"  运行状态: {'✅ 运行中' if status['running'] else '❌ 未运行'}")
        print(f"  进程 PID: {status['pid'] or 'N/A'}")
        print(f"  端口占用: {'是' if status['port_in_use'] else '否'}")
        print(f"  健康检查: {'✅ 正常' if status['health_check'] else '❌ 异常'}")
        print(f"  服务地址: {status['url']}")
        
        cuda_config = status['cuda_config']
        print(f"\n🔍 CUDA 配置:")
        print(f"  设备模式: {cuda_config['mode']}")
        print(f"  自动选择: {cuda_config['auto_select']}")
        print(f"  手动设备: {cuda_config['manual_devices'] or 'N/A'}")
        print(f"  可用 GPU: {cuda_config['available_gpus']}")
        print(f"  选中设备: {cuda_config['selected_devices']}")
    
    try:
        asyncio.run(_check())
    except Exception as e:
        print(f"❌ 检查 sglang 状态失败: {e}")


def test_gpu_selection():
    """测试 GPU 选择"""
    manager = SglangManager()
    
    print("\n🧪 测试 GPU 选择:")
    
    # 测试可用 GPU 检测
    available_gpus = manager.get_available_gpus()
    print(f"  可用 GPU: {available_gpus}")
    
    # 测试设备选择
    selected_devices = manager.select_cuda_devices()
    print(f"  选中设备: {selected_devices}")
    
    # 显示当前配置
    print(f"\n📋 当前配置:")
    print(f"  CUDA_DEVICE_MODE: {CUDA_DEVICE_MODE}")
    print(f"  CUDA_VISIBLE_DEVICES: {CUDA_VISIBLE_DEVICES or 'N/A'}")
    print(f"  CUDA_AUTO_SELECT: {CUDA_AUTO_SELECT}")


def start_sglang():
    """启动 sglang 服务"""
    import asyncio
    
    async def _start():
        manager = SglangManager()
        print("🚀 启动 sglang 服务...")
        success = await manager.start_server()
        if success:
            print("✅ sglang 服务启动成功")
        else:
            print("❌ sglang 服务启动失败")
    
    try:
        asyncio.run(_start())
    except Exception as e:
        print(f"❌ 启动 sglang 服务失败: {e}")


def stop_sglang():
    """停止 sglang 服务"""
    import asyncio
    
    async def _stop():
        manager = SglangManager()
        print("🛑 停止 sglang 服务...")
        success = await manager.stop_server()
        if success:
            print("✅ sglang 服务已停止")
        else:
            print("❌ 停止 sglang 服务失败")
    
    try:
        asyncio.run(_stop())
    except Exception as e:
        print(f"❌ 停止 sglang 服务失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="GPU 管理工具")
    parser.add_argument("--check", action="store_true", help="检查 GPU 和驱动状态")
    parser.add_argument("--info", action="store_true", help="显示 GPU 详细信息")
    parser.add_argument("--processes", action="store_true", help="显示 GPU 进程")
    parser.add_argument("--sglang-status", action="store_true", help="检查 sglang 状态")
    parser.add_argument("--test-selection", action="store_true", help="测试 GPU 选择")
    parser.add_argument("--start-sglang", action="store_true", help="启动 sglang 服务")
    parser.add_argument("--stop-sglang", action="store_true", help="停止 sglang 服务")
    parser.add_argument("--all", action="store_true", help="执行所有检查")
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    print("🔧 GPU 管理工具")
    print("=" * 50)
    
    if args.check or args.all:
        check_nvidia_driver()
    
    if args.info or args.all:
        show_gpu_info()
    
    if args.processes or args.all:
        show_gpu_processes()
    
    if args.test_selection or args.all:
        test_gpu_selection()
    
    if args.sglang_status or args.all:
        check_sglang_status()
    
    if args.start_sglang:
        start_sglang()
    
    if args.stop_sglang:
        stop_sglang()


if __name__ == "__main__":
    main()
