# MinerU API 多实例部署指南

## 概述

MinerU API 现在支持多实例部署，可以在多GPU环境下启动多个服务实例，每个实例使用独立的端口和GPU设备。

## 🚀 快速开始

### 1. CLI工具使用

#### 查看SGLang服务状态
```bash
# 查看默认实例状态
python mineru_cli.py sglang status

# 查看所有实例状态
python mineru_cli.py sglang status --all

# 查看指定端口的实例
python mineru_cli.py sglang status --port 30001
```

#### 查看日志
```bash
# 查看默认实例日志
python mineru_cli.py sglang logs

# 跟踪日志
python mineru_cli.py sglang logs -f

# 查看指定实例日志
python mineru_cli.py sglang logs --port 30001 -f
```

#### 服务管理
```bash
# 启动服务
python mineru_cli.py sglang start

# 停止服务
python mineru_cli.py sglang stop

# 重启服务
python mineru_cli.py sglang restart

# 健康检查
python mineru_cli.py sglang health
```

### 2. 单实例启动（支持实例ID）

```bash
# 启动实例0（API端口2233，SGLang端口30000，使用GPU0）
python mineru_api/start_server.py --port 2233 --instance-id 0 --auto-sglang

# 启动实例1（API端口2234，SGLang端口30001，使用GPU1）
CUDA_VISIBLE_DEVICES=1 python mineru_api/start_server.py --port 2234 --instance-id 1 --auto-sglang
```

### 3. 多实例批量启动

```bash
# 启动2个实例，自动分配GPU
python scripts/start_multi_instance.py --count 2

# 启动4个实例，从端口8000开始
python scripts/start_multi_instance.py --count 4 --base-port 8000

# 启动实例但不自动分配GPU
python scripts/start_multi_instance.py --count 2 --no-auto-gpu

# 仅使用pipeline模式（不启动SGLang）
python scripts/start_multi_instance.py --count 2 --backend-mode pipeline-only
```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `INSTANCE_ID` | 0 | 实例ID，影响SGLang端口计算 |
| `SGLANG_BASE_PORT` | 30000 | SGLang基础端口 |
| `MAX_INSTANCES` | 4 | 最大实例数 |

### 端口分配规则

- **API端口**: 用户指定（如2233, 2234...）
- **SGLang端口**: `SGLANG_BASE_PORT + INSTANCE_ID`
  - 实例0: 30000
  - 实例1: 30001
  - 实例2: 30002
  - ...

### 文件路径规则

每个实例使用独立的文件：

- **PID文件**: `.sglang_{port}.pid`
- **日志文件**: `logs/sglang-{port}.log`

## 📋 使用场景

### 场景1: 双GPU服务器

```bash
# 启动两个实例，分别使用GPU0和GPU1
python scripts/start_multi_instance.py --count 2 --base-port 2233

# 结果：
# - 实例0: API端口2233, SGLang端口30000, GPU0
# - 实例1: API端口2234, SGLang端口30001, GPU1
```

### 场景2: 单GPU多实例

```bash
# 启动多个实例共享GPU（适合小模型或测试）
CUDA_VISIBLE_DEVICES=0 python scripts/start_multi_instance.py --count 3 --no-auto-gpu
```

### 场景3: 混合模式

```bash
# 部分实例使用SGLang，部分使用Pipeline
python mineru_api/start_server.py --port 2233 --instance-id 0 --backend-mode sglang-only
python mineru_api/start_server.py --port 2234 --instance-id 1 --backend-mode pipeline-only
```

## 🔍 监控和管理

### 查看所有实例状态
```bash
python mineru_cli.py sglang status --all
```

### 查看特定实例日志
```bash
# 查看端口30001的SGLang日志
python mineru_cli.py sglang logs --port 30001 -f
```

### 停止所有实例
```bash
# 使用多实例脚本启动的可以用Ctrl+C停止
# 或者手动停止各个实例
python mineru_cli.py sglang stop --port 30000
python mineru_cli.py sglang stop --port 30001
```

## ⚠️ 注意事项

1. **端口冲突**: 确保API端口和SGLang端口不冲突
2. **GPU内存**: 多实例共享GPU时注意显存分配
3. **系统资源**: 每个实例都会占用CPU和内存资源
4. **日志管理**: 多实例会产生多个日志文件，注意磁盘空间

## 🐛 故障排除

### 实例启动失败
```bash
# 检查端口占用
netstat -tlnp | grep :2233
netstat -tlnp | grep :30000

# 查看详细日志
python mineru_cli.py sglang logs --port 30000
```

### SGLang服务异常
```bash
# 健康检查
python mineru_cli.py sglang health --port 30000

# 强制重启
python mineru_cli.py sglang stop --port 30000 --force
python mineru_cli.py sglang start --port 30000
```

### GPU分配问题
```bash
# 检查GPU状态
nvidia-smi

# 手动指定GPU
CUDA_VISIBLE_DEVICES=1 python mineru_api/start_server.py --port 2234 --instance-id 1
```

## 📚 API访问

启动多实例后，可以通过不同端口访问：

- 实例0: http://localhost:2233/docs
- 实例1: http://localhost:2234/docs
- SGLang0: http://localhost:30000/health
- SGLang1: http://localhost:30001/health
