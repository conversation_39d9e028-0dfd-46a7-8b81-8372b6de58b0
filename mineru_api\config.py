"""
配置文件
"""
import os
from pathlib import Path
from dotenv import load_dotenv
from loguru import logger
logger.info(f"==========ENV_PATH==========", os.getenv("ENV_PATH", ".env"))
load_dotenv(dotenv_path=os.getenv("ENV_PATH", ".env"))
MINERU_MODEL_SOURCE = os.getenv("MINERU_MODEL_SOURCE", "modelscope")
# 基础配置
BASE_DIR = Path(__file__).parent
OUTPUT_DIR = BASE_DIR / "output"
TEMP_DIR = BASE_DIR / "temp"

# 认证配置
ENABLE_AUTH = os.getenv("ENABLE_AUTH", "false").lower() == "true"
AUTH_BACKEND = os.getenv("AUTH_BACKEND", "file")  # file, sqlite, remote
AUTH_FILE_PATH = os.getenv("AUTH_FILE_PATH", "api_keys.json")
AUTH_SQLITE_PATH = os.getenv("AUTH_SQLITE_PATH", "auth.db")
AUTH_REMOTE_URL = os.getenv("AUTH_REMOTE_URL")
AUTH_REMOTE_API_KEY = os.getenv("AUTH_REMOTE_API_KEY")
AUTH_ENABLE_RATE_LIMIT = os.getenv("AUTH_ENABLE_RATE_LIMIT", "true").lower() == "true"
AUTH_DEFAULT_RATE_LIMIT = int(os.getenv("AUTH_DEFAULT_RATE_LIMIT", 100))

# 兼容性配置（保留原有API_KEY配置）
API_KEY = os.getenv("API_KEY", "mineru-api")

# 确保目录存在
OUTPUT_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)

# 服务配置
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", 8000))
WORKERS = int(os.getenv("WORKERS", 1))

# OCR 配置
DEFAULT_LANG = "ch"
DEFAULT_BACKEND = "pipeline"
DEFAULT_METHOD = "auto"

# sglang 配置
SGLANG_HOST = os.getenv("SGLANG_HOST", "127.0.0.1")
SGLANG_PORT = int(os.getenv("SGLANG_PORT", 30000))  # 默认端口，用于向后兼容
SGLANG_BASE_PORT = int(os.getenv("SGLANG_BASE_PORT", 30000))  # 多实例基础端口
SGLANG_URL = f"http://{SGLANG_HOST}:{SGLANG_PORT}"  # 默认URL，用于向后兼容
SGLANG_HEALTH_CHECK_TIMEOUT = int(os.getenv("SGLANG_HEALTH_CHECK_TIMEOUT", 5))
SGLANG_MEM_FRACTION_STATIC = float(os.getenv("SGLANG_MEM_FRACTION_STATIC", 0.7))

# 多实例配置
INSTANCE_ID = int(os.getenv("INSTANCE_ID", 0))  # 实例ID，默认为0
MAX_INSTANCES = int(os.getenv("MAX_INSTANCES", 4))  # 最大实例数

# CUDA 配置
CUDA_DEVICE_MODE = os.getenv("CUDA_DEVICE_MODE", "auto")  # auto, manual, all
CUDA_VISIBLE_DEVICES = os.getenv("CUDA_VISIBLE_DEVICES", "")  # 手动指定时使用
CUDA_AUTO_SELECT = os.getenv("CUDA_AUTO_SELECT", "true").lower() == "true"  # 是否自动选择可用GPU

# 后端映射配置
BACKEND_MAPPING = {
    "pipeline": "pipeline",
    "vlm": "vlm-sglang-client",
    "sglang": "vlm-sglang-client",
    "vlm-sglang-client": "vlm-sglang-client",
    "vlm-transformers": "vlm-transformers",
    "vlm-sglang-engine": "vlm-sglang-engine"
}

# 任务配置
MAX_CONCURRENT_TASKS = int(os.getenv("MAX_CONCURRENT_TASKS", 5))
TASK_TIMEOUT = int(os.getenv("TASK_TIMEOUT", 300))  # 5分钟超时

# 回调配置
CALLBACK_TIMEOUT = int(os.getenv("CALLBACK_TIMEOUT", 30))
CALLBACK_RETRY_TIMES = int(os.getenv("CALLBACK_RETRY_TIMES", 3))
CALLBACK_RETRY_DELAY = int(os.getenv("CALLBACK_RETRY_DELAY", 5))

# 日志配置
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = BASE_DIR / "logs" / "mineru_api.log"
LOG_FILE.parent.mkdir(exist_ok=True)

# 历史记录配置
HISTORY_RETENTION_DAYS = int(os.getenv("HISTORY_RETENTION_DAYS", 30))
ENABLE_TASK_HISTORY = os.getenv("ENABLE_TASK_HISTORY", "true").lower() == "true"


def get_auth_config():
    """获取认证配置"""
    from mineru_api.auth.models import AuthConfig, AuthBackendType

    # 确定后端类型
    backend_type = AuthBackendType.FILE
    if AUTH_BACKEND == "sqlite":
        backend_type = AuthBackendType.SQLITE
    elif AUTH_BACKEND == "remote":
        backend_type = AuthBackendType.REMOTE

    return AuthConfig(
        backend_type=backend_type,
        file_path=AUTH_FILE_PATH,
        sqlite_path=AUTH_SQLITE_PATH,
        remote_url=AUTH_REMOTE_URL,
        remote_api_key=AUTH_REMOTE_API_KEY,
        enable_rate_limit=AUTH_ENABLE_RATE_LIMIT,
        default_rate_limit=AUTH_DEFAULT_RATE_LIMIT
    )


# 监控配置
ENABLE_PERFORMANCE_LOGGING = os.getenv("ENABLE_PERFORMANCE_LOGGING", "true").lower() == "true"
ENABLE_DETAILED_LOGGING = os.getenv("ENABLE_DETAILED_LOGGING", "true").lower() == "true"

FILE_ACCESS_PREFIX = os.getenv("FILE_ACCESS_PREFIX", "http://localhost:8000/files/")
logger.info(f"FILE_ACCESS_PREFIX: {FILE_ACCESS_PREFIX}")
