<template>
  <div class="filter-container">
    <el-form
      :model="queryParams"
      class="filter-form grid"
      label-position="left"
      label-width="78px"
      size="small"
    >
      <el-form-item label="用户名">
        <el-input v-model="queryParams.username" clearable :prefix-icon="User" placeholder="请输入用户名" />
      </el-form-item>

      <el-form-item label="文件名">
        <el-input v-model="queryParams.file_name" clearable :prefix-icon="Files" placeholder="请输入文件名" />
      </el-form-item>

      <el-form-item label="批次ID">
        <el-input v-model="queryParams.batch_id" clearable :prefix-icon="Search" placeholder="请输入批次ID" />
      </el-form-item>

      <el-form-item label="文档ID">
        <el-input v-model="queryParams.document_id" clearable :prefix-icon="Document" placeholder="请输入文档ID" />
      </el-form-item>

      <el-form-item label="状态">
        <el-select v-model="queryParams.status" clearable placeholder="请选择状态" class="w-full">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
            <el-tag :type="item.type" size="small" effect="light" round>{{ item.label }}</el-tag>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 知识库类型筛选：仅在知识库模式下显示 -->
      <el-form-item label="知识库类型" v-if="sourceType === 'knowledge'">
        <el-select v-model="queryParams.kb_type" clearable placeholder="请选择知识库类型" class="w-full">
          <el-option v-for="item in kbTypeOptions" :key="item.value" :label="item.label" :value="item.value">
            <el-tag :color="item.color" size="small" effect="dark" class="custom-color-tag">{{ item.label }}</el-tag>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 操作区：自动靠右；窄屏独占一行 -->
      <div class="actions">
        <el-space :size="10" wrap>
          <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-space>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Search, Document, Files, Refresh, User } from '@element-plus/icons-vue'
import type { DOCQueryParams, DOCStatusOption, KBTypeOption } from '@/types/docDocument'

const props = defineProps<{
  queryParams: DOCQueryParams
  sourceType: string
}>()
const emit = defineEmits<{ (e: 'search'): void; (e: 'reset'): void }>()

const statusOptions: DOCStatusOption[] = [
  { value: 'PENDING', label: '待处理', type: 'info' },
  { value: 'PROCESSING', label: '处理中', type: 'warning' },
  { value: 'COMPLETED', label: '已完成', type: 'success' },
  { value: 'FAILED', label: '失败', type: 'danger' },
  { value: 'CANCELLED', label: '已取消', type: 'info' }
]

const kbTypeOptions: KBTypeOption[] = [
  { value: 'personal', label: '个人库', color: '#3b82f6' },
  { value: 'project', label: '项目库', color: '#10b981' }
]

const handleSearch = () => emit('search')
const handleReset = () => emit('reset')
</script>

<style scoped>
/* 容器：卡片化 */
.filter-container{
  padding:12px 16px;
  background:#fff;
  border:1px solid #e5e7eb;
  border-radius:12px;
  box-shadow:0 4px 18px rgba(0,0,0,.05);
}

/* 栅格：自适应列数（桌面 4 列），控件自然拉伸 */
.filter-form.grid{
  display:grid;
  grid-template-columns: repeat(4, minmax(240px, 1fr));
  gap:14px 16px;
  align-items:center;
}

/* 自动等宽控件 */
:deep(.el-form-item){
  margin-bottom:0 !important;
}
:deep(.el-form-item .el-form-item__content){
  width:100%;
}
.w-full{ width:100%; }

/* 标签更克制一些 */
:deep(.el-form-item__label){
  color:#6b7280;
  font-weight:600;
}

/* 操作区：占末两列并靠右；列数减少时自动换行 */
.actions{
  grid-column: -3 / -1;
  justify-self: end;
}
@media (max-width: 1400px){
  .filter-form.grid{ grid-template-columns: repeat(3, minmax(220px, 1fr)); }
  .actions{ grid-column: -3 / -1; }
}
@media (max-width: 1100px){
  .filter-form.grid{ grid-template-columns: repeat(2, minmax(220px, 1fr)); }
  .actions{ grid-column: 1 / -1; justify-self: end; }
}
@media (max-width: 680px){
  .filter-form.grid{ grid-template-columns: 1fr; }
  .actions{ grid-column: 1 / -1; justify-self: stretch; }
  :deep(.el-space){ width:100%; justify-content:flex-end; }
  :deep(.el-button){ width:auto; }
}

/* 统一输入/选择聚焦反馈 */
:deep(.el-input__wrapper:hover),
:deep(.el-input__wrapper.is-focus),
:deep(.el-select__wrapper:hover),
:deep(.el-select__wrapper.is-focus){
  box-shadow:0 0 0 1px var(--el-color-primary) inset;
}

.custom-color-tag{
  color:#fff; border:none; border-radius:4px;
}
</style>
