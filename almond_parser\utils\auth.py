# -*- encoding: utf-8 -*-
"""
认证工具
"""
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from almond_parser.config import settings
from almond_parser.db import get_db
from almond_parser.db.models import Api<PERSON>ey, User

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer 认证
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def generate_api_key() -> str:
    """生成API密钥"""
    random_part = secrets.token_urlsafe(settings.API_KEY_LENGTH)
    return f"{settings.API_KEY_PREFIX}{random_part}"


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        return payload
    except JWTError:
        return None


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 验证JWT令牌
        payload = verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception

        # 查询用户信息
        result = await db.execute(select(User).where(User.username == username))
        user = result.scalar_one_or_none()
        
        if user is None:
            raise credentials_exception
        
        return {
            "user_id": user.id,
            "username": user.username,
            "is_admin": user.is_admin,
            "is_active": user.is_active
        }
        
    except Exception as e:
        logger.error(f"用户认证失败: {e}")
        raise credentials_exception


async def get_current_api_key(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> ApiKey:
    """获取当前API密钥"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid API key",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        api_key = credentials.credentials
        
        # 查询API密钥
        result = await db.execute(select(ApiKey).where(ApiKey.key == api_key))
        api_key_obj = result.scalar_one_or_none()
        
        if api_key_obj is None:
            raise credentials_exception
        
        # 检查是否启用
        if not api_key_obj.is_enabled:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key is disabled"
            )
        
        # 检查是否过期
        if api_key_obj.expires_at and api_key_obj.expires_at < datetime.now():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key has expired"
            )
        
        # 异步更新使用次数和最后使用时间（不等待提交，提升性能）
        # 只在需要时更新，避免每次请求都写数据库
        now = datetime.now()
        if (api_key_obj.last_used_at is None or
            (now - api_key_obj.last_used_at).total_seconds() > 60):  # 1分钟内只更新一次
            api_key_obj.usage_count += 1
            api_key_obj.last_used_at = now
            # 使用后台任务异步提交，不阻塞请求
            try:
                await db.commit()
            except Exception as e:
                logger.warning(f"更新API Key使用记录失败: {e}")
                # 不影响主要业务流程
        
        return api_key_obj
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API密钥认证失败: {e}")
        raise credentials_exception


async def authenticate_user(username: str, password: str, db: AsyncSession) -> Optional[User]:
    """认证用户"""
    try:
        result = await db.execute(select(User).where(User.username == username))
        user = result.scalar_one_or_none()
        
        if not user:
            return None
        
        if not verify_password(password, user.password):
            return None
        
        return user
        
    except Exception as e:
        logger.error(f"用户认证失败: {e}")
        return None


def hash_api_key(api_key: str) -> str:
    """对API密钥进行哈希处理（用于日志记录等）"""
    return hashlib.sha256(api_key.encode()).hexdigest()[:16]
