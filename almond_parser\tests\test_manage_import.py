#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试 manage.py 导入和基本功能
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有导入"""
    print("🔍 测试导入...")
    
    try:
        # 测试基础导入
        from almond_parser.config import settings
        print("✅ config 导入成功")
        
        from almond_parser.db import get_db
        print("✅ db 导入成功")
        
        from almond_parser.db.models import User, Document, DocumentStatus, MinerUNode
        print("✅ models 导入成功")
        
        from almond_parser.utils.auth import verify_password, create_access_token, get_current_user, get_password_hash
        print("✅ auth 导入成功")
        
        from almond_parser.services.mineru_node_service import MinerUNodeService
        print("✅ mineru_node_service 导入成功")
        
        from almond_parser.tasks.document_tasks import process_document
        print("✅ document_tasks 导入成功")
        
        # 测试 manage 路由导入
        from almond_parser.api.manage import router
        print("✅ manage router 导入成功")
        
        print(f"📊 manage router 包含 {len(router.routes)} 个路由")
        
        # 打印所有路由
        for route in router.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                methods = ', '.join(route.methods)
                print(f"  - {methods} {route.path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_router_structure():
    """测试路由结构"""
    print("\n🔍 测试路由结构...")
    
    try:
        from almond_parser.api.manage import router
        
        # 检查路由前缀
        print(f"📍 路由前缀: {router.prefix}")
        print(f"🏷️  路由标签: {router.tags}")
        
        # 检查关键路由是否存在
        expected_routes = [
            "/login",
            "/me", 
            "/documents",
            "/retry/{document_id}",
            "/register",
            "/ws/logs/document/{document_id}",
            "/mineru/nodes",
            "/system/stats"
        ]
        
        actual_paths = [route.path for route in router.routes if hasattr(route, 'path')]
        
        for expected in expected_routes:
            if expected in actual_paths:
                print(f"✅ 路由存在: {expected}")
            else:
                print(f"❌ 路由缺失: {expected}")
        
        return True
        
    except Exception as e:
        print(f"❌ 路由结构测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试 manage.py...")
    
    success = True
    
    # 测试导入
    if not test_imports():
        success = False
    
    # 测试路由结构
    if not test_router_structure():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！manage.py 工作正常")
        return 0
    else:
        print("\n❌ 测试失败！请检查错误信息")
        return 1

if __name__ == "__main__":
    exit(main())
