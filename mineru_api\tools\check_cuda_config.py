#!/usr/bin/env python3
"""
CUDA 配置检查工具
用于验证和诊断 CUDA 配置问题
"""

import sys
import os
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from mineru_api.config import CUDA_DEVICE_MODE, CUDA_VISIBLE_DEVICES, CUDA_AUTO_SELECT


def check_config_validity():
    """检查配置有效性"""
    print("🔍 检查 CUDA 配置...")
    
    valid_modes = ["auto", "manual", "all"]
    issues = []
    
    # 检查 CUDA_DEVICE_MODE
    print(f"  CUDA_DEVICE_MODE: {CUDA_DEVICE_MODE}")
    if CUDA_DEVICE_MODE not in valid_modes:
        issues.append(f"❌ CUDA_DEVICE_MODE 无效: {CUDA_DEVICE_MODE}")
        issues.append(f"   有效值: {', '.join(valid_modes)}")
    else:
        print(f"  ✅ CUDA_DEVICE_MODE 有效")
    
    # 检查 CUDA_VISIBLE_DEVICES
    print(f"  CUDA_VISIBLE_DEVICES: '{CUDA_VISIBLE_DEVICES}'")
    if CUDA_DEVICE_MODE == "manual":
        if not CUDA_VISIBLE_DEVICES:
            issues.append("❌ CUDA_DEVICE_MODE=manual 但 CUDA_VISIBLE_DEVICES 未设置")
            issues.append("   请在 .env 文件中设置: CUDA_VISIBLE_DEVICES=1")
        else:
            print(f"  ✅ CUDA_VISIBLE_DEVICES 已设置")
    
    # 检查 CUDA_AUTO_SELECT
    print(f"  CUDA_AUTO_SELECT: {CUDA_AUTO_SELECT}")
    if CUDA_DEVICE_MODE == "auto" and not CUDA_AUTO_SELECT:
        issues.append("⚠️  CUDA_DEVICE_MODE=auto 但 CUDA_AUTO_SELECT=false")
        issues.append("   这将始终使用 GPU 0，可能不是您想要的")
    
    return issues


def show_recommended_configs():
    """显示推荐配置"""
    print("\n📋 推荐配置示例:")
    
    print("\n1️⃣  使用特定 GPU (如 GPU 1):")
    print("   CUDA_DEVICE_MODE=manual")
    print("   CUDA_VISIBLE_DEVICES=1")
    print("   CUDA_AUTO_SELECT=false")
    
    print("\n2️⃣  自动选择可用 GPU:")
    print("   CUDA_DEVICE_MODE=auto")
    print("   CUDA_AUTO_SELECT=true")
    print("   # CUDA_VISIBLE_DEVICES=  (注释掉)")
    
    print("\n3️⃣  使用多个 GPU:")
    print("   CUDA_DEVICE_MODE=manual")
    print("   CUDA_VISIBLE_DEVICES=0,1")
    print("   CUDA_AUTO_SELECT=false")
    
    print("\n4️⃣  使用所有 GPU:")
    print("   CUDA_DEVICE_MODE=all")
    print("   CUDA_AUTO_SELECT=false")
    print("   # CUDA_VISIBLE_DEVICES=  (注释掉)")


def test_device_selection():
    """测试设备选择逻辑"""
    print("\n🧪 测试设备选择逻辑...")
    
    try:
        from mineru_api.services.sglang_manager import SglangManager
        manager = SglangManager()
        
        # 测试设备选择
        selected_devices = manager.select_cuda_devices()
        print(f"  选中的设备: {selected_devices}")
        
        # 显示可用 GPU
        available_gpus = manager.get_available_gpus()
        print(f"  可用的 GPU: {available_gpus}")
        
        return True
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False


def show_env_file_example():
    """显示 .env 文件示例"""
    print("\n📄 .env 文件配置示例:")
    print("=" * 40)
    
    if CUDA_DEVICE_MODE not in ["auto", "manual", "all"]:
        print("# 修复后的配置 (使用 GPU 1)")
        print("CUDA_DEVICE_MODE=manual")
        print("CUDA_VISIBLE_DEVICES=1")
        print("CUDA_AUTO_SELECT=false")
    else:
        print("# 当前配置")
        print(f"CUDA_DEVICE_MODE={CUDA_DEVICE_MODE}")
        if CUDA_VISIBLE_DEVICES:
            print(f"CUDA_VISIBLE_DEVICES={CUDA_VISIBLE_DEVICES}")
        else:
            print("# CUDA_VISIBLE_DEVICES=")
        print(f"CUDA_AUTO_SELECT={str(CUDA_AUTO_SELECT).lower()}")


def main():
    """主函数"""
    print("🔧 CUDA 配置检查工具")
    print("=" * 50)
    
    # 显示当前配置
    print(f"📋 当前配置:")
    print(f"  CUDA_DEVICE_MODE: {CUDA_DEVICE_MODE}")
    print(f"  CUDA_VISIBLE_DEVICES: '{CUDA_VISIBLE_DEVICES}'")
    print(f"  CUDA_AUTO_SELECT: {CUDA_AUTO_SELECT}")
    
    # 检查配置有效性
    issues = check_config_validity()
    
    if issues:
        print(f"\n❌ 发现 {len(issues)} 个配置问题:")
        for issue in issues:
            print(f"  {issue}")
    else:
        print(f"\n✅ 配置检查通过")
    
    # 测试设备选择
    test_device_selection()
    
    # 显示推荐配置
    show_recommended_configs()
    
    # 显示 .env 文件示例
    show_env_file_example()
    
    if issues:
        print(f"\n🔧 修复建议:")
        print(f"  1. 编辑 .env 文件")
        print(f"  2. 修正上述配置问题")
        print(f"  3. 重新启动服务")
        print(f"  4. 再次运行此工具验证")


if __name__ == "__main__":
    main()
