# 回调错误排查和修复指南

## 问题描述

错误信息：`unable to perform operation on <TCPTransport closed=True reading=False>; the handler is closed`

这个错误表明 HTTP 客户端连接已经关闭，但代码仍在尝试使用它发送回调请求。

## 根本原因

1. **HTTP 客户端生命周期管理问题**
   - `httpx.AsyncClient` 实例被意外关闭
   - 在多线程环境中共享客户端实例导致竞态条件

2. **异步事件循环处理不当**
   - 在线程池中不正确地创建和管理事件循环
   - 事件循环关闭后仍尝试执行异步操作

3. **网络连接超时或中断**
   - 长时间运行的服务中连接池被清理
   - 网络不稳定导致连接断开

## 修复方案

### 1. 改进 HTTP 客户端管理

```python
class CallbackService:
    def __init__(self):
        self._client = None
        self._client_lock = threading.Lock()
    
    def _get_client(self) -> httpx.AsyncClient:
        """获取或创建HTTP客户端（线程安全）"""
        if self._client is None or self._client.is_closed:
            with self._client_lock:
                if self._client is None or self._client.is_closed:
                    self._client = httpx.AsyncClient(timeout=CALLBACK_TIMEOUT)
        return self._client
```

### 2. 线程安全的回调处理

```python
class CallbackHandler:
    def __init__(self):
        self._executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="callback")
    
    def send_callback_sync(self, task_id: str, callback_url: str, headers=None):
        """同步方式发送回调（在线程池中执行）"""
        future = self._executor.submit(self._run_async_callback, task_id, callback_url, headers)
```

### 3. 错误重试和连接重置

- 检测连接相关错误并自动重置客户端
- 实现指数退避重试机制
- 正确处理事件循环生命周期

## 配置优化建议

### 环境变量配置

```bash
# 回调配置
CALLBACK_TIMEOUT=30          # 回调超时时间（秒）
CALLBACK_RETRY_TIMES=3       # 重试次数
CALLBACK_RETRY_DELAY=5       # 重试间隔（秒）

# 连接池配置
HTTPX_POOL_CONNECTIONS=10    # 连接池大小
HTTPX_POOL_MAXSIZE=20        # 最大连接数
HTTPX_KEEPALIVE_EXPIRY=30    # 连接保活时间
```

### 日志监控

启用详细的回调日志来监控问题：

```python
# 在 config.py 中
LOG_LEVEL = "DEBUG"  # 临时启用调试日志

# 监控关键指标
- 回调成功率
- 连接错误频率
- 重试次数统计
```

## 测试验证

### 1. 运行回调测试

```bash
cd mineru_api
python tests/test_callback_fix.py
```

### 2. 压力测试

```bash
# 启动回调测试服务器
python tests/callback_test_server.py

# 在另一个终端运行压力测试
python tests/stress_test_callbacks.py
```

### 3. 监控日志

```bash
tail -f logs/mineru_api.log | grep -E "(回调|callback|TCPTransport)"
```

## 预防措施

1. **定期健康检查**
   - 实现回调服务健康检查端点
   - 监控连接池状态

2. **资源清理**
   - 应用关闭时正确清理资源
   - 定期清理过期连接

3. **错误告警**
   - 设置回调失败率告警
   - 监控连接错误趋势

## 故障排查步骤

1. **检查日志**
   ```bash
   grep -A 5 -B 5 "TCPTransport closed" logs/mineru_api.log
   ```

2. **验证网络连接**
   ```bash
   curl -X POST http://your-callback-url/webhook -d '{"test": true}'
   ```

3. **检查资源使用**
   ```bash
   # 检查文件描述符使用情况
   lsof -p $(pgrep -f mineru_api) | wc -l
   
   # 检查网络连接
   netstat -an | grep :8000
   ```

4. **重启服务**
   ```bash
   # 如果问题持续，重启服务
   pkill -f mineru_api
   python main.py
   ```

## 相关文件

- `mineru_api/services/callback_service.py` - 回调服务实现
- `mineru_api/utils/callback_utils.py` - 回调处理工具
- `mineru_api/services/task_manager.py` - 任务管理器
- `mineru_api/config.py` - 配置文件
- `mineru_api/main.py` - 主程序（包含资源清理）
