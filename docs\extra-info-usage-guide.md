# 杏仁解析扩展信息使用指南

## 概述

杏仁解析系统新增了两个字段来增强文档管理功能：

1. **备注字段 (remarks)**: 用于存储简单的文本备注信息
2. **扩展信息字段 (extra_info)**: 用于存储JSON格式的结构化扩展信息

这两个字段都是可选的，为系统提供了更好的扩展性和灵活性。

## 字段说明

### 备注字段 (remarks)
- **类型**: TEXT
- **用途**: 存储简单的文本备注信息
- **限制**: 最大500字符
- **示例**: "重要文档，需要优先处理"

### 扩展信息字段 (extra_info)
- **类型**: JSON
- **用途**: 存储结构化的扩展信息
- **格式**: 标准JSON对象
- **示例**: 
```json
{
  "account": "AI中心",
  "user_name": "金烨",
  "department": "上海总部-信息技术部",
  "user_id": "jin.ye",
  "module": "多文档解读",
  "project": "个人库"
}
```

## API 使用方式

### 1. 文档上传时传递扩展信息

```bash
curl -X POST "http://localhost:8000/api/v1/document/upload" \
  -H "X-API-Key: your-api-key" \
  -F "files=@document.pdf" \
  -F "remarks=重要文档，需要优先处理" \
  -F 'extra_info={"account":"AI中心","user_name":"金烨","department":"上海总部-信息技术部","user_id":"jin.ye","module":"多文档解读","project":"个人库"}'
```

### 2. Python 客户端示例

```python
import requests
import json

# 准备扩展信息
extra_info = {
    "account": "AI中心",
    "user_name": "金烨", 
    "department": "上海总部-信息技术部",
    "user_id": "jin.ye",
    "module": "多文档解读",
    "project": "个人库"
}

# 上传文档
files = {'files': open('document.pdf', 'rb')}
data = {
    'remarks': '重要文档，需要优先处理',
    'extra_info': json.dumps(extra_info),
    'priority': 5,
    'service_type': 'document',
    'parse_mode': 'sglang'
}
headers = {'X-API-Key': 'your-api-key'}

response = requests.post(
    'http://localhost:8000/api/v1/document/upload',
    files=files,
    data=data,
    headers=headers
)
```

### 3. JavaScript 客户端示例

```javascript
const formData = new FormData();
formData.append('files', fileInput.files[0]);
formData.append('remarks', '重要文档，需要优先处理');

const extraInfo = {
    account: "AI中心",
    user_name: "金烨",
    department: "上海总部-信息技术部", 
    user_id: "jin.ye",
    module: "多文档解读",
    project: "个人库"
};
formData.append('extra_info', JSON.stringify(extraInfo));

fetch('/api/v1/document/upload', {
    method: 'POST',
    headers: {
        'X-API-Key': 'your-api-key'
    },
    body: formData
});
```

## 前端显示

### 1. 文档列表页面
- 新增"备注"列，显示备注信息
- 如果没有备注则显示"-"

### 2. 文档详情页面
- 在基本信息中显示备注信息
- 在扩展信息部分显示所有extra_info字段
- 支持复杂JSON数据的格式化显示

## 数据库迁移

### 执行迁移
```bash
# 添加新字段
python almond_parser/migrations/add_remarks_and_extra_info.py

# 回滚迁移（如果需要）
python almond_parser/migrations/add_remarks_and_extra_info.py --rollback
```

### 字段定义
```sql
-- 备注字段
ALTER TABLE documents ADD COLUMN remarks TEXT NULL COMMENT '备注信息';

-- 扩展信息字段  
ALTER TABLE documents ADD COLUMN extra_info JSON NULL COMMENT '扩展信息(JSON格式)';
```

## 使用场景

### 1. AI中心调用场景
当AI中心调用杏仁解析时，可以传递用户和模块信息：

```json
{
  "account": "AI中心",
  "user_name": "金烨",
  "department": "上海总部-信息技术部",
  "user_id": "jin.ye", 
  "module": "多文档解读",
  "library_type": "个人库",
  "session_id": "sess_123456",
  "request_time": "2024-01-15T10:30:00Z"
}
```

### 2. 项目管理场景
```json
{
  "project_name": "智能客服系统",
  "project_id": "proj_001",
  "team": "产品研发部",
  "priority_level": "high",
  "deadline": "2024-02-01"
}
```

### 3. 文档分类场景
```json
{
  "document_category": "技术文档",
  "document_type": "API文档", 
  "version": "v1.2.0",
  "author": "技术团队",
  "tags": ["API", "接口", "开发"]
}
```

## 注意事项

1. **JSON格式验证**: extra_info字段必须是有效的JSON格式，否则会被忽略
2. **字段长度**: remarks字段建议不超过500字符
3. **向后兼容**: 新字段都是可选的，不会影响现有功能
4. **性能考虑**: JSON字段查询性能相对较低，不建议用于频繁查询的场景
5. **数据安全**: 不要在扩展信息中存储敏感数据如密码、密钥等

## 扩展建议

未来可以基于扩展信息字段实现：

1. **智能分类**: 根据extra_info自动分类文档
2. **权限控制**: 基于用户信息实现细粒度权限控制  
3. **统计分析**: 按部门、项目等维度统计解析量
4. **自动化流程**: 根据扩展信息触发不同的处理流程
5. **审计日志**: 记录详细的操作来源和上下文信息
