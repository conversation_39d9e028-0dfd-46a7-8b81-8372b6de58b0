#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
数据库迁移脚本：添加SGLang监控字段
"""
import asyncio
import sys
from pathlib import Path
from sqlalchemy import text
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.db.database import get_async_session


async def add_sglang_monitoring_fields():
    """添加SGLang监控相关字段"""
    
    # SQL语句列表
    sql_statements = [
        # 添加SGLang状态枚举类型
        """
        ALTER TABLE mineru_nodes
        ADD COLUMN sglang_status ENUM('ONLINE', 'OFFLINE', 'ERROR', 'UNKNOWN', 'RESTARTING')
        DEFAULT 'UNKNOWN'
        COMMENT 'SGLang服务状态'
        """,
        
        # 添加SGLang端口字段
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_port INT 
        COMMENT 'SGLang服务端口'
        """,
        
        # 添加SGLang URL字段
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_url VARCHAR(500) 
        COMMENT 'SGLang服务URL'
        """,
        
        # 添加SGLang最后检查时间
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_last_check DATETIME 
        COMMENT 'SGLang最后检查时间'
        """,
        
        # 添加SGLang连续失败次数
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_consecutive_failures INT 
        DEFAULT 0 
        COMMENT 'SGLang连续失败次数'
        """,
        
        # 添加SGLang最后重启时间
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_last_restart DATETIME 
        COMMENT 'SGLang最后重启时间'
        """,
        
        # 添加SGLang重启次数
        """
        ALTER TABLE mineru_nodes 
        ADD COLUMN sglang_restart_count INT 
        DEFAULT 0 
        COMMENT 'SGLang重启次数'
        """
    ]
    
    try:
        async with get_async_session() as db:
            logger.info("开始添加SGLang监控字段...")
            
            for i, sql in enumerate(sql_statements, 1):
                try:
                    logger.info(f"执行SQL语句 {i}/{len(sql_statements)}")
                    await db.execute(text(sql.strip()))
                    await db.commit()
                    logger.success(f"✅ SQL语句 {i} 执行成功")
                except Exception as e:
                    if "Duplicate column name" in str(e):
                        logger.warning(f"⚠️ 字段已存在，跳过: {e}")
                    else:
                        logger.error(f"❌ SQL语句 {i} 执行失败: {e}")
                        raise
            
            logger.success("🎉 SGLang监控字段添加完成")
            
    except Exception as e:
        logger.error(f"添加SGLang监控字段失败: {e}")
        raise


async def update_existing_nodes_sglang_info():
    """更新现有节点的SGLang信息"""
    try:
        async with get_async_session() as db:
            logger.info("开始更新现有节点的SGLang信息...")
            
            # 查询所有节点
            result = await db.execute(text("SELECT id, host, port FROM mineru_nodes"))
            nodes = result.fetchall()
            
            logger.info(f"找到 {len(nodes)} 个节点需要更新")
            
            for node in nodes:
                node_id, host, port = node
                
                # 计算SGLang端口和URL
                base_mineru_port = 2233
                base_sglang_port = 30000
                sglang_port = base_sglang_port + (port - base_mineru_port)
                sglang_url = f"http://{host}:{sglang_port}"
                
                # 更新节点信息
                update_sql = """
                UPDATE mineru_nodes
                SET sglang_port = :sglang_port,
                    sglang_url = :sglang_url,
                    sglang_status = 'UNKNOWN'
                WHERE id = :node_id
                """
                
                await db.execute(text(update_sql), {
                    "sglang_port": sglang_port,
                    "sglang_url": sglang_url,
                    "node_id": node_id
                })
                
                logger.info(f"更新节点 {node_id}: {sglang_url}")
            
            await db.commit()
            logger.success(f"✅ 成功更新 {len(nodes)} 个节点的SGLang信息")
            
    except Exception as e:
        logger.error(f"更新现有节点SGLang信息失败: {e}")
        raise


async def verify_migration():
    """验证迁移结果"""
    try:
        async with get_async_session() as db:
            logger.info("验证迁移结果...")
            
            # 检查字段是否存在
            result = await db.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'mineru_nodes' 
                AND COLUMN_NAME LIKE 'sglang_%'
            """))
            
            columns = [row[0] for row in result.fetchall()]
            expected_columns = [
                'sglang_status', 'sglang_port', 'sglang_url', 
                'sglang_last_check', 'sglang_consecutive_failures',
                'sglang_last_restart', 'sglang_restart_count'
            ]
            
            missing_columns = set(expected_columns) - set(columns)
            if missing_columns:
                logger.error(f"❌ 缺少字段: {missing_columns}")
                return False
            
            logger.success(f"✅ 所有SGLang字段已添加: {columns}")
            
            # 检查数据
            result = await db.execute(text("""
                SELECT COUNT(*) as total,
                       COUNT(sglang_url) as with_url,
                       COUNT(sglang_port) as with_port
                FROM mineru_nodes
            """))
            
            stats = result.fetchone()
            logger.info(f"节点统计: 总计 {stats[0]}, 有URL {stats[1]}, 有端口 {stats[2]}")
            
            return True
            
    except Exception as e:
        logger.error(f"验证迁移结果失败: {e}")
        return False


async def main():
    """主函数"""
    try:
        logger.info("🚀 开始SGLang监控字段迁移")
        
        # 1. 添加字段
        await add_sglang_monitoring_fields()
        
        # 2. 更新现有数据
        await update_existing_nodes_sglang_info()
        
        # 3. 验证结果
        if await verify_migration():
            logger.success("🎉 SGLang监控字段迁移完成")
        else:
            logger.error("❌ 迁移验证失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
