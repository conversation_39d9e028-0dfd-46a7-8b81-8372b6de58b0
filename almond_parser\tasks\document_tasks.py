# -*- encoding: utf-8 -*-
"""
文档处理任务
"""
import asyncio
import base64
from datetime import datetime

import aiohttp
import json
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from almond_parser.config import settings
from almond_parser.db import db_manager
from almond_parser.db.models import Document, DocumentStatus, DocumentLog, MinerUNode, NodeStatus
from almond_parser.services.document_service import DocumentService
from almond_parser.services.mineru_node_service import MinerUNodeService


async def process_document(
        ctx: Dict[str, Any],
        document_id: str,
        user_id: str,
        service_type: str = "auto",
        parse_mode: str = "auto",
        max_retries: int = 2,
        config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    处理文档解析任务

    Args:
        ctx: ARQ 上下文
        document_id: 文档ID
        user_id: 用户ID
        service_type: 服务类型
        parse_mode: 解析模式
        max_retries: 最大重试次数
        config: 解析配置
        user_id: 用户ID
        config: 解析配置

    Returns:
        任务结果
    """
    logger.info(f"开始处理文档: {document_id}, 用户: {user_id}")

    try:
        if config is None:
            config = {}
        if parse_mode == 'auto' or parse_mode == 'vlm' or parse_mode == 'sglang':
            backend = 'vlm-sglang-client'
        elif parse_mode == 'pipe' or parse_mode == 'pipeline':
            backend = 'pipeline'
        else:
            backend = 'vlm-sglang-client'

        config["backend"] = backend
        # 获取数据库会话
        async with db_manager.session_factory() as db:
            # 查询文档信息
            from sqlalchemy import select
            result = await db.execute(
                select(Document).where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                error_msg = f"文档不存在: {document_id}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # 更新文档状态为等待处理，记录提交时间防冲突
            document.status = DocumentStatus.PENDING
            document.progress = 0
            document.started_at = datetime.now()  # 记录提交时间
            await db.commit()

            # 记录开始处理日志
            await _log_document_event(
                db, document_id, "INFO",
                f"开始处理文档: {document.file_name}"
            )

            # 使用简化的节点管理器分配节点
            from almond_parser.utils.simplified_node_manager import simplified_node_manager
            from almond_parser.db.models import ServiceType

            # 映射服务类型 - 注意：auto类型在node_selector中特殊处理
            service_type_enum = {
                "document": ServiceType.DOCUMENT,
                "knowledge_base": ServiceType.KNOWLEDGE_BASE,
                "auto": ServiceType.UNIVERSAL  # 这里保持兼容，实际逻辑在node_selector中
            }.get(service_type, ServiceType.UNIVERSAL)

            node = await simplified_node_manager.allocate_node(
                db=db,
                service_type=service_type_enum,
                parse_mode=parse_mode if parse_mode != "auto" else None,
                document_id=document_id
            )

            if not node:
                # 节点不可用，使用增强的重试机制
                await _handle_node_unavailable_legacy(db, document, service_type, parse_mode)
                return {
                    "success": False,
                    "document_id": document_id,
                    "error": "no_available_nodes",
                    "retry_scheduled": True
                }

            # 更新文档的处理节点
            document.node_id = node.id
            await db.commit()

            # 记录节点分配日志
            await _log_document_event(
                db, document_id, "INFO",
                f"分配到节点: {node.name} ({node.base_url})"
            )

            # 调用 MinerU API
            api_result = await _call_mineru_api(node, document, config)

            if api_result["success"]:
                # 任务提交成功，节点已经在allocate_node时增加了current_tasks
                # 这里只需要记录任务ID
                task_confirmed = True

                if not task_confirmed:
                    logger.error(f"任务确认失败: {document_id}")
                    raise Exception("任务确认失败")

                # 更新文档状态为解析中，记录真正的处理开始时间
                document.status = DocumentStatus.PARSING
                document.progress = 10
                document.started_at = datetime.now()  # 更新为真正的处理开始时间
                document.task_id = api_result["task_id"]
                await db.commit()

                # 记录API调用成功日志
                await _log_document_event(
                    db, document_id, "INFO",
                    f"MinerU API 调用成功: {api_result.get('message', '任务已提交')}"
                )

                logger.info(f"文档提交到 MinerU 成功: {document_id}")
                return {
                    "success": True,
                    "document_id": document_id,
                    "status": "submitted_to_mineru",
                    "node": node.name,
                    "task_id": api_result.get("task_id")
                }
            else:
                # API 调用失败，释放任务槽位
                await simplified_node_manager.release_node(
                    db=db,
                    node_id=node.id,
                    task_id="failed_submission",
                    success=False,
                    reason="API调用失败"
                )
                raise Exception(f"MinerU API 调用失败: {api_result['error']}")

            # # 更新文档状态为完成
            # document.status = DocumentStatus.COMPLETED
            # document.progress = 100
            # await db.commit()
            #
            # # 记录完成日志
            # await _log_document_event(
            #     db, document_id, "INFO",
            #     f"文档处理完成: {document.file_name}"
            # )
            #
            # logger.info(f"文档处理完成: {document_id}")
            # return {
            #     "success": True,
            #     "document_id": document_id,
            #     "status": "completed"
            # }

    except Exception as e:
        error_msg = f"文档处理失败: {document_id}, 错误: {str(e)}"
        logger.error(error_msg)

        # 更新文档状态为失败并释放节点槽位
        try:
            async with db_manager.session_factory() as db:
                result = await db.execute(
                    select(Document).where(Document.document_id == document_id)
                )
                document = result.scalar_one_or_none()
                if document:
                    # 释放节点任务槽位（如果有分配的节点）
                    if document.node_id:
                        await simplified_node_manager.release_node(
                            db=db,
                            node_id=int(document.node_id),
                            task_id=document.task_id or "unknown",
                            success=False,
                            reason="任务异常失败"
                        )

                    document.status = DocumentStatus.FAILED
                    document.error_message = str(e)
                    await db.commit()

                    # 记录错误日志
                    await _log_document_event(
                        db, document_id, "ERROR", error_msg
                    )
        except Exception as log_error:
            logger.error(f"记录错误日志失败: {log_error}")

        return {"success": False, "error": str(e)}


async def retry_document(
        ctx: Dict[str, Any],
        document_id: str,
        user_id: str,
        force: bool = False
) -> Dict[str, Any]:
    """
    重试文档处理

    Args:
        ctx: ARQ 上下文
        document_id: 文档ID
        user_id: 用户ID
        force: 是否强制重试

    Returns:
        任务结果
    """
    logger.info(f"重试文档处理: {document_id}, 用户: {user_id}, 强制: {force}")

    try:
        async with db_manager.session_factory() as db:
            from sqlalchemy import select
            result = await db.execute(
                select(Document).where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                return {"success": False, "error": "文档不存在"}

            # 检查是否可以重试
            if not force and document.retry_count >= document.max_retries:
                return {"success": False, "error": "超过最大重试次数"}

            # 增加重试次数
            document.retry_count += 1
            document.status = DocumentStatus.UPLOADED
            document.error_message = None
            document.progress = 0
            await db.commit()

            # 记录重试日志
            await _log_document_event(
                db, document_id, "INFO",
                f"重试文档处理 (第{document.retry_count}次)"
            )

            # 重新提交处理任务
            from .arq_app import arq_manager
            job_id = await arq_manager.enqueue_task(
                "process_document",
                document_id=document_id,
                user_id=user_id
            )

            return {
                "success": True,
                "document_id": document_id,
                "job_id": job_id,
                "retry_count": document.retry_count
            }

    except Exception as e:
        logger.error(f"重试文档处理失败: {document_id}, 错误: {e}")
        return {"success": False, "error": str(e)}


async def _simulate_document_processing(db, document_id: str, document: Document):
    """模拟文档处理过程"""
    # 模拟处理步骤
    steps = [
        (20, "开始文档解析"),
        (40, "提取文档结构"),
        (60, "处理文档内容"),
        (80, "生成解析结果"),
        (100, "保存解析结果")
    ]

    for progress, message in steps:
        # 更新进度
        document.progress = progress
        await db.commit()

        # 记录处理日志
        await _log_document_event(db, document_id, "INFO", message)

        # 模拟处理时间
        await asyncio.sleep(1)


async def _log_document_event(
        db,
        document_id: str,
        level: str,
        message: str,
        source: str = "document_task"
):
    """记录文档处理事件"""
    try:
        log_entry = DocumentLog(
            document_id=document_id,
            level=level,
            message=message,
            source=source
        )
        db.add(log_entry)
        await db.commit()

        # 同时记录到系统日志
        if level == "ERROR":
            logger.error(f"[{document_id}] {message}")
        elif level == "WARNING":
            logger.warning(f"[{document_id}] {message}")
        else:
            logger.info(f"[{document_id}] {message}")

    except Exception as e:
        logger.error(f"记录文档日志失败: {e}")


async def process_batch_documents(
        ctx: Dict[str, Any],
        batch_id: str,
        user_id: str,
        priority: int = 1,
        service_type: str = "auto",
        parse_mode: str = "auto",
        max_retries: int = 2
) -> Dict[str, Any]:
    """
    批量处理文档解析任务

    Args:
        ctx: ARQ 上下文
        batch_id: 批次ID
        user_id: 用户ID
        priority: 任务优先级
        service_type: 服务类型
        parse_mode: 解析模式
        max_retries: 最大重试次数

    Returns:
        批量处理结果
    """
    logger.info(f"开始批量处理文档: 批次 {batch_id}, 用户 {user_id}")

    try:
        async with db_manager.session_factory() as db:
            doc_service = DocumentService(db)

            # 获取批次下的所有文档
            documents = await doc_service.get_batch_documents(batch_id)

            if not documents:
                logger.warning(f"批次 {batch_id} 下没有找到文档")
                return {"success": False, "error": "批次下没有文档"}

            logger.info(f"批次 {batch_id} 包含 {len(documents)} 个文档")

            # 为每个文档提交单独的解析任务
            from almond_parser.tasks.arq_app import arq_manager

            submitted_tasks = []
            failed_tasks = []

            # 使用任务分配服务来分配任务，而不是直接提交所有任务
            from almond_parser.services.task_allocation_service import task_allocation_service

            logger.info(f"开始为批次 {batch_id} 分配 {len(documents)} 个文档任务")

            # 立即分配可用的任务 - 使用按节点类型分配
            allocation_stats = await task_allocation_service.allocate_pending_tasks_by_node_type(max_allocations=len(documents))

            logger.info(f"批次任务分配完成: {allocation_stats}")

            # 统计结果
            allocated_count = allocation_stats.get("allocated", 0)
            failed_count = allocation_stats.get("failed", 0)
            pending_count = len(documents) - allocated_count - failed_count

            # 为统计目的，记录所有文档
            for document in documents:
                submitted_tasks.append({
                    "document_id": document.document_id,
                    "job_id": f"batch_{batch_id}",  # 批次任务ID
                    "status": "allocated" if allocated_count > 0 else "pending"
                })
                allocated_count = max(0, allocated_count - 1)  # 递减计数

            logger.info(f"批次 {batch_id} 处理完成: 已分配 {allocation_stats.get('allocated', 0)}, 等待中 {pending_count}, 失败 {failed_count}")

            logger.info(f"批次 {batch_id} 处理完成: 成功 {len(submitted_tasks)}, 失败 {len(failed_tasks)}")

            return {
                "success": True,
                "batch_id": batch_id,
                "total_documents": len(documents),
                "submitted_tasks": len(submitted_tasks),
                "failed_tasks": len(failed_tasks),
                "details": {
                    "submitted": submitted_tasks,
                    "failed": failed_tasks
                }
            }

    except Exception as e:
        error_msg = f"批量处理文档失败: 批次 {batch_id}, 错误: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": str(e)}


async def query_document_status(
        ctx: Dict[str, Any],
        document_id: str,
        user_id: str
) -> Dict[str, Any]:
    """
    查询文档解析状态（主动向 mineru-api 查询）

    Args:
        ctx: ARQ 上下文
        document_id: 文档ID
        user_id: 用户ID

    Returns:
        查询结果
    """
    logger.info(f"查询文档解析状态: {document_id}, 用户: {user_id}")

    try:
        async with db_manager.session_factory() as db:
            doc_service = DocumentService(db)

            # 获取文档信息
            from sqlalchemy import select
            result = await db.execute(
                select(Document).where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                return {"success": False, "error": "文档不存在"}

            # 如果文档已经完成或失败，直接返回状态
            if document.status in [DocumentStatus.COMPLETED, DocumentStatus.FAILED]:
                return {
                    "success": True,
                    "document_id": document_id,
                    "status": document.status.value,
                    "progress": document.progress,
                    "message": "文档已处理完成"
                }

            # 查询 mineru-api 节点状态
            # 这里需要根据实际的 mineru-api 接口来实现
            # 暂时返回当前状态
            await doc_service.add_document_log(
                document_id, "INFO", "执行状态查询"
            )

            return {
                "success": True,
                "document_id": document_id,
                "status": document.status.value,
                "progress": document.progress,
                "message": "状态查询完成"
            }

    except Exception as e:
        error_msg = f"查询文档状态失败: {document_id}, 错误: {str(e)}"
        logger.error(error_msg)
        return {"success": False, "error": str(e)}


async def _select_best_node(db, service_type: str = "document") -> Optional[MinerUNode]:
    """
    选择最佳的 MinerU 节点

    Args:
        db: 数据库会话
        service_type: 服务类型 ("document", "knowledge_base")

    Returns:
        最佳节点或 None
    """
    try:
        from sqlalchemy import select, or_
        from almond_parser.db.models import ServiceType

        # 映射服务类型
        service_type_enum = {
            "document": ServiceType.DOCUMENT,
            "knowledge_base": ServiceType.KNOWLEDGE_BASE
        }.get(service_type)

        if not service_type_enum:
            logger.error(f"不支持的服务类型: {service_type}")
            return None

        # 第一优先级：查找专用节点
        result = await db.execute(
            select(MinerUNode)
            .where(
                MinerUNode.is_enabled == True,
                MinerUNode.status == NodeStatus.ONLINE,
                MinerUNode.service_type == service_type_enum,
                MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks
            )
            .order_by(
                MinerUNode.current_tasks.asc(),  # 当前任务数最少
                MinerUNode.priority.desc()  # 优先级最高
            )
            .limit(1)
        )

        node = result.scalar_one_or_none()
        if node:
            logger.info(f"选择专用节点: {node.name} (类型: {service_type})")
            return node

        # 第二优先级：查找通用节点
        result = await db.execute(
            select(MinerUNode)
            .where(
                MinerUNode.is_enabled == True,
                MinerUNode.status == NodeStatus.ONLINE,
                MinerUNode.service_type == ServiceType.UNIVERSAL,
                MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks
            )
            .order_by(
                MinerUNode.current_tasks.asc(),  # 当前任务数最少
                MinerUNode.priority.desc()  # 优先级最高
            )
            .limit(1)
        )

        node = result.scalar_one_or_none()
        if node:
            logger.info(f"选择通用节点: {node.name} (降级处理 {service_type})")
            return node

        logger.warning(f"没有找到可用的节点处理 {service_type} 类型任务")
        return None

    except Exception as e:
        logger.error(f"选择节点失败: {e}")
        return None


async def _call_mineru_api(
        node: MinerUNode,
        document: Document,
        config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """调用 MinerU API 进行文档解析"""
    try:
        # 构建请求数据
        parse_url = urljoin(node.base_url, "/predict")
        logger.info(f"parse_url：{parse_url}")
        # 准备回调URL
        callback_url = f"{settings.HOST_URL}/api/v1/document/callback"

        # 读取文件并编码
        with open(document.file_path, "rb") as f:
            file_bytes = f.read()
        file_b64 = base64.b64encode(file_bytes).decode("utf-8")

        request_data = {
            "document_id": document.document_id,
            "file_name": document.file_name,
            "file_content": file_b64,
            "callback_url": callback_url,
            "callback_headers": {"Authorization": f"Bearer {node.auth_token}"},
            **config
        }

        # 准备请求头
        headers = {"Content-Type": "application/json"}
        if node.auth_token:
            headers["Authorization"] = f"Bearer {node.auth_token}"
        logger.info(f"headers: {headers}")
        # 发送解析请求
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                    parse_url,
                    json=request_data,
                    headers=headers
            ) as response:

                if response.status == 200:
                    result = await response.json()
                    logger.info(f"MinerU API 调用成功: {document.document_id}")
                    return {
                        "success": True,
                        "task_id": result.get("task_id"),
                        "message": "解析任务已提交到 MinerU"
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"MinerU API 调用失败: HTTP {response.status}, {error_text}")
                    return {
                        "success": False,
                        "error": f"MinerU API 返回错误: HTTP {response.status}"
                    }

    except asyncio.TimeoutError:
        return {"success": False, "error": "MinerU API 请求超时"}
    except Exception as e:
        logger.error(f"调用 MinerU API 异常: {e}")
        return {"success": False, "error": f"调用 MinerU API 失败: {str(e)}"}


async def _handle_node_unavailable_legacy(
        db: AsyncSession,
        document: Document,
        service_type: str,
        parse_mode: str
) -> None:
    """
    处理节点不可用情况 - 兼容原有任务系统

    Args:
        db: 数据库会话
        document: 文档对象
        service_type: 服务类型
        parse_mode: 解析模式
    """
    from datetime import datetime, timedelta
    logger.info(f'_handle_node_unavailable_legacy: {document.document_id}')
    # 更新文档状态为重试等待
    document.status = DocumentStatus.RETRY_PENDING
    document.is_system_retry = True
    document.retry_reason = "node_unavailable"
    document.next_retry_at = datetime.now() + timedelta(minutes=1)  # 1分钟后重试

    # 设置解析模式信息
    if not document.original_parse_mode:
        document.original_parse_mode = parse_mode
    document.current_parse_mode = parse_mode

    await db.commit()
    logger.info(f"1_handle_node_unavailable_legacy: {document.document_id}")
    # 记录日志
    await _log_document_event(
        db, document.document_id, "WARNING",
        f"节点不可用，已安排系统重试: {document.next_retry_at} (服务类型: {service_type}, 解析模式: {parse_mode})",
        "document_task_retry"
    )

    logger.warning(f"文档 {document.document_id} 节点不可用，安排重试: {document.next_retry_at}")
