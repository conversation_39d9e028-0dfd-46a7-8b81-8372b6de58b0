[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "aicenter-parserflow"
version = "1.0.0"
description = "AI中心解析流 - 统一的文档解析和OCR服务平台"
authors = [
    {name = "AI Center Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
keywords = ["ocr", "document-parsing", "ai", "fastapi", "litserve"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

# 基础公共依赖
dependencies = [
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "pydantic>=2.0.0",
    "httpx>=0.25.0",
    "pydantic-settings>=2.10.1",
    "pypdf2>=3.0.1",
]

[project.optional-dependencies]
# MinerU API 服务依赖
mineru-api = [
    "litserve>=0.2.0",
    "uvicorn>=0.24.0",
    "fastapi>=0.104.0",
    "python-multipart>=0.0.6",
    "Pillow>=10.0.0",
    "psutil>=5.9.0",
]

# 杏仁解析服务依赖
almond-parser = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.0",
    "aiomysql>=0.2.0",
    "pymysql>=1.1.0",
    "aioredis>=2.0.0",
    "arq>=0.25.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "python-magic>=0.4.27",
    "aiohttp>=3.9.0",
    "alembic>=1.12.0",
]

# Windows 平台依赖
windows = [
    "comtypes>=1.1.14",
    "mineru[core]>=2.0.6",
]

# Linux 平台依赖
linux = [
    "mineru[all]>=2.0.6",
]

# 安装所有服务
all = [
    "aicenter-parserflow[mineru-api,almond-parser]",
]

# 开发依赖
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]

# 测试依赖
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "httpx>=0.25.0",
]

[project.urls]
Homepage = "https://gitlab.com/aicenter/parserflow"
Documentation = "https://gitlab.com/aicenter/parserflow/-/blob/main/README.md"
Repository = "https://gitlab.com/aicenter/parserflow.git"
Issues = "https://gitlab.com/aicenter/parserflow/-/issues"

[project.scripts]
aicenter-bootstrap = "bootstrap:main"
aicenter-start = "start_services:main"

[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"

[tool.hatch.build.targets.wheel]
packages = ["almond_parser", "mineru_api"]

[tool.hatch.build.targets.sdist]
include = [
    "/almond_parser",
    "/mineru_api",
    "/web",
    "/docs",
    "/README.md",
    "/bootstrap.py",
    "/start_services.py",
]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
