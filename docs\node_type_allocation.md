# 按节点类型分配任务功能

## 🎯 功能概述

新的按节点类型分配功能解决了原有任务分配系统中容量计算不准确的问题，通过按节点类型（ParseMode + ServiceType）分别计算容量和分配任务，避免了任务卡住的情况。

## 🔧 问题背景

### 原有问题
1. **容量计算不准确**：计算总容量时没有区分节点类型，导致分配数量超过特定类型节点的实际容量
2. **任务卡住**：部分任务被标记为"解析中"但实际无法处理，定时任务无法重新分配
3. **资源浪费**：节点类型不匹配导致任务分配效率低下

### 解决方案
- 按节点类型分别计算容量
- 按节点类型分别查询和分配任务
- 智能匹配任务需求与节点能力

## 🔧 问题背景

### 原有问题
1. **容量计算不准确**：将所有节点的容量简单相加，没有区分节点类型
2. **任务分配不匹配**：可能将需要SGLANG的任务分配给只有PIPELINE的节点
3. **任务卡住**：任务被标记为"解析中"但实际无法处理，定时任务无法重新分配

### 具体场景
- 系统有5个SGLANG节点 + 10个PIPELINE节点，总容量15
- 一次性推送15个任务到ARQ队列
- 但某种类型的节点实际只能处理10个任务
- 剩余5个任务被标记为"解析中"但永远无法处理

## 🚀 新功能特性

### 1. 按节点类型分组
```python
@dataclass
class NodeTypeCapacity:
    parse_mode: ParseMode      # SGLANG, PIPELINE, AUTO
    service_type: ServiceType  # DOCUMENT, KNOWLEDGE_BASE, UNIVERSAL
    total_capacity: int        # 该类型节点的总容量
    current_load: int          # 该类型节点的当前负载
    available_capacity: int    # 该类型节点的可用容量
    node_count: int           # 该类型的节点数量
    priority: int             # 优先级（用于排序）
```

### 2. 智能优先级排序
- **服务类型优先级**：DOCUMENT(30) > KNOWLEDGE_BASE(20) > UNIVERSAL(10)
- **解析模式优先级**：SGLANG(2) > PIPELINE(1) > AUTO(0)
- **综合优先级**：service_type_priority + parse_mode_priority

### 3. 精确任务匹配
- 根据任务的解析模式需求匹配合适的节点类型
- 专用节点优先于通用节点
- 支持解析模式兼容性检查

## 📋 使用方法

### 1. 新的分配方法
```python
from almond_parser.services.task_allocation_service import TaskAllocationService

task_service = TaskAllocationService()

# 使用新的按节点类型分配方法
stats = await task_service.allocate_pending_tasks_by_node_type(max_allocations=10)
```

### 2. 返回结果格式
```python
{
    "allocated": 8,           # 成功分配的任务数
    "failed": 1,              # 分配失败的任务数
    "total_pending": 12,      # 总的等待任务数
    "allocation_method": "by_node_type",
    "node_type_details": {    # 各节点类型的详细分配信息
        "sglang_document": {
            "allocated": 3,
            "failed": 0,
            "available_capacity": 5,
            "suitable_tasks": 3
        },
        "pipeline_universal": {
            "allocated": 5,
            "failed": 1,
            "available_capacity": 8,
            "suitable_tasks": 9
        }
    }
}
```

## 🔄 系统集成

### 1. 定时任务更新
定时任务已自动切换到新的分配方法：
```python
# arq_app.py 中的定时任务
async def allocate_pending_tasks_cron(ctx):
    stats = await task_allocation_service.allocate_pending_tasks_by_node_type()
```

### 2. 事件触发更新
任务完成事件也使用新方法：
```python
async def handle_task_completion(self, document_id: str, success: bool, node_id: Optional[int] = None):
    await self.allocate_pending_tasks_by_node_type(max_allocations=3)
```

### 3. 批量处理更新
批量文档处理也使用新方法：
```python
allocation_stats = await task_allocation_service.allocate_pending_tasks_by_node_type(max_allocations=len(documents))
```

## 🧪 测试验证

### 1. 运行测试脚本
```bash
cd almond_parser
python tools/test_node_type_allocation.py
```

### 2. 测试内容
- 节点类型容量查询测试
- 节点类型任务查询测试
- 按节点类型分配测试
- 新旧方法对比测试

### 3. 预期结果
- 所有节点类型都能正确识别和计算容量
- 任务能够正确匹配到合适的节点类型
- 分配过程不会超出各节点类型的实际容量
- 不会出现任务永久卡住的情况

## 📊 监控指标

### 1. 分配统计
- 各节点类型的分配成功率
- 各节点类型的容量利用率
- 任务匹配准确率

### 2. 日志监控
```
🚀 开始按节点类型分配任务，最大分配数量: 10
📋 发现 3 种节点类型:
  - sglang_document: 容量=3/5, 节点数=2, 优先级=32
  - pipeline_universal: 容量=8/10, 节点数=3, 优先级=11
🎯 处理节点类型 sglang_document, 可分配数量: 3
📝 节点类型 sglang_document 找到 3 个适合的任务
📊 节点类型 sglang_document 分配完成: 成功=3, 失败=0
🎉 按节点类型分配完成: {...}
```

## 🔧 配置说明

### 1. 节点类型配置
确保节点正确配置了 `parse_mode` 和 `service_type`：
```sql
UPDATE mineru_nodes SET 
    parse_mode = 'sglang',
    service_type = 'document'
WHERE name = 'sglang-node-1';
```

### 2. 任务解析模式
确保任务正确设置了 `current_parse_mode`：
```python
document.current_parse_mode = "sglang"  # 或 "pipeline", "auto"
```

## 🚨 注意事项

1. **向后兼容**：保留了原有的 `allocate_pending_tasks` 方法，可以随时回退
2. **渐进部署**：可以先在测试环境验证，再逐步切换生产环境
3. **监控告警**：建议监控分配失败率，及时发现配置问题
4. **容量规划**：需要根据实际业务需求合理配置各类型节点的数量

## 🔄 回退方案

如果新方法出现问题，可以快速回退到原方法：
```python
# 在相关文件中将调用改回原方法
stats = await task_allocation_service.allocate_pending_tasks(max_allocations=10)
```

## 📈 性能优化

1. **缓存节点类型信息**：避免重复查询数据库
2. **批量任务查询**：减少数据库查询次数
3. **异步并发处理**：并行处理不同节点类型的分配
4. **智能调度间隔**：根据系统负载动态调整分配频率
