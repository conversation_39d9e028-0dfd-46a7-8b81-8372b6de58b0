# -*- encoding: utf-8 -*-
"""
SGLang 服务重启服务
"""
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from loguru import logger

from almond_parser.db.models import MinerUNode, SglangStatus, ParseMode
from almond_parser.config import settings


class SglangRestartService:
    """SGLang 服务重启服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.timeout = aiohttp.ClientTimeout(total=30, connect=10)
    
    async def restart_node_sglang(self, node: MinerUNode) -> Dict[str, Any]:
        """重启节点的SGLang服务"""
        try:
            # 初始化重启周期（如果是第一次重启）
            now = datetime.now()
            if (node.sglang_current_cycle_restarts or 0) == 0:
                node.sglang_restart_cycle_start = now
                logger.info(f"节点 {node.id} ({node.name}) 开始新的重启周期")

            logger.info(f"开始重启节点 {node.id} ({node.name}) 的SGLang服务 (周期内第{(node.sglang_current_cycle_restarts or 0) + 1}次)")

            # 更新状态为重启中
            node.sglang_status = SglangStatus.RESTARTING
            node.sglang_last_restart = now
            node.sglang_restart_count = (node.sglang_restart_count or 0) + 1
            node.sglang_current_cycle_restarts = (node.sglang_current_cycle_restarts or 0) + 1
            await self.db.commit()
            
            # 构建重启请求URL - 使用MinerU-API的重启接口
            restart_url = f"{node.base_url}/sglang/restart"

            # 发送重启请求到MinerU-API
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                # 使用force_kill参数进行强制重启
                restart_data = {"force_kill": True}
                async with session.post(restart_url, json=restart_data) as response:
                    if response.status == 200:
                        result_data = await response.json()
                        logger.info(f"节点 {node.id} SGLang重启请求成功: {result_data}")

                        # 等待10秒让服务重启完成
                        await asyncio.sleep(10)

                        return {
                            "success": True,
                            "node_id": node.id,
                            "message": f"SGLang服务重启成功 (周期内第{node.sglang_current_cycle_restarts or 0}次)",
                            "restart_time": node.sglang_last_restart.isoformat(),
                            "restart_count": node.sglang_restart_count or 0,
                            "cycle_restarts": node.sglang_current_cycle_restarts or 0,
                            "mineru_response": result_data
                        }
                    else:
                        # 尝试获取错误详情
                        try:
                            error_data = await response.json()
                            error_msg = f"MinerU-API重启失败: {error_data.get('detail', f'HTTP {response.status}')}"
                        except:
                            error_msg = f"MinerU-API重启失败: HTTP {response.status}"

                        logger.error(f"节点 {node.id} {error_msg}")

                        # 重置状态
                        node.sglang_status = SglangStatus.ERROR
                        await self.db.commit()

                        return {
                            "success": False,
                            "node_id": node.id,
                            "error": error_msg
                        }
                        
        except asyncio.TimeoutError:
            error_msg = "MinerU-API重启请求超时"
            logger.error(f"节点 {node.id} {error_msg}")

            # 重置状态
            node.sglang_status = SglangStatus.ERROR
            await self.db.commit()

            return {
                "success": False,
                "node_id": node.id,
                "error": error_msg
            }

        except aiohttp.ClientConnectorError:
            error_msg = "MinerU-API连接失败"
            logger.error(f"节点 {node.id} {error_msg}")

            # 重置状态
            node.sglang_status = SglangStatus.ERROR
            await self.db.commit()

            return {
                "success": False,
                "node_id": node.id,
                "error": error_msg
            }

        except Exception as e:
            error_msg = f"MinerU-API重启异常: {str(e)}"
            logger.error(f"节点 {node.id} {error_msg}")

            # 重置状态
            node.sglang_status = SglangStatus.ERROR
            await self.db.commit()

            return {
                "success": False,
                "node_id": node.id,
                "error": error_msg
            }
    
    async def batch_restart_nodes(self, node_ids: List[int]) -> Dict[str, Any]:
        """批量重启节点的SGLang服务"""
        try:
            # 获取节点信息
            result = await self.db.execute(
                select(MinerUNode).where(MinerUNode.id.in_(node_ids))
            )
            nodes = result.scalars().all()
            
            if not nodes:
                return {"success": False, "error": "没有找到指定的节点"}
            
            logger.info(f"开始批量重启 {len(nodes)} 个节点的SGLang服务")
            
            # 并发重启所有节点
            tasks = []
            for node in nodes:
                task = self.restart_node_sglang(node)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            success_count = 0
            error_count = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"重启节点 {nodes[i].id} 时发生异常: {result}")
                    error_count += 1
                elif result.get("success"):
                    success_count += 1
                else:
                    error_count += 1
            
            logger.info(f"批量重启完成: 成功 {success_count}, 失败 {error_count}")
            
            return {
                "success": True,
                "total_nodes": len(nodes),
                "success_count": success_count,
                "error_count": error_count,
                "results": [r for r in results if not isinstance(r, Exception)]
            }
            
        except Exception as e:
            logger.error(f"批量重启节点失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def check_restart_needed(self, node: MinerUNode) -> bool:
        """检查节点是否需要重启"""
        try:
            # 检查条件：
            # 1. 节点启用
            # 2. SGLang连续失败次数 >= 3
            # 3. SGLang状态为OFFLINE或ERROR
            # 4. 当前重启周期内重启次数 < 2
            # 5. 如果已重启过，需要等待3-5分钟再次检测

            if not node.is_enabled:
                return False

            if (node.sglang_consecutive_failures or 0) < 3:
                return False

            if node.sglang_status not in [SglangStatus.OFFLINE, SglangStatus.ERROR]:
                return False

            # 检查当前重启周期的重启次数
            if (node.sglang_current_cycle_restarts or 0) >= 2:
                # 已经重启2次了，不再重启，应该发送告警
                return False

            # 如果已经重启过，检查是否需要等待
            if node.sglang_last_restart:
                time_since_restart = datetime.now() - node.sglang_last_restart
                # 第一次重启后等待3-5分钟再检测，这里设置为4分钟
                if time_since_restart < timedelta(minutes=4):
                    return False
            
            # 检查重启次数限制（1小时内最多重启3次）
            if (node.sglang_restart_count or 0) >= 3:
                # 检查是否超过1小时
                if node.sglang_last_restart:
                    time_since_restart = datetime.now() - node.sglang_last_restart
                    if time_since_restart < timedelta(hours=1):
                        logger.warning(f"节点 {node.id} 1小时内重启次数过多，跳过重启")
                        return False
                else:
                    # 重置重启计数
                    node.sglang_restart_count = 0
                    await self.db.commit()
            
            return True

        except Exception as e:
            logger.error(f"检查节点 {node.id} 重启需求失败: {e}")
            return False

    async def check_alert_needed(self, node: MinerUNode) -> bool:
        """检查节点是否需要发送告警"""
        try:
            # 检查条件：
            # 1. 节点启用
            # 2. SGLang连续失败次数 >= 3
            # 3. SGLang状态为OFFLINE或ERROR
            # 4. 当前重启周期内已重启2次
            # 5. 最后一次重启后等待了3-5分钟
            # 6. 还没有发送过告警

            if not node.is_enabled:
                return False

            if (node.sglang_consecutive_failures or 0) < 3:
                return False

            if node.sglang_status not in [SglangStatus.OFFLINE, SglangStatus.ERROR]:
                return False

            if (node.sglang_current_cycle_restarts or 0) < 2:
                return False

            if node.sglang_alert_sent:
                return False

            # 检查最后一次重启后是否等待了足够时间
            if node.sglang_last_restart:
                time_since_restart = datetime.now() - node.sglang_last_restart
                # 最后一次重启后等待4分钟再发送告警
                if time_since_restart < timedelta(minutes=4):
                    return False

            return True

        except Exception as e:
            logger.error(f"检查节点 {node.id} 告警需求失败: {e}")
            return False

    async def mark_alert_sent(self, node: MinerUNode):
        """标记告警已发送"""
        try:
            node.sglang_alert_sent = True
            node.sglang_alert_sent_at = datetime.now()
            await self.db.commit()
            logger.info(f"节点 {node.id} 告警已标记为已发送")
        except Exception as e:
            logger.error(f"标记节点 {node.id} 告警状态失败: {e}")

    async def reset_restart_cycle(self, node: MinerUNode):
        """重置重启周期（当SGLang恢复正常时调用）"""
        try:
            if (node.sglang_current_cycle_restarts or 0) > 0 or node.sglang_alert_sent:
                logger.info(f"重置节点 {node.id} 的重启周期")
                node.sglang_current_cycle_restarts = 0
                node.sglang_restart_cycle_start = None
                node.sglang_alert_sent = False
                node.sglang_alert_sent_at = None
                await self.db.commit()
        except Exception as e:
            logger.error(f"重置节点 {node.id} 重启周期失败: {e}")
    
    async def auto_restart_failed_nodes(self) -> Dict[str, Any]:
        """自动重启失败的节点"""
        try:
            # 获取所有启用的节点，但排除Pipeline模式的节点（Pipeline模式不需要SGLang服务）
            result = await self.db.execute(
                select(MinerUNode).where(
                    and_(
                        MinerUNode.is_enabled == True,
                        MinerUNode.parse_mode != ParseMode.PIPELINE  # 排除Pipeline模式节点
                    )
                )
            )
            nodes = result.scalars().all()
            
            # 筛选需要重启的节点
            nodes_to_restart = []
            for node in nodes:
                if await self.check_restart_needed(node):
                    nodes_to_restart.append(node)
            
            if not nodes_to_restart:
                return {
                    "success": True,
                    "message": "没有需要重启的节点",
                    "total_checked": len(nodes),
                    "restart_count": 0
                }
            
            logger.info(f"发现 {len(nodes_to_restart)} 个节点需要重启SGLang服务")
            
            # 执行重启
            restart_results = []
            for node in nodes_to_restart:
                result = await self.restart_node_sglang(node)
                restart_results.append(result)
                
                # 重启间隔，避免同时重启过多服务
                await asyncio.sleep(2)
            
            # 统计结果
            success_count = sum(1 for r in restart_results if r.get("success"))
            error_count = len(restart_results) - success_count
            
            return {
                "success": True,
                "total_checked": len(nodes),
                "restart_count": len(nodes_to_restart),
                "success_count": success_count,
                "error_count": error_count,
                "results": restart_results
            }
            
        except Exception as e:
            logger.error(f"自动重启失败节点异常: {e}")
            return {"success": False, "error": str(e)}
