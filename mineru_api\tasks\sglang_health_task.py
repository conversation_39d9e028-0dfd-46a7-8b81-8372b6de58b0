"""
sglang 健康检查定时任务
定期检查 sglang 服务健康状态，自动处理异常情况
"""
import asyncio
from datetime import datetime
from loguru import logger

from ..services.sglang_health_monitor import sglang_health_monitor
from ..config import SGLANG_URL


async def sglang_health_check_task():
    """sglang 健康检查定时任务"""
    try:
        import platform

        # 检查平台，只在 Linux 上执行 sglang 相关操作
        if platform.system().lower() != "linux":
            logger.debug("Windows 平台，跳过 sglang 健康检查")
            return {
                "timestamp": datetime.now().isoformat(),
                "platform": platform.system(),
                "skipped": True,
                "reason": "非 Linux 平台"
            }

        logger.debug("🔍 执行 sglang 健康检查...")

        # 执行健康检查
        health_result = await sglang_health_monitor.health_check()

        # 记录检查结果
        if health_result["healthy"]:
            logger.debug(f"✅ sglang 健康检查通过 (响应时间: {health_result.get('response_time', 'N/A')}s)")
        else:
            logger.warning(f"❌ sglang 健康检查失败: {health_result.get('error', '未知错误')}")
            logger.warning(f"连续失败次数: {sglang_health_monitor.consecutive_failures}")

        # 检查是否需要自动重启
        restart_triggered = await sglang_health_monitor.auto_restart_if_needed()

        if restart_triggered:
            logger.warning("🔄 已触发 sglang 自动重启")

        # 返回检查结果
        return {
            "timestamp": datetime.now().isoformat(),
            "healthy": health_result["healthy"],
            "consecutive_failures": sglang_health_monitor.consecutive_failures,
            "restart_triggered": restart_triggered,
            "process_count": health_result.get("process_info", {}).get("total_processes", 0),
            "response_time": health_result.get("response_time"),
            "error": health_result.get("error")
        }

    except Exception as e:
        logger.error(f"sglang 健康检查任务失败: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "healthy": False,
            "error": str(e),
            "task_failed": True
        }


async def sglang_process_cleanup_task():
    """sglang 进程清理任务（每小时执行一次）"""
    try:
        import platform

        # 检查平台，只在 Linux 上执行 sglang 相关操作
        if platform.system().lower() != "linux":
            logger.debug("Windows 平台，跳过 sglang 进程清理检查")
            return {
                "timestamp": datetime.now().isoformat(),
                "platform": platform.system(),
                "skipped": True,
                "reason": "非 Linux 平台"
            }

        logger.info("🧹 执行 sglang 进程清理检查...")

        # 获取详细状态
        status = await sglang_health_monitor.get_detailed_status()
        process_info = status.get("health_check", {}).get("process_info", {})

        total_processes = process_info.get("total_processes", 0)
        memory_usage = process_info.get("memory_usage", 0)

        # 检查是否需要清理
        cleanup_needed = False
        cleanup_reason = []

        if total_processes > 20:
            cleanup_needed = True
            cleanup_reason.append(f"进程数过多 ({total_processes})")

        if memory_usage > 32000:  # 8GB
            cleanup_needed = True
            cleanup_reason.append(f"内存使用过高 ({memory_usage:.1f}MB)")

        if not status.get("health_check", {}).get("healthy", False):
            # 如果服务不健康且进程数大于5，也考虑清理
            if total_processes > 5:
                cleanup_needed = True
                cleanup_reason.append("服务不健康且进程数较多")

        if cleanup_needed:
            logger.warning(f"🔥 触发进程清理: {', '.join(cleanup_reason)}")

            # 执行强制重启
            from ..services.sglang_manager import sglang_manager
            restart_success = await sglang_manager.force_restart_server()

            return {
                "timestamp": datetime.now().isoformat(),
                "cleanup_triggered": True,
                "cleanup_reason": cleanup_reason,
                "restart_success": restart_success,
                "before_cleanup": {
                    "total_processes": total_processes,
                    "memory_usage": memory_usage
                }
            }
        else:
            logger.debug(f"✅ 进程状态正常 (进程数: {total_processes}, 内存: {memory_usage:.1f}MB)")
            return {
                "timestamp": datetime.now().isoformat(),
                "cleanup_triggered": False,
                "process_count": total_processes,
                "memory_usage": memory_usage
            }

    except Exception as e:
        logger.error(f"sglang 进程清理任务失败: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "cleanup_triggered": False,
            "error": str(e),
            "task_failed": True
        }


async def sglang_startup_check():
    """启动时的 sglang 检查"""
    try:
        import platform

        # 检查平台，只在 Linux 上执行 sglang 相关操作
        if platform.system().lower() != "linux":
            logger.info("Windows 平台，跳过启动时 sglang 检查")
            return {
                "timestamp": datetime.now().isoformat(),
                "platform": platform.system(),
                "skipped": True,
                "reason": "非 Linux 平台"
            }

        logger.info("🚀 执行启动时 sglang 检查...")

        # 检查服务是否运行
        from ..services.sglang_manager import sglang_manager
        is_running = await sglang_manager.is_running()

        if is_running:
            logger.info("✅ sglang 服务已在运行")

            # 执行一次健康检查
            health_result = await sglang_health_monitor.health_check()

            if health_result["healthy"]:
                logger.success("✅ sglang 服务健康状态良好")
            else:
                logger.warning(f"⚠️ sglang 服务运行但健康检查失败: {health_result.get('error')}")
        else:
            logger.warning("❌ sglang 服务未运行")

            # 尝试启动服务
            logger.info("🔄 尝试启动 sglang 服务...")
            start_success = await sglang_manager.start_server()

            if start_success:
                logger.success("✅ sglang 服务启动成功")
            else:
                logger.error("❌ sglang 服务启动失败")

        return {
            "timestamp": datetime.now().isoformat(),
            "platform": platform.system(),
            "initially_running": is_running,
            "final_status": await sglang_manager.is_running()
        }

    except Exception as e:
        logger.error(f"启动时 sglang 检查失败: {e}")
        return {
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "task_failed": True
        }
