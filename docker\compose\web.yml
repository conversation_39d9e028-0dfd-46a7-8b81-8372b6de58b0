version: '3.8'

services:
  # Web 前端服务
  web-frontend:
    image: web-frontend:latest
    container_name: parserflow-web
    restart: unless-stopped
    environment:
      # API配置
      VITE_API_BASE_URL: ${API_BASE_URL:-http://localhost/api}
      VITE_MINERU_API_URL: ${MINERU_API_URL:-http://localhost/mineru}
      
      # 应用配置
      VITE_APP_TITLE: ${APP_TITLE:-杏仁解析管理平台}
      VITE_APP_VERSION: ${APP_VERSION:-1.0.0}
      
      # 功能开关
      VITE_ENABLE_UPLOAD: ${ENABLE_UPLOAD:-true}
      VITE_ENABLE_BATCH: ${ENABLE_BATCH:-true}
      VITE_ENABLE_MONITORING: ${ENABLE_MONITORING:-true}
      
      # 文件上传配置
      VITE_MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100}
      VITE_ALLOWED_FILE_TYPES: ${ALLOWED_FILE_TYPES:-pdf,doc,docx,ppt,pptx,jpg,png}
      
      # 界面配置
      VITE_THEME: ${THEME:-light}
      VITE_LANGUAGE: ${LANGUAGE:-zh-CN}
      
    ports:
      - "${WEB_PORT:-3000}:80"
    volumes:
      - web_static:/usr/share/nginx/html
    networks:
      - parserflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  web_static:
    external: true

networks:
  parserflow-network:
    external: true
