#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
诊断查询条件问题
"""
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from sqlalchemy import select, func, and_, or_


async def diagnose_query_conditions():
    """逐步诊断查询条件"""
    print("=" * 60)
    print("诊断查询条件问题")
    print("=" * 60)
    
    async with get_async_session() as db:
        current_time = datetime.now()
        print(f"当前UTC时间: {current_time}")
        
        # 1. 最基础查询：所有RETRY_PENDING任务
        print("\n1. 查询所有RETRY_PENDING任务:")
        result1 = await db.execute(
            select(func.count(Document.id)).where(
                Document.status == DocumentStatus.RETRY_PENDING
            )
        )
        count1 = result1.scalar() or 0
        print(f"   总RETRY_PENDING任务数: {count1}")
        
        # 2. 加上task_id条件
        print("\n2. 查询task_id为NULL的RETRY_PENDING任务:")
        result2 = await db.execute(
            select(func.count(Document.id)).where(
                Document.status == DocumentStatus.RETRY_PENDING,
                Document.task_id.is_(None)
            )
        )
        count2 = result2.scalar() or 0
        print(f"   task_id为NULL的RETRY_PENDING任务数: {count2}")
        
        # 3. 查看具体的RETRY_PENDING任务详情
        print("\n3. 查看RETRY_PENDING任务详情:")
        detail_result = await db.execute(
            select(Document).where(
                Document.status == DocumentStatus.RETRY_PENDING
            ).limit(5)
        )
        tasks = detail_result.scalars().all()
        
        for task in tasks:
            print(f"   文档ID: {task.document_id}")
            print(f"     文件名: {task.file_name}")
            print(f"     task_id: {task.task_id}")
            print(f"     retry_count: {task.retry_count}")
            print(f"     max_retries: {task.max_retries}")
            print(f"     next_retry_at: {task.next_retry_at}")
            print(f"     created_at: {task.created_at}")
            print(f"     updated_at: {task.updated_at}")
            
            # 检查各个条件
            print(f"     条件检查:")
            print(f"       task_id is None: {task.task_id is None}")
            print(f"       next_retry_at is None: {task.next_retry_at is None}")
            if task.next_retry_at:
                print(f"       next_retry_at <= current_time: {task.next_retry_at <= current_time}")
            print(f"       retry_count: {task.retry_count}, max_retries: {task.max_retries}")
            if task.retry_count is not None and task.max_retries is not None:
                print(f"       retry_count < max_retries: {task.retry_count < task.max_retries}")
            print()
        
        # 4. 测试时间条件
        print("4. 测试时间条件:")
        result4 = await db.execute(
            select(func.count(Document.id)).where(
                Document.status == DocumentStatus.RETRY_PENDING,
                Document.task_id.is_(None),
                or_(
                    Document.next_retry_at.is_(None),
                    Document.next_retry_at <= current_time
                )
            )
        )
        count4 = result4.scalar() or 0
        print(f"   通过时间条件的任务数: {count4}")
        
        # 5. 测试重试次数条件
        print("\n5. 测试重试次数条件:")
        result5 = await db.execute(
            select(func.count(Document.id)).where(
                Document.status == DocumentStatus.RETRY_PENDING,
                Document.task_id.is_(None),
                or_(
                    Document.next_retry_at.is_(None),
                    Document.next_retry_at <= current_time
                ),
                Document.retry_count < Document.max_retries
            )
        )
        count5 = result5.scalar() or 0
        print(f"   通过重试次数条件的任务数: {count5}")
        
        # 6. 测试NULL值处理
        print("\n6. 测试NULL值处理:")
        result6 = await db.execute(
            select(func.count(Document.id)).where(
                Document.status == DocumentStatus.RETRY_PENDING,
                Document.task_id.is_(None),
                or_(
                    Document.next_retry_at.is_(None),
                    Document.next_retry_at <= current_time
                ),
                or_(
                    Document.retry_count.is_(None),
                    Document.max_retries.is_(None),
                    Document.retry_count < Document.max_retries
                )
            )
        )
        count6 = result6.scalar() or 0
        print(f"   处理NULL值后的任务数: {count6}")
        
        # 7. 完整的查询条件（当前使用的）
        print("\n7. 完整查询条件:")
        result7 = await db.execute(
            select(func.count(Document.id)).where(
                and_(
                    or_(
                        Document.status == DocumentStatus.UPLOADED,
                        and_(
                            Document.status == DocumentStatus.RETRY_PENDING,
                            or_(
                                Document.next_retry_at.is_(None),
                                Document.next_retry_at <= current_time
                            ),
                            or_(
                                Document.retry_count.is_(None),
                                Document.max_retries.is_(None),
                                Document.retry_count < Document.max_retries
                            )
                        )
                    ),
                    Document.task_id.is_(None),
                )
            )
        )
        count7 = result7.scalar() or 0
        print(f"   完整查询条件的任务数: {count7}")


if __name__ == "__main__":
    asyncio.run(diagnose_query_conditions())
