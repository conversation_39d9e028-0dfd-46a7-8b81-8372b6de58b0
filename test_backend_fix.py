#!/usr/bin/env python3
"""
测试后端修复的脚本
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mineru_api'))

async def test_backend_manager():
    """测试后端管理器"""
    print("🔧 测试后端管理器...")
    
    from mineru_api.services.backend_manager import backend_manager
    from mineru_api.config import BACKEND_MAPPING
    
    print(f"📋 后端映射配置: {BACKEND_MAPPING}")
    
    # 测试初始化
    try:
        await backend_manager.initialize(
            check_sglang=True,
            auto_start_sglang=True,
            backend_mode="auto"
        )
        print(f"✅ 后端管理器初始化成功")
        print(f"📋 支持的后端: {backend_manager.get_supported_backends()}")
        print(f"🔧 后端信息: {backend_manager.get_backend_info()}")
        
    except Exception as e:
        print(f"❌ 后端管理器初始化失败: {e}")
        return False
    
    # 测试后端选择
    test_backends = ["pipeline", "vlm", "sglang", "vlm-sglang-client", "vlm-transformers"]
    
    for backend in test_backends:
        try:
            actual_backend, server_url = backend_manager.get_optimal_backend(backend)
            print(f"🔍 {backend} -> {actual_backend} (server: {server_url})")
        except Exception as e:
            print(f"❌ 测试后端 {backend} 失败: {e}")
    
    return True

async def test_pdf_processing():
    """测试PDF处理"""
    print("\n📄 测试PDF处理...")
    
    # 这里可以添加PDF处理测试
    print("✅ PDF处理测试跳过（需要实际文件）")
    return True

def main():
    """主函数"""
    print("🚀 开始测试后端修复...")
    
    async def run_tests():
        success = True
        
        # 测试后端管理器
        if not await test_backend_manager():
            success = False
        
        # 测试PDF处理
        if not await test_pdf_processing():
            success = False
        
        if success:
            print("\n✅ 所有测试通过！")
        else:
            print("\n❌ 部分测试失败！")
        
        return success
    
    return asyncio.run(run_tests())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
