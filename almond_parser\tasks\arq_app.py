# -*- encoding: utf-8 -*-
"""
ARQ 异步任务队列配置
"""
import asyncio
from datetime import datetime
from typing import Dict, Any
from arq import create_pool, ArqRedis, cron
from arq.connections import RedisSettings
from loguru import logger

from almond_parser.config import settings
from almond_parser.schemas import DocumentStatus
from almond_parser.db.models.mineru_node import ServiceType
from almond_parser.tasks import document_tasks, node_tasks, enhanced_document_tasks, retry_tasks, reset_stuck_nodes, sglang_monitor_task
from almond_parser.services.task_allocation_service import task_allocation_service
from almond_parser.tasks.reset_stuck_nodes import reset_current_tasks_cron


class ArqManager:
    """ARQ 管理器 - 单例模式"""

    _instance = None
    _redis_pool: ArqRedis = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def initialize(self):
        """初始化 ARQ Redis 连接池"""
        if self._initialized:
            return

        if self._redis_pool is None:
            try:
                redis_settings = RedisSettings(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    password=settings.REDIS_PASSWORD,
                    database=1,  # 使用数据库1作为任务队列
                )

                self._redis_pool = await create_pool(redis_settings)
                self._initialized = True
                logger.info("✅ ARQ Redis 连接池初始化成功")

            except Exception as e:
                logger.error(f"❌ ARQ Redis 连接池初始化失败: {e}")
                raise RuntimeError(f"ARQ Redis 连接池初始化失败: {e}")

    @property
    def redis_pool(self) -> ArqRedis:
        """获取 Redis 连接池"""
        if not self._initialized or self._redis_pool is None:
            raise RuntimeError("ARQ Redis 连接池未初始化，请先调用 initialize()")
        return self._redis_pool

    async def enqueue_task(
            self,
            task_name: str,
            *args,
            **kwargs
    ) -> str:
        """入队任务"""
        if not self._initialized:
            await self.initialize()

        try:
            job = await self.redis_pool.enqueue_job(task_name, *args, **kwargs)
            logger.info(f"✅ 任务入队成功: {task_name}, job_id: {job.job_id}")
            return job.job_id
        except Exception as e:
            error_msg = f"❌ 任务入队失败: {task_name}, 错误: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

    async def get_job_result(self, job_id: str) -> Dict[str, Any]:
        """获取任务结果"""
        try:
            # 使用 redis_pool 属性确保连接池已初始化
            job = await self.redis_pool.get_job(job_id)
            if job is None:
                return {"status": "not_found", "result": None}

            if job.finished:
                return {
                    "status": "completed",
                    "result": job.result,
                    "success": job.success
                }
            elif job.in_progress:
                return {"status": "running", "result": None}
            else:
                return {"status": "pending", "result": None}

        except Exception as e:
            logger.error(f"获取任务结果失败: {job_id}, 错误: {e}")
            return {"status": "error", "result": str(e)}

    async def close(self):
        """关闭连接池"""
        if self._redis_pool:
            await self._redis_pool.close()
            logger.info("ARQ Redis 连接池已关闭")


# 全局 ARQ 管理器实例
arq_manager = ArqManager()


async def get_arq_redis() -> ArqRedis:
    """获取 ARQ Redis 连接"""
    return arq_manager.redis_pool


async def startup_arq():
    """启动 ARQ"""
    await arq_manager.initialize()


async def shutdown_arq():
    """关闭 ARQ"""
    await arq_manager.close()


async def cleanup_stuck_node_tasks(ctx):
    """清理卡住的节点任务"""
    try:
        from almond_parser.db.database import get_async_session
        from almond_parser.utils.simplified_node_manager import simplified_node_manager

        async with get_async_session() as db:
            stats = await simplified_node_manager.cleanup_stuck_tasks(db, timeout_minutes=30)
            logger.info(f"清理卡住任务完成: {stats}")

    except Exception as e:
        logger.error(f"清理卡住任务失败: {e}")


async def allocate_pending_tasks_cron(ctx):
    """智能定时分配pending任务的ARQ任务包装函数 - 使用按节点类型分配"""
    try:
        logger.info("🔍 开始按节点类型检测pending任务...")

        # 使用新的按节点类型分配方法
        stats = await task_allocation_service.allocate_pending_tasks_by_node_type()
        logger.info(f'按节点类型分配结果: {stats}')

    except Exception as e:
        logger.error(f"❌ 智能定时任务分配失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")


async def smart_cleanup_zombie_tasks_cron(ctx):
    """智能僵尸任务清理 - 基于文档大小的动态超时判断"""
    try:
        from almond_parser.db.database import get_async_session
        from almond_parser.db.models.document import Document, DocumentStatus
        from almond_parser.db.models.mineru_node import ServiceType
        from almond_parser.utils.timeout_calculator import TimeoutCalculator
        from sqlalchemy import select, and_
        from datetime import datetime

        logger.info("🧹 开始智能清理僵尸任务（基于文档大小）...")

        async with get_async_session() as db:
            current_time = datetime.now()

            # 查找所有正在解析的任务
            parsing_tasks = await db.execute(
                select(Document).where(
                    and_(
                        Document.status == DocumentStatus.PARSING,
                        Document.started_at.isnot(None),  # 必须有开始时间
                        Document.file_size.isnot(None),  # 必须有文件大小
                        Document.node_id.isnot(None)  # 必须占用了节点
                    )
                ).order_by(Document.started_at.asc())
            )

            tasks = parsing_tasks.scalars().all()

            if not tasks:
                logger.debug("✅ 没有发现正在解析的任务")
                return

            logger.info(f"🔍 检查 {len(tasks)} 个正在解析的任务...")

            cleaned_count = 0

            for task in tasks:
                try:
                    # 🎯 智能超时判断：基于文档大小和类型

                    # 1. 推断服务类型（基于文件类型）
                    service_type = _infer_service_type_from_file(task.file_type)

                    # 2. 计算该文档的智能超时阈值
                    timeout_minutes = TimeoutCalculator.calculate_timeout_minutes(
                        service_type,
                        task.file_size,
                        task.current_parse_mode or "auto"
                    )

                    # 3. 计算实际运行时间（基于started_at，更准确）
                    running_time = current_time - task.started_at
                    running_minutes = running_time.total_seconds() / 60

                    # 4. 判断是否超时
                    if running_minutes > timeout_minutes:
                        file_size_mb = task.file_size / (1024 * 1024)
                        logger.warning(
                            f"🧟 发现超时任务: {task.document_id}, "
                            f"文件: {task.file_name} ({file_size_mb:.1f}MB), "
                            f"类型: {service_type.value}, "
                            f"运行时长: {running_minutes:.1f}分钟, "
                            f"超时阈值: {timeout_minutes}分钟"
                        )

                        # 清理超时任务
                        await _smart_cleanup_single_task(db, task, timeout_minutes, running_minutes)
                        cleaned_count += 1
                    else:
                        # 任务仍在合理时间内
                        file_size_mb = task.file_size / (1024 * 1024)
                        logger.debug(
                            f"⏳ 任务正常运行: {task.document_id} ({file_size_mb:.1f}MB), "
                            f"运行: {running_minutes:.1f}分钟, 阈值: {timeout_minutes}分钟"
                        )

                except Exception as e:
                    logger.error(f"检查任务超时失败: {task.document_id}, 错误: {e}")

            if cleaned_count > 0:
                await db.commit()
                logger.info(f"🧹 智能僵尸任务清理完成，清理了 {cleaned_count} 个超时任务")
            else:
                logger.info("✅ 所有任务都在合理时间内运行")

    except Exception as e:
        logger.error(f"❌ 智能僵尸任务清理失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")


def _infer_service_type_from_file(file_type: str) -> ServiceType:
    """根据文件类型推断服务类型"""
    if not file_type:
        return ServiceType.UNIVERSAL

    file_type = file_type.lower()

    # 文档类型
    if file_type in ["pdf", "doc", "docx", "ppt", "pptx"]:
        return ServiceType.DOCUMENT
    # 知识库类型（通常是大批量或复杂文档）
    elif file_type in ["zip", "rar", "tar", "gz"]:
        return ServiceType.KNOWLEDGE_BASE
    # 其他类型
    else:
        return ServiceType.UNIVERSAL


async def _smart_cleanup_single_task(db, task, timeout_minutes: int, running_minutes: float):
    """智能清理单个超时任务"""
    try:
        from almond_parser.utils.node_concurrency_manager import node_concurrency_manager
        from almond_parser.services.document_service import DocumentService

        file_size_mb = task.file_size / (1024 * 1024)
        logger.warning(
            f"🔧 清理超时任务: {task.document_id} ({task.file_name}, {file_size_mb:.1f}MB), "
            f"运行{running_minutes:.1f}分钟 > 阈值{timeout_minutes}分钟"
        )

        # 1. 释放节点资源
        if task.node_id:
            released = await node_concurrency_manager.release_task_slot(
                db=db,
                node_id=task.node_id,
                task_id=task.task_id or "unknown",
                success=False,
                reason=f"任务超时（{running_minutes:.1f}分钟 > {timeout_minutes}分钟）"
            )
            if released:
                logger.info(f"   ✅ 已释放节点槽位: {task.node_id}")

        # 2. 更新任务状态 - 超时任务直接标记为失败，不再重试
        task.task_id = None
        task.node_id = None
        task.updated_at = datetime.now()

        # 3. 超时任务直接标记为失败
        task.status = DocumentStatus.FAILED
        task.error_message = (
            f"任务超时失败：运行{running_minutes:.1f}分钟超过阈值{timeout_minutes}分钟"
        )
        task.completed_at = datetime.now()
        logger.warning(f"   ❌ 任务超时，直接标记为失败: {task.document_id}")

        # 4. 记录清理日志
        doc_service = DocumentService(db)
        await doc_service.add_document_log(
            task.document_id, "WARNING",
            f"智能超时清理：{file_size_mb:.1f}MB文件运行{running_minutes:.1f}分钟超过阈值{timeout_minutes}分钟"
        )

    except Exception as e:
        logger.error(f"智能清理超时任务失败: {task.document_id}, 错误: {e}")


async def cleanup_node_resources_cron(ctx):
    """定时清理节点资源的ARQ任务包装函数"""
    try:
        from almond_parser.utils.node_concurrency_manager import node_concurrency_manager
        from almond_parser.db.database import get_async_session

        async with get_async_session() as db:
            stats = await node_concurrency_manager.cleanup_expired_reservations(db)
            if stats.get("cleaned_nodes", 0) > 0:
                logger.info(f"节点资源清理完成: {stats}")
    except Exception as e:
        logger.error(f"节点资源清理失败: {e}")


# 工作器启动和关闭钩子
async def worker_startup(ctx):
    """工作器启动时的初始化"""
    from almond_parser.db.database import init_database
    logger.info("🔧 初始化工作器数据库...")
    await init_database()
    logger.info("✅ 工作器数据库初始化完成")


async def worker_shutdown(ctx):
    """工作器关闭时的清理"""
    from almond_parser.db.database import close_database
    logger.info("🔧 关闭工作器数据库连接...")
    await close_database()
    logger.info("✅ 工作器数据库连接已关闭")


def interval_set(step: int, start: int = 0, end: int = 60) -> set[int]:
    """
    生成一个间隔时间的 set 集合，用于 ARQ cron 参数。

    :param step: 步长，例如每5秒写 5
    :param start: 起始值，默认为 0
    :param end: 结束值（不包含），默认为 60
    :return: set[int]
    """
    return set(range(start, end, step))


# ARQ 工作器配置
class WorkerSettings:
    """ARQ 工作器配置"""

    redis_settings = RedisSettings(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        database=1,
    )

    # 任务函数：直接传函数对象，而不是字符串路径
    functions = [
        document_tasks.process_document,
        document_tasks.process_batch_documents,
        document_tasks.retry_document,
        document_tasks.query_document_status,
        node_tasks.health_check_node,
        node_tasks.health_check_all_nodes,
        # 增强的文档处理任务
        enhanced_document_tasks.enhanced_process_document,
        enhanced_document_tasks.enhanced_process_document_result,
        # 重试任务
        retry_tasks.process_retry_documents,
        retry_tasks.cleanup_old_retry_records,
        # 新的任务分配和清理任务
        allocate_pending_tasks_cron,
        smart_cleanup_zombie_tasks_cron,  # 智能僵尸任务清理
        cleanup_node_resources_cron,
        cleanup_stuck_node_tasks,
        reset_current_tasks_cron,
        # SGLang监控任务
        sglang_monitor_task.sglang_monitor_cron,
        sglang_monitor_task.sglang_restart_cron,
        sglang_monitor_task.sglang_alert_cron,
        sglang_monitor_task.sglang_comprehensive_cron
    ]

    # 工作器生命周期钩子
    on_startup = worker_startup
    on_shutdown = worker_shutdown

    max_jobs = 10
    job_timeout = 7200  # 任务超时时间：2小时
    keep_result = 86400  # 任务结果保留时间：1天

    # 定时任务配置
    # ARQ cron 支持的时间单位：minute, hour, day, weekday, month
    # minute=None 表示每分钟执行，minute=1 表示每小时的第1分钟执行
    cron_jobs = [
        cron(
            coroutine=node_tasks.health_check_all_nodes,
            second=interval_set(10),
            run_at_startup=True,
        ),
        # 每5秒分配pending任务（智能动态调度）
        cron(
            coroutine=allocate_pending_tasks_cron,
            second=interval_set(5),
            # minute=interval_set(5),
            run_at_startup=True,
        ),

        # 每5分钟智能清理僵尸任务（基于文档大小的动态超时）
        cron(
            coroutine=smart_cleanup_zombie_tasks_cron,
            minute=interval_set(5),
            run_at_startup=True,
        ),

        cron(
            coroutine=reset_current_tasks_cron,  # 你的函数
            minute=interval_set(1),  # 每1分钟执行
            run_at_startup=True,  # 启动时立即执行
        ),

        # SGLang监控定时任务
        # 每2分钟监控SGLang服务状态
        cron(
            coroutine=sglang_monitor_task.sglang_monitor_cron,
            minute=interval_set(2),
            run_at_startup=True,
        ),

        # 每5分钟检查并重启失败的SGLang服务
        cron(
            coroutine=sglang_monitor_task.sglang_restart_cron,
            minute=interval_set(5),
            run_at_startup=False,  # 启动时不立即执行，避免误重启
        ),

        # 每10分钟检查SGLang告警
        cron(
            coroutine=sglang_monitor_task.sglang_alert_cron,
            minute=interval_set(10),
            run_at_startup=False,
        ),
        # # 每5分钟清理节点资源（过期预留等）
        # cron(
        #     coroutine=cleanup_node_resources_cron,
        #     minute={0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55},
        #     run_at_startup=False,
        # ),
        # # 每小时清理过期重试记录
        # cron(
        #     coroutine=retry_tasks.cleanup_old_retry_records,
        #     minute=0,
        #     run_at_startup=False,
        # ),
        # # 每10分钟同步卡住的任务状态
        # cron(
        #     coroutine=task_sync_service.sync_stuck_tasks,
        #     minute={0, 10, 20, 30, 40, 50},
        #     run_at_startup=False,
        # ),
        #
        # # 每30分钟清理卡住的节点任务
        # cron(
        #     coroutine=cleanup_stuck_node_tasks,
        #     minute={0, 30},
        #     run_at_startup=False,
        # )
    ]

    log_results = True
    max_tries = 3
    retry_delay = 60
