# 任务幂等性保护机制

## 🎯 问题背景

在文档解析系统中，存在两个路径可能同时提交相同文档的处理任务：

1. **上传路径**：用户上传文件后，`manage.py` 立即提交 `enhanced_process_document` 任务
2. **定时任务路径**：`allocate_pending_tasks_cron` 每5秒检查并分配 PENDING/UPLOADED 状态的任务

这可能导致**竞态条件**，同一文档被重复处理。

## ⚠️ 竞态条件场景

### 场景1：上传时的时序冲突
```
时间线：
T1: 用户上传文件 → 状态设为 UPLOADED
T2: manage.py 提交 enhanced_process_document 任务
T3: 定时任务执行，发现 UPLOADED 状态文档
T4: 定时任务也提交 enhanced_process_document 任务
结果: 同一文档被提交两次！
```

### 场景2：状态更新延迟
```
时间线：
T1: enhanced_process_document 开始执行
T2: 定时任务执行，文档状态还未更新为 PARSING
T3: 定时任务提交重复任务
T4: 第一个任务更新状态为 PARSING
结果: 两个任务同时处理同一文档！
```

## 🛠️ 解决方案

### 1. enhanced_process_document 幂等性保护

在任务开始时添加状态检查和原子性更新：

```python
async def enhanced_process_document(...):
    # 🔒 幂等性保护
    async with db_manager.session_factory() as db:
        # 使用行锁获取文档
        result = await db.execute(
            select(Document)
            .where(Document.document_id == document_id)
            .with_for_update()  # 行锁，防止并发冲突
        )
        document = result.scalar_one_or_none()
        
        # 幂等性检查
        if document.status in [DocumentStatus.PARSING, DocumentStatus.COMPLETED]:
            logger.info(f"文档 {document_id} 已在处理中，跳过重复处理")
            return {
                "success": True,
                "message": f"任务已在处理中，当前状态: {document.status.value}",
                "idempotent_skip": True
            }
        
        # 立即更新状态，防止其他任务重复处理
        document.status = DocumentStatus.PARSING
        await db.commit()
```

### 2. 任务分配服务保护

在任务分配前添加状态检查：

```python
async def _allocate_single_task(self, db: AsyncSession, document: Document) -> bool:
    # 🔒 额外的状态检查
    if document.status in [DocumentStatus.PARSING, DocumentStatus.COMPLETED]:
        logger.info(f"文档 {document.document_id} 已在处理中，跳过分配")
        return True  # 返回True表示"处理成功"（实际是跳过）
    
    # 继续正常分配逻辑...
```

## 🔍 保护机制特点

### 1. 双重保护
- **任务分配层**：避免提交已在处理中的任务
- **任务执行层**：任务开始时再次检查，确保幂等性

### 2. 原子性保证
- 使用数据库行锁 (`with_for_update()`) 确保状态检查和更新的原子性
- 避免并发任务同时通过状态检查

### 3. 明确的返回标识
- 幂等性跳过时返回 `idempotent_skip: True`
- 便于日志记录和问题排查

## 🧪 测试验证

使用 `test_idempotency.py` 脚本验证：

```bash
python test_idempotency.py
```

### 预期结果
- 并发提交的相同任务中，至少有一个被幂等性保护跳过
- 文档最终状态正确
- 日志显示幂等性保护生效

## 📊 监控指标

### 日志关键词
- `"已在处理中或已完成，跳过重复处理"`
- `"idempotent_skip": true`
- `"跳过分配"`

### 监控建议
1. 统计幂等性跳过次数，了解重复提交频率
2. 监控任务执行时间，确保保护机制不影响性能
3. 检查是否有任务卡在 PARSING 状态过久

## 🚀 部署建议

1. **渐进式部署**：先在测试环境验证
2. **监控告警**：设置幂等性跳过次数告警
3. **回滚准备**：保留原始代码备份

## 🔧 故障排查

### 如果仍有重复处理
1. 检查数据库事务是否正确提交
2. 确认行锁是否生效
3. 查看任务队列是否有重复任务

### 如果任务卡住
1. 检查是否有长时间的 PARSING 状态任务
2. 使用僵尸任务清理机制
3. 手动重置异常状态的文档
