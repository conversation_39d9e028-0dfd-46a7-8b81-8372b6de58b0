# -*- encoding: utf-8 -*-
"""
@File   :reset_stuck_nodes..py
@Time   :2025/7/11 17:56
<AUTHOR>
"""
from loguru import logger
from almond_parser.db.models.document import DocumentStatus

BUSY_STATUSES = {
    DocumentStatus.PENDING,
    DocumentStatus.PARSING,
    DocumentStatus.RETRY_PENDING,
    DocumentStatus.FALLBACK_RETRY,
}


async def reset_current_tasks_cron(ctx):
    """定时重置节点的 current_tasks（兜底修复机制）"""
    logger.info('定时任务启动：修正节点 current_tasks')
    try:
        from almond_parser.db.database import get_async_session
        from almond_parser.db.models.mineru_node import MinerUNode
        from almond_parser.db.models.document import Document
        from sqlalchemy import select, func

        async with get_async_session() as db:
            nodes_result = await db.execute(
                select(MinerUNode).where(MinerUNode.is_enabled == True)
            )
            nodes = nodes_result.scalars().all()

            for node in nodes:
                # 统计该节点是否还有占用中任务
                task_count_result = await db.execute(
                    select(func.count(Document.id)).where(
                        Document.node_id == node.id,
                        Document.status.in_(BUSY_STATUSES)
                    )
                )
                remaining_tasks = task_count_result.scalar() or 0

                if remaining_tasks == 0 and node.current_tasks != 0:
                    logger.warning(
                        f"节点 {node.name} 无任务但 current_tasks={node.current_tasks}，已强制清零"
                    )
                    node.current_tasks = 0
                    await db.flush()

            await db.commit()
            logger.info("定时任务完成：节点 current_tasks 修正完成")

    except Exception as e:
        logger.error(f"定时任务异常：修正节点 current_tasks 失败: {e}")
