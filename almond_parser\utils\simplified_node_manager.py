# -*- encoding: utf-8 -*-
"""
简化的节点并发管理器
移除复杂的reserved_tasks机制，使用更简单的并发控制
"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update
from sqlalchemy.orm import selectinload
from loguru import logger

from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus, ServiceType


class SimplifiedNodeManager:
    """简化的节点管理器 - 单例模式"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    async def allocate_node(
        self, 
        db: AsyncSession,
        service_type: ServiceType,
        parse_mode: Optional[str] = None,
        document_id: Optional[str] = None
    ) -> Optional[MinerUNode]:
        """
        简化的节点分配 - 直接使用current_tasks进行控制
        
        Args:
            db: 数据库会话
            service_type: 服务类型
            parse_mode: 解析模式
            document_id: 文档ID（用于日志）
            
        Returns:
            分配的节点或None
        """
        try:
            # 使用node_selector选择最佳节点
            from almond_parser.utils.node_selector import NodeSelector

            node_selector = NodeSelector(db)

            # 将ServiceType转换为字符串
            service_type_str = {
                ServiceType.DOCUMENT: "document",
                ServiceType.KNOWLEDGE_BASE: "knowledge_base",
                ServiceType.UNIVERSAL: "auto"
            }.get(service_type, "auto")

            node = await node_selector.select_node(
                service_type=service_type_str,
                parse_mode=parse_mode,
                use_compatibility=True
            )

            if not node:
                logger.warning(f"没有可用节点处理 {service_type.value} 类型任务")
                return None

            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行更新
                result = await db.execute(
                    select(MinerUNode)
                    .where(MinerUNode.id == node.id)
                    .with_for_update()
                )
                fresh_node = result.scalar_one_or_none()

                if not fresh_node:
                    logger.error(f"节点不存在: {node.id}")
                    return None

                # 再次检查是否还有可用槽位
                if fresh_node.current_tasks >= fresh_node.max_concurrent_tasks:
                    logger.warning(f"节点 {fresh_node.name} 已满载，无法分配新任务")
                    return None

                # 增加当前任务数
                fresh_node.current_tasks += 1
                fresh_node.last_task_assigned_at = datetime.now()
                await db.flush()

                logger.info(
                    f"✅ 节点分配成功: {fresh_node.name} "
                    f"(当前: {fresh_node.current_tasks}/{fresh_node.max_concurrent_tasks})"
                )

                return fresh_node
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    # 重新查询节点以获取最新状态并加锁
                    result = await db.execute(
                        select(MinerUNode)
                        .where(MinerUNode.id == node.id)
                        .with_for_update()
                    )
                    fresh_node = result.scalar_one_or_none()

                    if not fresh_node:
                        logger.error(f"节点不存在: {node.id}")
                        return None

                    # 再次检查是否还有可用槽位
                    if fresh_node.current_tasks >= fresh_node.max_concurrent_tasks:
                        logger.warning(f"节点 {fresh_node.name} 已满载，无法分配新任务")
                        return None

                    # 增加当前任务数
                    fresh_node.current_tasks += 1
                    fresh_node.last_task_assigned_at = datetime.now()
                    await db.flush()

                    logger.info(
                        f"✅ 节点分配成功: {fresh_node.name} "
                        f"(当前: {fresh_node.current_tasks}/{fresh_node.max_concurrent_tasks})"
                    )

                    return fresh_node

        except Exception as e:
            logger.error(f"节点分配失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return None
    
    async def release_node(
        self,
        db: AsyncSession,
        node_id: int,
        task_id: str,
        success: bool = True,
        reason: str = "task_completed"
    ) -> bool:
        """
        释放节点任务槽位
        
        Args:
            db: 数据库会话
            node_id: 节点ID
            task_id: 任务ID
            success: 任务是否成功
            reason: 释放原因
            
        Returns:
            是否成功
        """
        try:
            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行更新
                result = await db.execute(
                    select(MinerUNode)
                    .where(MinerUNode.id == node_id)
                    .with_for_update()
                )
                node = result.scalar_one_or_none()

                if not node:
                    logger.error(f"节点不存在: {node_id}")
                    return False

                # 释放任务槽位
                if node.current_tasks > 0:
                    node.current_tasks -= 1
                    if success:
                        node.success_tasks += 1
                    else:
                        node.failed_tasks += 1

                    await db.flush()

                    logger.info(
                        f"✅ 任务槽位释放: {node.name} - {task_id} "
                        f"(原因: {reason}, 当前: {node.current_tasks})"
                    )

                    # 触发pending任务的重新分配
                    await self._trigger_pending_task_allocation(db)

                    return True
                else:
                    logger.warning(f"节点 {node.name} 当前任务数为0，无需释放")
                    return False
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    # 获取节点并加锁
                    result = await db.execute(
                        select(MinerUNode)
                        .where(MinerUNode.id == node_id)
                        .with_for_update()
                    )
                    node = result.scalar_one_or_none()

                    if not node:
                        logger.error(f"节点不存在: {node_id}")
                        return False

                    # 释放任务槽位
                    if node.current_tasks > 0:
                        node.current_tasks -= 1
                        if success:
                            node.success_tasks += 1
                        else:
                            node.failed_tasks += 1

                        await db.flush()

                        logger.info(
                            f"✅ 任务槽位释放: {node.name} - {task_id} "
                            f"(原因: {reason}, 当前: {node.current_tasks})"
                        )

                        # 触发pending任务的重新分配
                        await self._trigger_pending_task_allocation(db)

                        return True
                    else:
                        logger.warning(f"节点 {node.name} 当前任务数为0，无需释放")
                        return False

        except Exception as e:
            logger.error(f"释放任务槽位失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return False
    
    async def _trigger_pending_task_allocation(self, db: AsyncSession):
        """触发pending任务的重新分配 - 智能版本"""
        try:
            # 延迟导入避免循环依赖
            from almond_parser.services.task_allocation_service import task_allocation_service
            from almond_parser.db.models.document import Document, DocumentStatus
            from sqlalchemy import select, func

            # 快速检查是否有等待任务
            pending_result = await db.execute(
                select(func.count(Document.id)).where(
                    Document.status.in_([DocumentStatus.UPLOADED, DocumentStatus.RETRY_PENDING]),
                    Document.task_id.is_(None)
                )
            )
            pending_count = pending_result.scalar() or 0

            # 只有在有等待任务时才触发分配
            if pending_count > 0:
                import asyncio
                # 异步触发任务分配，分配数量基于等待任务数 - 使用按节点类型分配
                max_allocations = min(pending_count, 10)  # 最多一次分配10个
                asyncio.create_task(
                    task_allocation_service.allocate_pending_tasks_by_node_type(max_allocations=max_allocations)
                )
                logger.debug(f"触发任务分配: 等待任务={pending_count}, 分配数量={max_allocations}")

        except Exception as e:
            logger.error(f"触发pending任务分配失败: {e}")
    
    async def cleanup_stuck_tasks(self, db: AsyncSession, timeout_minutes: int = 30) -> Dict[str, Any]:
        """
        清理卡住的任务（超时任务）
        
        Args:
            db: 数据库会话
            timeout_minutes: 超时时间（分钟）
            
        Returns:
            清理统计信息
        """
        try:
            timeout_time = datetime.now() - timedelta(minutes=timeout_minutes)
            
            # 查找超时的节点
            result = await db.execute(
                select(MinerUNode)
                .where(
                    MinerUNode.current_tasks > 0,
                    MinerUNode.last_task_assigned_at < timeout_time
                )
            )
            
            stuck_nodes = result.scalars().all()
            cleaned_count = 0
            released_slots = 0
            
            for node in stuck_nodes:
                logger.warning(f"发现卡住的节点: {node.name}, 当前任务数: {node.current_tasks}")
                
                # 重置节点状态
                released_slots += node.current_tasks
                node.current_tasks = 0
                cleaned_count += 1
                
                logger.info(f"已重置节点状态: {node.name}")
            
            await db.commit()
            
            stats = {
                "cleaned_nodes": cleaned_count,
                "released_slots": released_slots,
                "timeout_minutes": timeout_minutes
            }
            
            if cleaned_count > 0:
                logger.info(f"清理完成: {stats}")
                # 触发pending任务重新分配
                await self._trigger_pending_task_allocation(db)
            
            return stats
            
        except Exception as e:
            logger.error(f"清理卡住任务失败: {e}")
            await db.rollback()
            return {"error": str(e)}


# 全局实例
simplified_node_manager = SimplifiedNodeManager()
