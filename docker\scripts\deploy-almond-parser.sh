#!/bin/bash

# 部署杏仁解析服务的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查镜像是否存在
    if ! docker images | grep -q "almond-parser"; then
        log_error "almond-parser 镜像不存在，请先运行 ./scripts/build.sh"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查依赖服务
check_dependencies() {
    log_info "检查依赖服务"
    
    # 检查网络
    if ! docker network ls | grep -q "parserflow-network"; then
        log_error "网络 parserflow-network 不存在，请先运行 ./scripts/deploy-infrastructure.sh"
        exit 1
    fi
    
    # 检查MySQL
    if ! docker ps | grep -q "parserflow-mysql"; then
        log_warning "MySQL 服务未运行，尝试启动基础设施..."
        ./scripts/deploy-infrastructure.sh
    fi
    
    # 检查Redis
    if ! docker ps | grep -q "parserflow-redis"; then
        log_warning "Redis 服务未运行，尝试启动基础设施..."
        ./scripts/deploy-infrastructure.sh
    fi
    
    log_success "依赖服务检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录"
    
    mkdir -p data/uploads
    mkdir -p logs/almond
    mkdir -p data/output
    
    log_success "目录创建完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库"
    
    # 等待MySQL就绪
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec parserflow-mysql mysqladmin ping -h localhost --silent; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "MySQL 连接超时"
        exit 1
    fi
    
    # 检查数据库是否已初始化
    if docker exec parserflow-mysql mysql -u root -pparserflow123 -e "USE parserflow; SHOW TABLES;" 2>/dev/null | grep -q "users"; then
        log_info "数据库已初始化"
    else
        log_info "首次运行，将自动初始化数据库"
    fi
    
    log_success "数据库检查完成"
}

# 部署杏仁解析服务
deploy_almond_parser() {
    log_info "部署杏仁解析服务"
    
    # 加载环境变量
    if [ -f "config/.env.almond-parser" ]; then
        export $(cat config/.env.almond-parser | grep -v '^#' | xargs)
    fi
    
    # 启动服务
    docker-compose -f compose/almond-parser.yml up -d
    
    log_success "杏仁解析服务启动成功"
}

# 等待服务就绪
wait_for_service() {
    log_info "等待杏仁解析服务就绪..."
    
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            log_success "杏仁解析服务已就绪"
            break
        fi
        sleep 3
        timeout=$((timeout-3))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "杏仁解析服务启动超时"
        log_info "查看日志："
        docker-compose -f compose/almond-parser.yml logs --tail=20
        exit 1
    fi
}

# 显示服务状态
show_status() {
    log_info "=== 服务状态 ==="
    docker-compose -f compose/almond-parser.yml ps
    
    log_info "=== 服务访问地址 ==="
    echo "杏仁解析 API:  http://localhost:8000"
    echo "API 文档:      http://localhost:8000/docs"
    echo "健康检查:      http://localhost:8000/health"
    
    log_info "=== 服务日志 ==="
    echo "查看日志: docker-compose -f compose/almond-parser.yml logs -f"
}

# 主函数
main() {
    log_info "开始部署杏仁解析服务"
    
    # 切换到docker目录
    cd "$(dirname "$0")/.."
    
    # 执行部署步骤
    check_environment
    check_dependencies
    create_directories
    init_database
    deploy_almond_parser
    wait_for_service
    show_status
    
    log_success "杏仁解析服务部署完成！"
    log_info "现在可以部署其他服务："
    echo "  ./scripts/deploy-mineru-api.sh     # 部署MinerU API"
    echo "  ./scripts/deploy-web.sh            # 部署Web前端"
}

# 执行主函数
main "$@"
