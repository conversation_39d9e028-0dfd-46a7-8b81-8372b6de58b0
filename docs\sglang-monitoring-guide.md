# SGLang 服务监控功能使用指南

## 🎯 功能概述

杏仁解析中心现已集成SGLang服务监控功能，通过MinerU-API接口实现：

- ✅ 实时监控所有MinerU节点的SGLang服务状态
- 🔄 通过MinerU-API自动重启失败的SGLang服务
- 🚨 智能告警通知（企业微信）
- 📊 在节点管理界面显示SGLang状态
- 🔒 安全的内网通信，无需开放SGLang服务端口

## 🏗️ 架构设计

### 监控架构

```
杏仁解析中心 → MinerU-API → SGLang服务
     ↓              ↓           ↓
  监控服务      管理接口     实际服务
     ↓              ↓           ↓
  状态更新      健康检查     服务重启
```

**优势**：
- 🔒 **安全性**：无需直接访问SGLang服务端口
- 🛡️ **防火墙友好**：只需MinerU-API端口可访问
- 📊 **信息丰富**：获取更详细的服务状态信息
- 🔧 **统一管理**：通过MinerU-API统一管理SGLang服务

### 数据库字段

在`mineru_nodes`表中新增以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `sglang_status` | ENUM | SGLang服务状态 |
| `sglang_port` | INT | SGLang服务端口 |
| `sglang_url` | VARCHAR(500) | SGLang服务URL |
| `sglang_last_check` | DATETIME | 最后检查时间 |
| `sglang_consecutive_failures` | INT | 连续失败次数 |
| `sglang_last_restart` | DATETIME | 最后重启时间 |
| `sglang_restart_count` | INT | 重启次数 |

### 状态枚举

```python
class SglangStatus(str, Enum):
    ONLINE = "online"        # 在线
    OFFLINE = "offline"      # 离线
    ERROR = "error"          # 错误
    UNKNOWN = "unknown"      # 未知
    RESTARTING = "restarting" # 重启中
```

### 端口分配规则

SGLang端口 = 30000 + (MinerU-API端口 - 2233)

示例：
- MinerU-API: 2233 → SGLang: 30000
- MinerU-API: 2234 → SGLang: 30001
- MinerU-API: 2235 → SGLang: 30002

### MinerU-API接口

监控系统使用以下MinerU-API接口：

| 接口 | 方法 | 功能 |
|------|------|------|
| `/sglang/status` | GET | 获取SGLang基本状态 |
| `/sglang/health` | GET | 获取SGLang详细健康状态 |
| `/sglang/restart` | POST | 重启SGLang服务 |

**接口响应示例**：

```json
// GET /sglang/health
{
  "health_check": {
    "healthy": true,
    "response_time": 0.15,
    "error": null
  },
  "process_info": {
    "total_processes": 2,
    "main_pid": 12345
  },
  "port_status": "listening"
}
```

## 🚀 部署步骤

### 1. 数据库迁移

```bash
# 执行数据库迁移脚本
python almond_parser/migrations/add_sglang_monitoring.py
```

### 2. 重启服务

```bash
# 重启杏仁解析服务
python almond_parser/start_services.py
```

### 3. 验证功能

访问管理界面，检查节点列表是否显示SGLang状态列。

## 📋 定时任务

系统会自动运行以下定时任务：

| 任务名称 | 频率 | 功能 |
|----------|------|------|
| `sglang_monitor_cron` | 每2分钟 | 监控SGLang服务状态 |
| `sglang_restart_cron` | 每5分钟 | 自动重启失败的服务 |
| `sglang_alert_cron` | 每10分钟 | 检查并发送告警 |

## 🔧 API接口

### 获取节点SGLang状态

```http
GET /api/mineru-nodes/{node_id}/sglang/status
```

响应示例：
```json
{
  "success": true,
  "node_id": 1,
  "sglang_status": "online",
  "sglang_url": "http://192.168.1.100:30000",
  "healthy": true,
  "consecutive_failures": 0,
  "response_time": 0.15
}
```

### 重启节点SGLang服务

```http
POST /api/mineru-nodes/{node_id}/sglang/restart
```

### 监控所有SGLang服务

```http
POST /api/mineru-nodes/sglang/monitor-all
```

### 重启失败的SGLang服务

```http
POST /api/mineru-nodes/sglang/restart-failed
```

## 🚨 告警机制

### 告警触发条件

1. **连续失败3次以上**
2. **SGLang状态为OFFLINE或ERROR**
3. **节点已启用**

### 告警频率控制

- 同一节点1小时内最多告警1次
- 避免告警轰炸

### 告警内容

告警消息包含：
- 节点基本信息
- SGLang服务状态
- 失败次数和重启次数
- 建议处理步骤

### 企业微信配置

确保在配置文件中设置：
```python
wecom_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"
```

## 🔄 重启机制

### 自动重启条件

1. **连续失败次数 ≥ 3**
2. **最近10分钟内未重启过**
3. **1小时内重启次数 < 3**

### 重启流程

1. 检测到SGLang服务异常
2. 发送重启请求到MinerU-API节点
3. 等待5分钟后开始检测
4. 如果仍然异常，发送企业微信告警

### 重启API

重启请求发送到：`POST {mineru_api_url}/sglang/restart`

## 📊 监控指标

### 节点级别指标

- SGLang服务状态
- 响应时间
- 连续失败次数
- 重启次数
- 最后检查时间

### 系统级别指标

- 在线节点数量
- 异常节点数量
- 总重启次数
- 告警发送次数

## 🛠️ 故障排除

### 常见问题

#### 1. SGLang状态一直显示UNKNOWN

**原因**：节点SGLang信息未初始化

**解决**：
```bash
# 重新运行迁移脚本
python almond_parser/migrations/add_sglang_monitoring.py
```

#### 2. 重启失败

**原因**：MinerU-API节点不支持重启接口

**解决**：
1. 确保MinerU-API版本支持`/sglang/restart`接口
2. 检查节点网络连接
3. 查看MinerU-API日志

#### 3. 告警不发送

**原因**：企业微信配置错误

**解决**：
1. 检查`wecom_webhook_url`配置
2. 测试企业微信机器人连接
3. 查看告警服务日志

### 日志查看

```bash
# 查看SGLang监控日志
grep "sglang" logs/almond_parser.log

# 查看定时任务日志
grep "SGLang" logs/arq_worker.log
```

### 手动触发

```bash
# 手动触发监控
curl -X POST "http://localhost:8010/api/mineru-nodes/sglang/monitor-all" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 手动重启失败服务
curl -X POST "http://localhost:8010/api/mineru-nodes/sglang/restart-failed" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📈 性能优化

### 监控频率调整

如果节点较多，可以调整监控频率：

```python
# 在 arq_app.py 中修改
cron(
    coroutine=sglang_monitor_task.sglang_monitor_cron,
    minute=interval_set(5),  # 改为每5分钟
    run_at_startup=True,
)
```

### 并发控制

监控任务使用异步并发，但可以通过以下方式控制：

```python
# 在 SglangMonitorService 中调整超时时间
self.timeout = aiohttp.ClientTimeout(total=5, connect=2)
```

## 🔮 未来规划

- [ ] 支持自定义监控间隔
- [ ] 增加更多监控指标（GPU使用率、内存等）
- [ ] 支持多种告警渠道（钉钉、邮件等）
- [ ] 监控数据可视化图表
- [ ] 智能故障预测

## 📞 技术支持

如有问题，请：

1. 查看日志文件
2. 检查配置文件
3. 联系技术支持团队

---

**注意**：此功能需要MinerU-API支持SGLang重启接口，请确保使用最新版本。
