"""
回调测试服务器 - 用于测试回调功能
"""
from fastapi import FastAPI, Request
from loguru import logger
import uvicorn
import json

app = FastAPI(title="Callback Test Server")


@app.post("/webhook")
async def receive_callback(request: Request):
    """接收回调通知"""
    try:
        data = await request.json()
        logger.info("收到回调通知:")
        logger.info(json.dumps(data, indent=2, ensure_ascii=False))
        
        # 这里可以添加你的业务逻辑
        task_id = data.get("task_id")
        status = data.get("status")
        
        if status == "completed":
            logger.success(f"任务 {task_id} 完成!")
            result = data.get("result", {})
            files = result.get("files", {})
            logger.info(f"生成的文件: {list(files.keys())}")
        elif status == "failed":
            logger.error(f"任务 {task_id} 失败: {data.get('error', '未知错误')}")
        
        return {"status": "ok", "message": "回调已接收"}
        
    except Exception as e:
        logger.error(f"处理回调失败: {e}")
        return {"status": "error", "message": str(e)}


@app.get("/")
async def root():
    """根路径"""
    return {"message": "Callback Test Server is running"}


if __name__ == "__main__":
    logger.info("启动回调测试服务器: http://localhost:9000")
    uvicorn.run(app, host="0.0.0.0", port=9000)
