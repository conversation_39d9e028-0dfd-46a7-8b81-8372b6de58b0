# -*- encoding: utf-8 -*-
"""
添加节点并发控制字段的数据库迁移
"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime


def upgrade():
    """添加节点并发控制相关字段"""
    
    # 添加 reserved_tasks 字段
    op.add_column('mineru_nodes', 
        sa.Column('reserved_tasks', sa.Integer(), nullable=False, default=0, comment='预留任务数')
    )
    
    # 添加 last_task_assigned_at 字段
    op.add_column('mineru_nodes', 
        sa.Column('last_task_assigned_at', sa.DateTime(), nullable=True, comment='最后分配任务时间')
    )
    
    # 为现有记录设置默认值
    connection = op.get_bind()
    connection.execute(
        sa.text("UPDATE mineru_nodes SET reserved_tasks = 0 WHERE reserved_tasks IS NULL")
    )


def downgrade():
    """移除节点并发控制相关字段"""
    
    # 移除添加的字段
    op.drop_column('mineru_nodes', 'last_task_assigned_at')
    op.drop_column('mineru_nodes', 'reserved_tasks')
