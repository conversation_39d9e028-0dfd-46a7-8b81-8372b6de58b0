# Dashboard 仪表板功能说明

## 📊 功能模块

### 1. 系统运行状态
- **MinerU 在线节点数**: 显示当前在线的解析节点数量，按服务类型分类
- **解析任务运行状态**: 显示队列中的任务数量和正在处理的任务数
- **今日成功率**: 显示当日任务的成功率统计
- **平均负载**: 显示所有节点的平均负载情况

### 2. 今日概况
- **今日提交文档数**: 显示当日提交的文档总数，包含与昨日的对比趋势
- **今日完成解析数**: 显示当日完成解析的文档数量，包含趋势对比
- **平均耗时**: 显示文档解析的平均耗时，包含性能趋势

### 3. 历史趋势
- **最近7天任务曲线图**: 展示成功/失败任务的趋势变化
- **节点负载折线图**: 显示最近24小时的节点负载变化情况

### 4. 错误摘要
- **最近异常任务列表**: 显示最近发生的错误任务，支持重试操作
- **Top5 错误原因分布饼图**: 统计最常见的错误类型分布

### 5. 快捷入口
- **上传文档入口**: 快速跳转到文档上传页面
- **系统配置**: 快速访问节点管理和API密钥管理
- **查看日志**: 快速访问解析任务和统计分析页面

## 🔄 实时更新

- Dashboard 每30秒自动刷新数据
- 支持手动刷新错误列表
- 图表数据实时更新

## 🎯 快速操作

- 点击错误任务可查看详细信息
- 支持一键重试失败的任务
- 快捷按钮直接跳转到相关管理页面

## 📱 响应式设计

- 支持桌面端和移动端访问
- 自适应不同屏幕尺寸
- 优化的触摸交互体验

## 🔧 技术实现

- **前端**: Vue 3 + Element Plus + ECharts
- **后端**: FastAPI + SQLAlchemy
- **实时通信**: WebSocket (可选)
- **图表库**: ECharts 5.x

## 📈 数据来源

- 系统状态数据来自 MinerU 节点管理
- 任务统计来自解析任务记录
- 错误信息来自任务执行日志
- 趋势数据基于历史记录分析
