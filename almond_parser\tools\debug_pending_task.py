#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
调试特定的 RETRY_PENDING 任务
"""
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
from sqlalchemy import select, func, and_, or_
from loguru import logger


async def debug_specific_task():
    """调试特定的 RETRY_PENDING 任务"""
    print("=" * 60)
    print("调试 RETRY_PENDING 任务")
    print("=" * 60)
    
    async with get_async_session() as db:
        # 1. 查找所有 RETRY_PENDING 任务
        print("\n1. 查找所有 RETRY_PENDING 任务:")
        retry_pending_result = await db.execute(
            select(Document).where(
                Document.status == DocumentStatus.RETRY_PENDING
            ).order_by(Document.created_at.desc()).limit(10)
        )
        
        retry_tasks = retry_pending_result.scalars().all()
        print(f"  总共找到 {len(retry_tasks)} 个 RETRY_PENDING 任务")
        
        for task in retry_tasks:
            print(f"    文档ID: {task.document_id}")
            print(f"      文件名: {task.file_name}")
            print(f"      状态: {task.status.value}")
            print(f"      任务ID: {task.task_id}")
            print(f"      重试次数: {task.retry_count}/{task.max_retries}")
            print(f"      下次重试: {task.next_retry_at}")
            print(f"      创建时间: {task.created_at}")
            print(f"      更新时间: {task.updated_at}")
            print()
        
        # 2. 使用修复后的查询条件检查
        print("2. 使用修复后的查询条件:")
        current_time = datetime.now()
        
        allocatable_result = await db.execute(
            select(Document).where(
                and_(
                    or_(
                        # 新上传的文档
                        Document.status == DocumentStatus.UPLOADED,
                        # 等待重试的文档（需要额外检查重试条件）
                        and_(
                            Document.status == DocumentStatus.RETRY_PENDING,
                            or_(
                                Document.next_retry_at.is_(None),  # 没有设置重试时间
                                Document.next_retry_at <= current_time  # 重试时间已到
                            ),
                            Document.retry_count < Document.max_retries  # 未超过最大重试次数
                        )
                    ),
                    Document.task_id.is_(None),  # 确保没有正在处理
                )
            ).order_by(Document.created_at.desc()).limit(10)
        )
        
        allocatable_tasks = allocatable_result.scalars().all()
        print(f"  可分配任务数: {len(allocatable_tasks)}")
        
        for task in allocatable_tasks:
            print(f"    文档ID: {task.document_id}")
            print(f"      文件名: {task.file_name}")
            print(f"      状态: {task.status.value}")
            print(f"      任务ID: {task.task_id}")
            print(f"      重试次数: {task.retry_count}/{task.max_retries}")
            print(f"      下次重试: {task.next_retry_at}")
            print(f"      当前时间: {current_time}")
            print(f"      重试时间已到: {task.next_retry_at is None or task.next_retry_at <= current_time}")
            print()
        
        # 3. 检查节点状态
        print("3. 检查节点状态:")
        node_result = await db.execute(
            select(
                MinerUNode.id,
                MinerUNode.name,
                MinerUNode.status,
                MinerUNode.is_enabled,
                MinerUNode.current_tasks,
                MinerUNode.max_concurrent_tasks
            ).where(
                MinerUNode.is_enabled == True
            )
        )
        
        nodes = node_result.all()
        print(f"  总共 {len(nodes)} 个启用的节点:")
        
        for node in nodes:
            print(f"    节点ID: {node.id}")
            print(f"      名称: {node.name}")
            print(f"      状态: {node.status.value}")
            print(f"      启用: {node.is_enabled}")
            print(f"      当前任务: {node.current_tasks}/{node.max_concurrent_tasks}")
            print(f"      可用: {node.status in [NodeStatus.ONLINE, NodeStatus.BUSY]}")
            print()
        
        # 4. 计算系统容量
        print("4. 系统容量计算:")
        capacity_result = await db.execute(
            select(
                func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                func.sum(MinerUNode.current_tasks).label("current_load"),
                func.count(MinerUNode.id).label("active_nodes")
            ).where(
                MinerUNode.is_enabled == True,
                MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])
            )
        )
        
        capacity_row = capacity_result.first()
        total_capacity = capacity_row.total_capacity or 0
        current_load = capacity_row.current_load or 0
        active_nodes = capacity_row.active_nodes or 0
        available_capacity = total_capacity - current_load
        
        print(f"  活跃节点: {active_nodes}")
        print(f"  总容量: {total_capacity}")
        print(f"  当前负载: {current_load}")
        print(f"  可用容量: {available_capacity}")


if __name__ == "__main__":
    asyncio.run(debug_specific_task())
