#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
诊断文档上传后卡在UPLOADED状态的问题
"""
import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from sqlalchemy import select, func, and_, or_
from almond_parser.db.database import init_database, get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus, ParseMode, ServiceType
from almond_parser.services.task_allocation_service import TaskAllocationService


async def check_uploaded_documents():
    """检查UPLOADED状态的文档"""
    print("🔍 检查UPLOADED状态的文档...")
    
    async with get_async_session() as db:
        try:
            # 查询UPLOADED状态的文档
            result = await db.execute(
                select(Document)
                .where(Document.status == DocumentStatus.UPLOADED)
                .order_by(Document.created_at.desc())
                .limit(10)
            )
            
            documents = result.scalars().all()
            
            if not documents:
                print("✅ 没有UPLOADED状态的文档")
                return True
            
            print(f"📋 发现 {len(documents)} 个UPLOADED状态的文档:")
            for doc in documents:
                print(f"  - {doc.document_id}: {doc.file_name}")
                print(f"    创建时间: {doc.created_at}")
                print(f"    解析模式: {doc.current_parse_mode}")
                print(f"    task_id: {doc.task_id}")
                print(f"    node_id: {doc.node_id}")
                print(f"    started_at: {doc.started_at}")
                print()
            
            return False
            
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return False


async def check_node_availability():
    """检查节点可用性"""
    print("🔍 检查节点可用性...")
    
    async with get_async_session() as db:
        try:
            # 查询所有节点状态
            result = await db.execute(
                select(
                    MinerUNode.parse_mode,
                    MinerUNode.service_type,
                    MinerUNode.status,
                    MinerUNode.is_enabled,
                    func.count(MinerUNode.id).label("count"),
                    func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                    func.sum(MinerUNode.current_tasks).label("current_load")
                ).group_by(
                    MinerUNode.parse_mode,
                    MinerUNode.service_type,
                    MinerUNode.status,
                    MinerUNode.is_enabled
                )
            )
            
            print("🖥️ 节点状态统计:")
            total_available_capacity = 0
            
            for row in result:
                status_icon = "✅" if row.is_enabled and row.status == NodeStatus.ONLINE else "❌"
                print(f"  {status_icon} {row.parse_mode.value}_{row.service_type.value} ({row.status.value}, enabled={row.is_enabled}):")
                print(f"    节点数: {row.count}")
                print(f"    容量: {row.current_load or 0}/{row.total_capacity or 0}")
                
                if row.is_enabled and row.status in [NodeStatus.ONLINE, NodeStatus.BUSY]:
                    available = (row.total_capacity or 0) - (row.current_load or 0)
                    total_available_capacity += max(0, available)
                    print(f"    可用: {available}")
                print()
            
            print(f"📊 系统总可用容量: {total_available_capacity}")
            return total_available_capacity > 0
            
        except Exception as e:
            print(f"❌ 检查节点失败: {e}")
            return False


async def check_task_allocation_service():
    """检查任务分配服务"""
    print("🔍 检查任务分配服务...")
    
    try:
        task_service = TaskAllocationService()
        
        # 测试获取节点类型容量
        async with get_async_session() as db:
            capacities = await task_service._get_node_type_capacities(db)
            
            if not capacities:
                print("❌ 没有可用的节点类型容量")
                return False
            
            print(f"📊 节点类型容量 ({len(capacities)} 种):")
            for cap in capacities:
                print(f"  - {cap.type_key}: {cap.available_capacity}/{cap.total_capacity} (节点数: {cap.node_count})")
            
            # 测试查询待分配任务
            total_suitable_tasks = 0
            for cap in capacities:
                if cap.available_capacity > 0:
                    tasks = await task_service._get_tasks_for_node_type(db, cap, 5)
                    print(f"  {cap.type_key} 适合的任务: {len(tasks)}")
                    total_suitable_tasks += len(tasks)
            
            print(f"📝 总适合任务数: {total_suitable_tasks}")
            return total_suitable_tasks > 0
            
    except Exception as e:
        print(f"❌ 检查任务分配服务失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def test_manual_allocation():
    """手动测试任务分配"""
    print("🚀 手动测试任务分配...")
    
    try:
        task_service = TaskAllocationService()
        
        # 执行按节点类型分配
        stats = await task_service.allocate_pending_tasks_by_node_type(max_allocations=5)
        
        print("📊 分配结果:")
        print(f"  成功分配: {stats.get('allocated', 0)}")
        print(f"  分配失败: {stats.get('failed', 0)}")
        print(f"  等待任务: {stats.get('total_pending', 0)}")
        
        if 'error' in stats:
            print(f"  错误: {stats['error']}")
            return False
        
        # 显示详细信息
        details = stats.get('node_type_details', {})
        if details:
            print("\n📋 节点类型分配详情:")
            for node_type, detail in details.items():
                print(f"  {node_type}:")
                print(f"    分配: {detail.get('allocated', 0)}")
                print(f"    失败: {detail.get('failed', 0)}")
                print(f"    容量: {detail.get('available_capacity', 0)}")
                print(f"    任务: {detail.get('suitable_tasks', 0)}")
        
        return stats.get('allocated', 0) > 0 or stats.get('total_pending', 0) == 0
        
    except Exception as e:
        print(f"❌ 手动分配测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def check_arq_worker_status():
    """检查ARQ工作器状态"""
    print("🔍 检查ARQ工作器状态...")
    
    try:
        from almond_parser.tasks.arq_app import arq_manager
        
        # 检查Redis连接
        if hasattr(arq_manager, '_redis_pool') and arq_manager._redis_pool:
            print("✅ ARQ Redis连接正常")
        else:
            print("❌ ARQ Redis连接未初始化")
            return False
        
        # 检查队列状态
        try:
            # 这里可以添加更多的队列状态检查
            print("✅ ARQ队列服务可用")
            return True
        except Exception as e:
            print(f"❌ ARQ队列检查失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ ARQ工作器检查失败: {e}")
        return False


async def check_recent_logs():
    """检查最近的日志"""
    print("🔍 检查最近的任务分配日志...")
    
    async with get_async_session() as db:
        try:
            # 查询最近的文档日志
            from almond_parser.db.models.document import DocumentLog
            
            result = await db.execute(
                select(DocumentLog)
                .where(DocumentLog.created_at >= datetime.now() - timedelta(minutes=10))
                .order_by(DocumentLog.created_at.desc())
                .limit(20)
            )
            
            logs = result.scalars().all()
            
            if not logs:
                print("⚠️ 最近10分钟没有文档日志")
                return False
            
            print(f"📋 最近10分钟的文档日志 ({len(logs)} 条):")
            for log in logs[:10]:  # 只显示前10条
                print(f"  {log.created_at.strftime('%H:%M:%S')} [{log.level}] {log.document_id}: {log.message}")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查日志失败: {e}")
            return False


async def provide_solutions():
    """提供解决方案"""
    print("\n🔧 可能的解决方案:")
    print("1. 检查ARQ工作器是否正在运行:")
    print("   python -m almond_parser.worker")
    print()
    print("2. 手动触发任务分配:")
    print("   python almond_parser/tools/test_node_type_allocation.py")
    print()
    print("3. 检查节点状态:")
    print("   确保至少有一个节点状态为ONLINE且is_enabled=True")
    print()
    print("4. 检查Redis连接:")
    print("   确保Redis服务正在运行且配置正确")
    print()
    print("5. 查看详细日志:")
    print("   检查应用日志中的任务分配相关信息")


async def main():
    """主诊断函数"""
    print("🩺 开始诊断文档上传卡住问题\n")
    
    # 初始化数据库
    await init_database()
    
    # 运行诊断检查
    checks = [
        ("UPLOADED文档检查", check_uploaded_documents),
        ("节点可用性检查", check_node_availability),
        ("任务分配服务检查", check_task_allocation_service),
        ("ARQ工作器状态检查", check_arq_worker_status),
        ("最近日志检查", check_recent_logs),
        ("手动分配测试", test_manual_allocation),
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"🔍 {check_name}:")
        try:
            result = await check_func()
            results.append((check_name, result))
            print(f"{'✅' if result else '❌'} {check_name} {'正常' if result else '异常'}")
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
            results.append((check_name, False))
        
        print("\n" + "-"*50 + "\n")
    
    # 总结
    print("📋 诊断总结:")
    issues = []
    for check_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {check_name}")
        if not result:
            issues.append(check_name)
    
    if issues:
        print(f"\n⚠️ 发现 {len(issues)} 个问题:")
        for issue in issues:
            print(f"  - {issue}")
        
        await provide_solutions()
    else:
        print("\n🎉 所有检查都通过，系统状态正常！")
        print("如果文档仍然卡住，可能是定时任务间隔问题，请等待几秒钟。")


if __name__ == "__main__":
    asyncio.run(main())
