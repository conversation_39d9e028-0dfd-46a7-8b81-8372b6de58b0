<template>
  <div class="table-height-test">
    <div class="page-header">
      <h2>表格高度测试</h2>
      <div class="controls">
        <el-button-group>
          <el-button 
            v-for="size in pageSizes" 
            :key="size"
            :type="currentPageSize === size ? 'primary' : 'default'"
            @click="changePageSize(size)"
          >
            {{ size }}条/页
          </el-button>
        </el-button-group>
        <el-button @click="toggleData">
          {{ showData ? '隐藏数据' : '显示数据' }}
        </el-button>
      </div>
    </div>

    <div class="content-container">
      <div class="info-card">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="视窗高度">
            {{ windowHeight }}px
          </el-descriptions-item>
          <el-descriptions-item label="计算的表格高度">
            {{ calculatedTableHeight }}px
          </el-descriptions-item>
          <el-descriptions-item label="当前数据量">
            {{ currentData.length }}条
          </el-descriptions-item>
          <el-descriptions-item label="每页显示">
            {{ currentPageSize }}条
          </el-descriptions-item>
          <el-descriptions-item label="屏幕类型">
            {{ screenType }}
          </el-descriptions-item>
          <el-descriptions-item label="可用高度">
            {{ availableHeight }}px
          </el-descriptions-item>
          <el-descriptions-item label="内容高度">
            {{ contentHeight }}px
          </el-descriptions-item>
          <el-descriptions-item label="最大允许高度">
            {{ maxAllowedHeight }}px
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="table-card">
        <div class="table-container">
          <el-table
            :data="currentData"
            :max-height="calculatedTableHeight"
            border
            stripe
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="名称" width="150" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" link>
                  查看
                </el-button>
                <el-button type="warning" size="small" link>
                  编辑
                </el-button>
                <el-button type="danger" size="small" link>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              :current-page="currentPage"
              :page-size="currentPageSize"
              :page-sizes="pageSizes"
              :total="totalData.length"
              layout="total, sizes, prev, pager, next, jumper"
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface TestData {
  id: number
  name: string
  description: string
  status: string
  createTime: string
}

const windowHeight = ref(window.innerHeight)
const currentPage = ref(1)
const currentPageSize = ref(10)
const pageSizes = [10, 20, 50, 100]
const showData = ref(true)

// 生成测试数据
const generateTestData = (count: number): TestData[] => {
  const statuses = ['成功', '失败', '处理中', '等待']
  const data: TestData[] = []
  
  for (let i = 1; i <= count; i++) {
    data.push({
      id: i,
      name: `测试项目 ${i}`,
      description: `这是第 ${i} 个测试项目的详细描述，用于测试表格高度和滚动功能。`.repeat(Math.floor(Math.random() * 3) + 1),
      status: statuses[Math.floor(Math.random() * statuses.length)],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString()
    })
  }
  
  return data
}

const totalData = ref<TestData[]>(generateTestData(200))

// 当前显示的数据
const currentData = computed(() => {
  if (!showData.value) return []
  
  const start = (currentPage.value - 1) * currentPageSize.value
  const end = start + currentPageSize.value
  return totalData.value.slice(start, end)
})

// 计算表格高度（复制自 DocumentTable.vue 的逻辑）
const calculatedTableHeight = computed(() => {
  const baseHeight = 320
  const availableHeight = windowHeight.value - baseHeight

  // 计算内容实际需要的高度
  const rowHeight = 60
  const headerHeight = 60
  const contentHeight = currentData.value.length * rowHeight + headerHeight

  // 根据屏幕尺寸设置不同的最大高度策略
  let maxAllowedHeight: number
  if (windowHeight.value <= 768) {
    maxAllowedHeight = Math.max(availableHeight * 0.8, 350)
  } else if (windowHeight.value <= 900) {
    maxAllowedHeight = Math.max(availableHeight * 0.85, 400)
  } else if (windowHeight.value <= 1200) {
    maxAllowedHeight = Math.max(availableHeight * 0.9, 500)
  } else {
    maxAllowedHeight = Math.max(availableHeight * 0.95, 600)
  }

  // 智能高度选择
  const minHeight = 300
  return Math.min(Math.max(contentHeight, minHeight), maxAllowedHeight)
})

const availableHeight = computed(() => windowHeight.value - 320)

const contentHeight = computed(() => {
  const rowHeight = 60
  const headerHeight = 60
  return currentData.value.length * rowHeight + headerHeight
})

const maxAllowedHeight = computed(() => {
  const availableHeight = windowHeight.value - 320
  if (windowHeight.value <= 768) {
    return Math.max(availableHeight * 0.8, 350)
  } else if (windowHeight.value <= 900) {
    return Math.max(availableHeight * 0.85, 400)
  } else if (windowHeight.value <= 1200) {
    return Math.max(availableHeight * 0.9, 500)
  } else {
    return Math.max(availableHeight * 0.95, 600)
  }
})

const screenType = computed(() => {
  if (windowHeight.value <= 768) return '小屏幕'
  if (windowHeight.value <= 900) return '中小屏幕'
  if (windowHeight.value <= 1200) return '大屏幕'
  return '超大屏幕'
})

const getStatusType = (status: string) => {
  switch (status) {
    case '成功': return 'success'
    case '失败': return 'danger'
    case '处理中': return 'warning'
    case '等待': return 'info'
    default: return 'info'
  }
}

const changePageSize = (size: number) => {
  currentPageSize.value = size
  currentPage.value = 1
}

const toggleData = () => {
  showData.value = !showData.value
}

const handleSizeChange = (size: number) => {
  currentPageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const handleResize = () => {
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.table-height-test {
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.page-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.page-header h2 {
  margin: 0;
  color: #1e293b;
}

.controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0;
}

.info-card {
  flex-shrink: 0;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-card {
  background: transparent;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.table-container {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 100%;
  min-height: 0;
}

.pagination-container {
  flex-shrink: 0;
  height: 60px;
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: #fafafa;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.02);
  margin-top: auto;
}
</style>
