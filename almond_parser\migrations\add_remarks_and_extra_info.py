#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
数据库迁移脚本：添加备注和扩展信息字段
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from loguru import logger
from almond_parser.db.database import get_async_session


async def migrate_database():
    """执行数据库迁移"""
    engine = get_async_session()

    try:
        async with engine.begin() as conn:
            logger.info("开始执行数据库迁移：添加备注和扩展信息字段")

            # 检查字段是否已存在
            check_remarks_sql = """
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'documents' 
            AND column_name = 'remarks'
            """

            check_extra_info_sql = """
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'documents' 
            AND column_name = 'extra_info'
            """

            # 检查 remarks 字段
            result = await conn.execute(text(check_remarks_sql))
            remarks_exists = result.scalar() > 0

            # 检查 extra_info 字段
            result = await conn.execute(text(check_extra_info_sql))
            extra_info_exists = result.scalar() > 0

            # 添加 remarks 字段
            if not remarks_exists:
                logger.info("添加 remarks 字段...")
                await conn.execute(text("""
                    ALTER TABLE documents 
                    ADD COLUMN remarks TEXT NULL COMMENT '备注信息'
                """))
                logger.info("✅ remarks 字段添加成功")
            else:
                logger.info("⚠️  remarks 字段已存在，跳过")

            # 添加 extra_info 字段
            if not extra_info_exists:
                logger.info("添加 extra_info 字段...")
                await conn.execute(text("""
                    ALTER TABLE documents 
                    ADD COLUMN extra_info JSON NULL COMMENT '扩展信息(JSON格式)'
                """))
                logger.info("✅ extra_info 字段添加成功")
            else:
                logger.info("⚠️  extra_info 字段已存在，跳过")

            # 提交事务
            await conn.commit()
            logger.info("🎉 数据库迁移完成！")

    except Exception as e:
        logger.error(f"❌ 数据库迁移失败: {e}")
        raise
    finally:
        await engine.dispose()


async def rollback_migration():
    """回滚迁移（删除添加的字段）"""
    engine = get_async_engine()

    try:
        async with engine.begin() as conn:
            logger.info("开始回滚数据库迁移：删除备注和扩展信息字段")

            # 删除 remarks 字段
            try:
                await conn.execute(text("ALTER TABLE documents DROP COLUMN remarks"))
                logger.info("✅ remarks 字段删除成功")
            except Exception as e:
                logger.warning(f"⚠️  删除 remarks 字段失败（可能不存在）: {e}")

            # 删除 extra_info 字段
            try:
                await conn.execute(text("ALTER TABLE documents DROP COLUMN extra_info"))
                logger.info("✅ extra_info 字段删除成功")
            except Exception as e:
                logger.warning(f"⚠️  删除 extra_info 字段失败（可能不存在）: {e}")

            # 提交事务
            await conn.commit()
            logger.info("🎉 数据库回滚完成！")

    except Exception as e:
        logger.error(f"❌ 数据库回滚失败: {e}")
        raise
    finally:
        await engine.dispose()


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="数据库迁移脚本：添加备注和扩展信息字段")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")

    args = parser.parse_args()

    if args.rollback:
        await rollback_migration()
    else:
        await migrate_database()


if __name__ == "__main__":
    asyncio.run(main())
