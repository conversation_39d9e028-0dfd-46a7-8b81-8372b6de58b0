# -*- encoding: utf-8 -*-
"""
DOC文档相关Schema
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum


class DOCDocumentStatus(str, Enum):
    """DOC文档状态枚举"""
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"
    DONE = "DONE"
    ERROR = "ERROR"


class KBType(str, Enum):
    """知识库类型枚举"""
    PERSONAL = "personal"
    PROJECT = "project"


class ParseType(str, Enum):
    """解析类型枚举"""
    OCR = "OCR"
    LAYOUT = "LAYOUT"
    TEXT = "TEXT"


class DownloadConfigResponse(BaseModel):
    """下载配置响应"""
    base_url: str = Field(description="下载基础URL")
    docs_prefix: str = Field(description="多文档下载路径前缀")
    kb_prefix: str = Field(description="知识库下载路径前缀")
    kb_path_prefix: str = Field(description="知识库文件路径前缀")


class ServerConfigResponse(BaseModel):
    """服务器配置响应"""
    name: str = Field(description="服务器标识名")
    label: str = Field(description="显示名称")
    host: str = Field(description="主机地址")
    port: int = Field(description="端口")
    database: str = Field(description="数据库名")
    download: Optional[DownloadConfigResponse] = Field(default=None, description="下载配置")


class DOCDocumentBase(BaseModel):
    """DOC文档基础模型"""
    id: int = Field(description="记录ID")
    document_id: str = Field(description="文档ID")
    batch_id: str = Field(description="批次ID")
    username: str = Field(description="用户名")
    file_name: str = Field(description="文件名")
    file_path: Optional[str] = Field(default=None, description="文件路径")
    file_type: Optional[str] = Field(default=None, description="文件类型")
    file_size: Optional[int] = Field(default=None, description="文件大小")
    status: DOCDocumentStatus = Field(description="文档状态")
    kb_type: Optional[KBType] = Field(default=None, description="知识库类型")
    parse_type: Optional[ParseType] = Field(default=None, description="解析类型")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    created_at: Optional[str] = Field(default=None, description="创建时间")
    updated_at: Optional[str] = Field(default=None, description="更新时间")
    completed_at: Optional[str] = Field(default=None, description="完成时间")
    duration: Optional[int] = Field(default=None, description="耗时（秒）")
    remarks: Optional[str] = Field(default=None, description="备注")
    extra_info: Optional[Dict[str, Any]] = Field(default=None, description="扩展信息")
    source_table: str = Field(description="数据来源表")
    source_name: str = Field(description="数据来源名称")


class DOCDocumentResponse(DOCDocumentBase):
    """DOC文档响应模型"""
    pass


class DOCDocumentQueryParams(BaseModel):
    """DOC文档查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=100, description="每页大小")
    server: Optional[str] = Field(default=None, description="服务器名称")
    source_type: Optional[str] = Field(default="document", description="数据源类型: document(多文档) 或 knowledge(知识库)")
    username: Optional[str] = Field(default=None, description="用户名")
    file_name: Optional[str] = Field(default=None, description="文件名")
    status: Optional[str] = Field(default=None, description="状态")
    kb_type: Optional[KBType] = Field(default=None, description="知识库类型")
    parse_type: Optional[ParseType] = Field(default=None, description="解析类型")
    batch_id: Optional[str] = Field(default=None, description="批次ID")
    document_id: Optional[str] = Field(default=None, description="文档ID")
    sort_by: Optional[str] = Field(default=None, description="排序字段")
    sort_order: Optional[str] = Field(default="desc", description="排序方向")


class DOCDocumentListResponse(BaseModel):
    """DOC文档列表响应"""
    items: List[DOCDocumentResponse] = Field(description="文档列表")
    pagination: Dict[str, Any] = Field(description="分页信息")


class DOCDocumentRetryRequest(BaseModel):
    """DOC文档重试请求"""
    server: str = Field(description="服务器名称")


class DOCDocumentLogsResponse(BaseModel):
    """DOC文档日志响应"""
    logs: List[str] = Field(description="日志列表")


class ServerListResponse(BaseModel):
    """服务器列表响应"""
    servers: List[ServerConfigResponse] = Field(description="服务器列表")
