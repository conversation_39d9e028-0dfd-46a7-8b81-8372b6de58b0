<template>
  <div class="table-container">
    <div class="table-wrapper">
      <el-table
        v-loading="loading"
        :data="documents"
        border
        style="width: 100%"
        @sort-change="handleSortChange"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: 600,
          fontSize: '14px'
        }"
        stripe
        highlight-current-row
      >
        <el-table-column prop="file_name" label="文件名" :min-width="260" fixed="left" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ row.file_name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="file_type" label="文件类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag size="small" :type="getFileTypeColor(row.file_type)">
              {{ row.file_type || 'Unknown' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="file_size" label="文件大小" width="120" align="center" sortable="custom">
          <template #default="{ row }">
            <span v-if="row.file_size">{{ formatFileSize(row.file_size) }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="120" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="kb_type" label="知识库类型" width="120" align="center" v-if="showKBType">
          <template #default="{ row }">
            <el-tag
              v-if="row.kb_type"
              :color="getKBTypeColor(row.kb_type)"
              size="small"
              class="kb-type-tag"
            >
              {{ getKBTypeText(row.kb_type) }}
            </el-tag>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <!-- 解析类型已移到详情，这里不展示 -->

        <el-table-column prop="username" label="用户名" width="120" show-overflow-tooltip />

        <el-table-column prop="created_at" label="创建时间" width="180" align="center" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间" width="180" align="center" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>

        <el-table-column prop="duration" label="耗时" width="150" align="center">
          <template #default="{ row }">
            <span>{{ formatDuration(row.created_at, row.updated_at, row.status) }}</span>
          </template>
        </el-table-column>

        <!-- 批次ID与文档ID已移到详情 -->

        <!-- 数据来源列已隐藏 -->

        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button type="primary" link @click="handleViewDetails(row)">
                <el-icon><View /></el-icon>
                <span class="button-text">详情</span>
              </el-button>
              <el-dropdown @command="(command: string) => handleDropdownCommand(command, row)" trigger="click">
                <el-button type="primary" link>
                  <el-icon><More /></el-icon>
                  <span class="button-text">更多</span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="canRetry(row)" command="retry">
                      <el-icon><RefreshRight /></el-icon>重试
                    </el-dropdown-item>
                    <el-dropdown-item command="download">
                      <el-icon><Download /></el-icon>下载
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  DocumentCopy,
  Document,
  RefreshRight,
  Download,
  More,
  View
} from '@element-plus/icons-vue'
import type { DOCDocument } from '@/types/docDocument'
import { formatDateTime, formatFileSize, formatDuration } from '@/utils/format'
import { copyToClipboard } from '@/utils/clipboard'

const props = defineProps<{
  loading: boolean
  documents: DOCDocument[]
  total: number
  currentPage: number
  pageSize: number
}>()

const emit = defineEmits<{
  (e: 'retry', document: DOCDocument): void
  (e: 'view-details', document: DOCDocument): void
  (e: 'view-logs', document: DOCDocument): void
  (e: 'download', document: DOCDocument): void
  (e: 'update:current-page', value: number): void
  (e: 'update:page-size', value: number): void
  (e: 'sort-change', data: { prop: string, order: string | null }): void
}>()

const currentPage = computed(() => props.currentPage)
const pageSize = computed(() => props.pageSize)

// 是否显示知识库类型列
const showKBType = computed(() => {
  return props.documents.some(doc => doc.kb_type)
})

// 是否显示解析类型列（已移到详情页，此处保留逻辑以便日后恢复）
const showParseType = computed(() => {
  return props.documents.some(doc => doc.parse_type)
})

const canRetry = (document: DOCDocument) => {
  return document.status === 'FAILED' || document.status === 'PROCESSING'
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: 'info',
    PROCESSING: 'warning',
    COMPLETED: 'success',
    FAILED: 'danger',
    CANCELLED: 'info',
    ERROR:"danger"
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '等待处理',
    PROCESSING: '处理中',
    COMPLETED: '已完成',
    FAILED: '失败',
    CANCELLED: '已取消',
    ERROR:"失败"
  }
  return statusMap[status] || status
}

const getFileTypeColor = (fileType: string) => {
  const typeMap: Record<string, string> = {
    pdf: 'danger',
    doc: 'primary',
    docx: 'primary',
    ppt: 'warning',
    pptx: 'warning',
    jpg: 'success',
    jpeg: 'success',
    png: 'success'
  }
  return typeMap[fileType?.toLowerCase()] || 'info'
}

const getKBTypeColor = (kbType: string) => {
  return kbType === 'personal' ? '#3b82f6' : '#10b981'
}

const getKBTypeText = (kbType: string) => {
  return kbType === 'personal' ? '个人库' : '项目库'
}

const getParseTypeColor = (parseType: string) => {
  const typeMap: Record<string, string> = {
    OCR: '#f59e0b',
    LAYOUT: '#8b5cf6',
    TEXT: '#06b6d4'
  }
  return typeMap[parseType] || '#6b7280'
}

const handleViewDetails = (document: DOCDocument) => {
  emit('view-details', document)
}

const handleViewLogs = (document: DOCDocument) => {
  emit('view-logs', document)
}

const handleDropdownCommand = (command: string, document: DOCDocument) => {
  switch (command) {
    case 'retry':
      emit('retry', document)
      break
    case 'download':
      emit('download', document)
      break
  }
}

const handleSizeChange = (val: number) => {
  emit('update:page-size', val)
}

const handleCurrentChange = (val: number) => {
  emit('update:current-page', val)
}

const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  emit('sort-change', { prop, order })
}
</script>

<style scoped>
.table-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  /* 移除固定高度，改为自适应 */
}

.table-wrapper {
  padding: 12px;
  overflow: visible; /* 允许内容自然显示 */
}
/* 表格自适应高度，无需额外样式 */

.pagination-wrapper {
  flex-shrink: 0;
  padding: 12px 16px;
  min-height: 60px; /* 增加分页区域高度 */
  border-top: 1px solid #e5e7eb;
  background: #fafbfc;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 8px; /* 添加底部间距 */
}

.file-info {
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #3b82f6;
  font-size: 16px;
}

.file-name {
  font-weight: 500;
  color: #374151;
}

.copy-wrapper {
  align-items: center;
  gap: 8px;
}

.id-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6b7280;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.copy-btn {
  opacity: 0;
  transition: opacity 0.2s;
  padding: 2px;
  min-height: auto;
}

.copy-wrapper:hover .copy-btn {
  opacity: 1;
}

.operation-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.button-text {
  margin-left: 4px;
  font-size: 13px;
}

.text-muted {
  color: #9ca3af;
  font-style: italic;
}

.kb-type-tag,
.parse-type-tag {
  border: none;
  color: #fff;
  font-weight: 500;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .table-container {
    min-height: 400px; /* 小屏下调整最小高度 */
    max-height: calc(100vh - 150px); /* 小屏下调整最大高度 */
  }

  .table-wrapper {
    padding: 8px; /* 小屏下减少 padding */
  }

  .pagination-wrapper {
    min-height: 50px; /* 小屏下减少分页高度 */
    padding: 8px 12px; /* 小屏下减少 padding */
  }

  .operation-buttons {
    gap: 4px;
  }

  .button-text {
    display: none;
  }
}
</style>
