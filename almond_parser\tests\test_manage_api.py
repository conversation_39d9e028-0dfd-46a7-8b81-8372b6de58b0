#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
管理后台 API 测试脚本
"""
import asyncio
import aiohttp
import json
from loguru import logger

from almond_parser.config import settings


async def test_admin_workflow():
    """测试管理员工作流程"""
    
    base_url = f"http://{settings.HOST}:{settings.PORT}/api/v1"
    
    # 测试数据
    admin_user = {
        "username": "admin",
        "password": "admin123",
        "confirm_password": "admin123",
        "is_admin": True,
        "super_key": settings.SUPER_KEY
    }
    
    regular_user = {
        "username": "testuser",
        "password": "test123",
        "confirm_password": "test123",
        "is_admin": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            
            # 1. 注册管理员账号
            logger.info("🔐 注册管理员账号...")
            admin_token = await register_user(session, base_url, admin_user)
            
            if not admin_token:
                logger.error("管理员注册失败")
                return
            
            # 2. 注册普通用户
            logger.info("👤 注册普通用户...")
            user_token = await register_user(session, base_url, regular_user)
            
            # 3. 管理员登录
            logger.info("🔑 管理员登录...")
            admin_headers = await login_user(session, base_url, {
                "username": admin_user["username"],
                "password": admin_user["password"]
            })
            
            if not admin_headers:
                logger.error("管理员登录失败")
                return
            
            # 4. 测试管理员功能
            await test_admin_functions(session, base_url, admin_headers)
            
    except Exception as e:
        logger.error(f"测试失败: {e}")


async def register_user(session, base_url, user_data):
    """注册用户"""
    try:
        register_url = f"{base_url}/manage/register"
        
        async with session.post(register_url, json=user_data) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"✅ 用户注册成功: {user_data['username']}")
                return result.get("access_token")
            else:
                error_text = await response.text()
                logger.error(f"❌ 用户注册失败: HTTP {response.status}, {error_text}")
                return None
                
    except Exception as e:
        logger.error(f"注册用户异常: {e}")
        return None


async def login_user(session, base_url, login_data):
    """用户登录"""
    try:
        login_url = f"{base_url}/manage/login"
        
        async with session.post(login_url, json=login_data) as response:
            if response.status == 200:
                result = await response.json()
                token = result.get("access_token")
                logger.info(f"✅ 用户登录成功: {login_data['username']}")
                return {"Authorization": f"Bearer {token}"}
            else:
                error_text = await response.text()
                logger.error(f"❌ 用户登录失败: HTTP {response.status}, {error_text}")
                return None
                
    except Exception as e:
        logger.error(f"用户登录异常: {e}")
        return None


async def test_admin_functions(session, base_url, headers):
    """测试管理员功能"""
    
    # 1. 获取当前用户信息
    logger.info("📋 获取当前用户信息...")
    await test_get_me(session, base_url, headers)
    
    # 2. 查询文档记录
    logger.info("📄 查询文档记录...")
    await test_query_documents(session, base_url, headers)
    
    # 3. 查看节点状态
    logger.info("🖥️ 查看节点状态...")
    await test_node_stats(session, base_url, headers)
    
    # 4. 检查任务计数
    logger.info("🔍 检查任务计数...")
    await test_check_task_counts(session, base_url, headers)


async def test_get_me(session, base_url, headers):
    """测试获取用户信息"""
    try:
        me_url = f"{base_url}/manage/me"
        
        async with session.get(me_url, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"✅ 用户信息: {result}")
            else:
                error_text = await response.text()
                logger.error(f"❌ 获取用户信息失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"获取用户信息异常: {e}")


async def test_query_documents(session, base_url, headers):
    """测试查询文档"""
    try:
        docs_url = f"{base_url}/manage/documents"
        params = {
            "page": 1,
            "page_size": 10
        }
        
        async with session.get(docs_url, headers=headers, params=params) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"✅ 文档查询成功: 总数 {result.get('pagination', {}).get('total', 0)}")
            else:
                error_text = await response.text()
                logger.error(f"❌ 文档查询失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"文档查询异常: {e}")


async def test_node_stats(session, base_url, headers):
    """测试节点统计"""
    try:
        stats_url = f"{base_url}/manage/mineru/task-counts"
        
        async with session.get(stats_url, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"✅ 节点统计: {len(result.get('nodes', []))} 个节点")
            else:
                error_text = await response.text()
                logger.error(f"❌ 节点统计失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"节点统计异常: {e}")


async def test_check_task_counts(session, base_url, headers):
    """测试检查任务计数"""
    try:
        check_url = f"{base_url}/manage/mineru/check-task-counts"
        
        async with session.post(check_url, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"✅ 任务计数检查: {result.get('message')}")
            else:
                error_text = await response.text()
                logger.error(f"❌ 任务计数检查失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"任务计数检查异常: {e}")


async def test_api_endpoints():
    """测试所有管理 API 端点"""
    logger.info("🧪 开始管理后台 API 测试...")
    
    # 测试管理员工作流程
    await test_admin_workflow()
    
    logger.info("✅ 管理后台 API 测试完成")


if __name__ == "__main__":
    asyncio.run(test_api_endpoints())
