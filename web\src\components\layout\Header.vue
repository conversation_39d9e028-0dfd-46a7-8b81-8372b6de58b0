<template>
  <div class="header">
    <div class="left">
      <el-button
        class="collapse-btn"
        :icon="isCollapsed ? 'Expand' : 'Fold'"
        @click="toggleCollapse"
      />
      <div class="logo">
        <img src="@/assets/logo.svg" alt="Logo" class="logo-icon" />
        <h1 class="title" v-if="!isCollapsed">杏仁解析</h1>
      </div>
    </div>
    <div class="center">
      <h2 class="page-title">{{ currentRoute }}</h2>
    </div>
    <div class="right">
      <el-dropdown trigger="click">
        <span class="user-dropdown">
          <el-avatar :size="32" icon="UserFilled" />
          <span class="username">{{ username }}</span>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleLogout">登出</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { Expand, Fold } from '@element-plus/icons-vue'

const props = defineProps<{
  isCollapsed: boolean
}>()

const emit = defineEmits<{
  'update:isCollapsed': [value: boolean]
}>()

const route = useRoute()
const userStore = useUserStore()

const username = computed(() => userStore.username)
const currentRoute = computed(() => {
  const routeMap: Record<string, string> = {
    '/': '首页',
    '/documents': '文档列表',
    '/statistics': '数据统计',
    '/settings': '系统设置',
    '/api-keys': 'API密钥'
  }
  return routeMap[route.path] || route.name || ''
})

const toggleCollapse = () => {
  emit('update:isCollapsed', !props.isCollapsed)
}

const handleLogout = () => {
  userStore.logout()
}
</script>

<style scoped>
.header {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  padding: 6px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  transition: all 0.3s;
}

.collapse-btn:hover {
  color: #409EFF;
  background: #f6f6f6;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #34495e;
  margin: 0;
  white-space: nowrap;
}

.center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #34495e;
  margin: 0;
}

.right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 0 8px;
  height: 40px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background: #f6f6f6;
}

.username {
  font-size: 14px;
  color: #34495e;
}
</style> 