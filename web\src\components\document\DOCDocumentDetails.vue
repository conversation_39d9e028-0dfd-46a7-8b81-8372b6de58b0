<template>
  <el-dialog
    v-model="visible"
    title="DOC文档详情"
    width="900px"
    :before-close="handleClose"
    class="document-details-dialog"
  >
    <div v-if="document" class="document-details">
      <!-- 基本信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Document /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label>文件名</label>
              <div class="info-value">{{ document.file_name }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>文件类型</label>
              <div class="info-value">
                <el-tag size="small">{{ document.file_type || '-' }}</el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>文件大小</label>
              <div class="info-value">{{ document.file_size ? formatFileSize(document.file_size) : '-' }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>用户名</label>
              <div class="info-value">{{ document.username }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 状态信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><InfoFilled /></el-icon>
            <span>状态信息</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label>状态</label>
              <div class="info-value">
                <el-tag :type="getStatusType(document.status)" size="small">
                  {{ getStatusText(document.status) }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>数据来源</label>
              <div class="info-value">
                <el-tag 
                  :type="document.source_name === '知识库' ? 'primary' : 'success'" 
                  size="small"
                >
                  {{ document.source_name }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="12" v-if="document.kb_type">
            <div class="info-item">
              <label>知识库类型</label>
              <div class="info-value">
                <el-tag 
                  :color="getKBTypeColor(document.kb_type)" 
                  size="small"
                  class="kb-type-tag"
                >
                  {{ getKBTypeText(document.kb_type) }}
                </el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="12" v-if="document.extra_info?.parse_type">
            <div class="info-item">
              <label>解析类型</label>
              <div class="info-value">
                <el-tag 
                  :color="getParseTypeColor(document.extra_info.parse_type)" 
                  size="small"
                  class="parse-type-tag"
                >
                  {{ document.extra_info.parse_type }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- ID信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Key /></el-icon>
            <span>标识信息</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="24" v-if="document.extra_info?.batch_id">
            <div class="info-item">
              <label>批次ID</label>
              <div class="info-value copy-wrapper">
                <span class="id-text">{{ document.extra_info.batch_id }}</span>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="copyToClipboard(document.extra_info.batch_id)"
                >
                  <el-icon><DocumentCopy /></el-icon>
                </el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="24" v-if="document.extra_info?.document_id">
            <div class="info-item">
              <label>文档ID</label>
              <div class="info-value copy-wrapper">
                <span class="id-text">{{ document.extra_info.document_id }}</span>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="copyToClipboard(document.extra_info.document_id)"
                >
                  <el-icon><DocumentCopy /></el-icon>
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 时间信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Clock /></el-icon>
            <span>时间信息</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label>创建时间</label>
              <div class="info-value">{{ formatDateTime(document.created_at) }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>更新时间</label>
              <div class="info-value">{{ formatDateTime(document.updated_at) }}</div>
            </div>
          </el-col>
          <el-col :span="12" v-if="document.completed_at">
            <div class="info-item">
              <label>完成时间</label>
              <div class="info-value">{{ formatDateTime(document.completed_at) }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>耗时</label>
              <div class="info-value">
                <span v-if="document.duration">{{ formatDuration(document.duration) }}</span>
                <span v-else class="text-muted">-</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 错误信息卡片 -->
      <el-card class="info-card error-card" shadow="never" v-if="document.error_message">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon error-icon"><WarningFilled /></el-icon>
            <span>错误信息</span>
          </div>
        </template>
        
        <el-alert
          :title="document.error_message"
          type="error"
          :closable="false"
          show-icon
        />
      </el-card>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Document, 
  DocumentCopy, 
  InfoFilled, 
  Key, 
  Clock, 
  WarningFilled 
} from '@element-plus/icons-vue'
import type { DOCDocument } from '@/types/docDocument'
import { formatDateTime, formatFileSize, formatDuration } from '@/utils/format'
import { copyToClipboard } from '@/utils/clipboard'

const props = defineProps<{
  modelValue: boolean
  document: DOCDocument | null
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  visible.value = false
}

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'COMPLETED': 'success',
    'PROCESSING': 'warning', 
    'FAILED': 'danger',
    'PENDING': 'info',
    'CANCELLED': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'COMPLETED': '已完成',
    'PROCESSING': '处理中',
    'FAILED': '失败',
    'PENDING': '等待中',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const getKBTypeColor = (kbType: string) => {
  return kbType === 'personal' ? '#e7f3ff' : '#f0f9ff'
}

const getKBTypeText = (kbType: string) => {
  return kbType === 'personal' ? '个人库' : '项目库'
}

const getParseTypeColor = (parseType: string) => {
  const colorMap: Record<string, string> = {
    'OCR': '#fef3c7',
    'LAYOUT': '#dbeafe', 
    'TEXT': '#d1fae5'
  }
  return colorMap[parseType] || '#f3f4f6'
}
</script>

<style scoped>
.document-details {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
}

.header-icon {
  font-size: 16px;
  color: #6b7280;
}

.error-card .header-icon.error-icon {
  color: #ef4444;
}

.info-item {
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #374151;
  word-break: break-all;
}

.copy-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.id-text {
  flex: 1;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f9fafb;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.text-muted {
  color: #9ca3af;
}

.kb-type-tag,
.parse-type-tag {
  border: none !important;
}
</style>
