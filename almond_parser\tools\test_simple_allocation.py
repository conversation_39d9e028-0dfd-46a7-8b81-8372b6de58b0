#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
简单的任务分配测试
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

async def test_simple_allocation():
    """简单测试任务分配"""
    try:
        from almond_parser.services.task_allocation_service import task_allocation_service
        
        print("开始测试任务分配...")
        
        # 测试分配逻辑
        stats = await task_allocation_service.allocate_pending_tasks(max_allocations=5)
        
        print(f"分配结果: {stats}")
        
        if "error" in stats:
            print(f"❌ 分配失败: {stats['error']}")
        else:
            print("✅ 分配测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_allocation())
