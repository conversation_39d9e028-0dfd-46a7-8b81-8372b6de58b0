#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试 ARQ cron 修复
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.utils.logger import setup_logger
from loguru import logger


def test_cron_syntax():
    """测试 cron 语法"""
    logger.info("🧪 测试 ARQ cron 语法...")
    
    try:
        from arq import cron
        from almond_parser.tasks import node_tasks
        
        # 测试新的 cron 配置
        test_cron = cron(
            coroutine=node_tasks.health_check_all_nodes,
            minute=1,
            run_at_startup=True,
        )
        
        logger.info("✅ ARQ cron 语法正确")
        logger.info(f"  任务函数: {test_cron.coroutine.__name__}")
        logger.info(f"  执行间隔: 每分钟")
        logger.info(f"  启动时运行: {test_cron.run_at_startup}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ARQ cron 语法错误: {e}")
        return False


def test_worker_settings():
    """测试 WorkerSettings"""
    logger.info("🧪 测试 WorkerSettings...")
    
    try:
        from almond_parser.tasks.arq_app import WorkerSettings
        
        # 检查 cron_jobs
        if hasattr(WorkerSettings, 'cron_jobs'):
            cron_jobs = WorkerSettings.cron_jobs
            logger.info(f"✅ 找到 {len(cron_jobs)} 个定时任务")
            
            for i, cron_job in enumerate(cron_jobs):
                logger.info(f"  任务 {i+1}: {cron_job.coroutine.__name__}")
        else:
            logger.error("❌ WorkerSettings 中没有 cron_jobs")
            return False
        
        # 检查其他配置
        logger.info(f"  Redis 配置: {WorkerSettings.redis_settings.host}:{WorkerSettings.redis_settings.port}")
        logger.info(f"  最大任务数: {WorkerSettings.max_jobs}")
        logger.info(f"  任务超时: {WorkerSettings.job_timeout}s")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ WorkerSettings 测试失败: {e}")
        return False


async def test_task_function():
    """测试任务函数"""
    logger.info("🧪 测试任务函数...")
    
    try:
        from almond_parser.tasks.node_tasks import health_check_all_nodes
        from almond_parser.db import init_database
        
        # 初始化数据库
        await init_database()
        
        # 模拟 ARQ 上下文
        ctx = {"job_id": "test_cron_fix", "job_try": 1}
        
        # 执行任务
        result = await health_check_all_nodes(ctx)
        
        logger.info(f"✅ 任务函数执行成功")
        logger.info(f"  结果: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 任务函数执行失败: {e}")
        return False


def show_restart_instructions():
    """显示重启说明"""
    logger.info("📋 重启 Worker 说明:")
    logger.info("  1. 停止当前 Worker (Ctrl+C)")
    logger.info("  2. 重新启动: python almond_parser/worker.py")
    logger.info("  3. 观察启动日志，应该看到:")
    logger.info("     - 'Starting worker for...'")
    logger.info("     - 'Registering cron job...'")
    logger.info("  4. 等待1分钟，应该看到定时任务执行日志")


async def main():
    """主函数"""
    setup_logger()
    
    logger.info("🔧 ARQ cron 修复测试")
    logger.info("=" * 40)
    
    success = True
    
    # 测试 cron 语法
    if not test_cron_syntax():
        success = False
    
    # 测试 WorkerSettings
    if not test_worker_settings():
        success = False
    
    # 测试任务函数
    if not await test_task_function():
        success = False
    
    if success:
        logger.info("\n🎉 所有测试通过！")
        logger.info("ARQ cron 配置已修复")
        show_restart_instructions()
    else:
        logger.info("\n❌ 测试失败，请检查错误信息")


if __name__ == "__main__":
    asyncio.run(main())
