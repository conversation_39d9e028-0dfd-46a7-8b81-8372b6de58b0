# -*- encoding: utf-8 -*-
"""
DOC数据库连接管理器
"""
import hashlib
from pathlib import Path
from typing import Dict, Optional, List, Any
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool
from loguru import logger

from almond_parser.config import settings, DOCDatabaseConfig


class DOCDatabaseManager:
    """DOC数据库连接管理器"""
    
    def __init__(self):
        self._engines: Dict[str, any] = {}
        self._session_makers: Dict[str, async_sessionmaker] = {}
        self._configs: Dict[str, DOCDatabaseConfig] = {}
        self._initialized = False
        self._config_file_md5: Optional[str] = None  # 配置文件MD5缓存
    
    async def initialize(self):
        """初始化数据库连接"""
        if self._initialized:
            return
        
        try:
            # 加载配置
            configs = settings.load_doc_databases_config()
            
            for config in configs:
                await self._create_engine(config)
                self._configs[config.name] = config
            
            # 计算并缓存配置文件MD5
            self._config_file_md5 = self._calculate_config_file_md5()

            self._initialized = True
            logger.info(f"DOC数据库管理器初始化完成，共加载 {len(configs)} 个数据库配置")

        except Exception as e:
            logger.error(f"DOC数据库管理器初始化失败: {e}")
            raise
    
    async def _create_engine(self, config: DOCDatabaseConfig):
        """创建数据库引擎"""
        try:
            engine = create_async_engine(
                config.database_url,
                poolclass=NullPool,  # 使用NullPool避免连接池问题
                echo=False,
                pool_pre_ping=True,
                pool_recycle=3600,
                connect_args={
                    "charset": config.charset,
                    "autocommit": False
                }
            )
            
            # 创建会话工厂
            session_maker = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            self._engines[config.name] = engine
            self._session_makers[config.name] = session_maker
            
            logger.info(f"数据库引擎创建成功: {config.name} ({config.label})")
            
        except Exception as e:
            logger.error(f"创建数据库引擎失败 {config.name}: {e}")
            raise
    
    async def get_session(self, database_name: str) -> AsyncSession:
        """获取数据库会话"""
        if not self._initialized:
            await self.initialize()
        
        if database_name not in self._session_makers:
            raise ValueError(f"数据库配置不存在: {database_name}")
        
        return self._session_makers[database_name]()
    
    def get_config(self, database_name: str) -> Optional[DOCDatabaseConfig]:
        """获取数据库配置"""
        return self._configs.get(database_name)
    
    def get_all_configs(self) -> List[DOCDatabaseConfig]:
        """获取所有数据库配置"""
        return list(self._configs.values())
    
    def get_server_list(self) -> List[Dict[str, Any]]:
        """获取服务器列表，用于前端显示（支持配置文件动态加载）"""
        # 检查配置文件是否有更新
        self._check_and_reload_config()

        result = []
        for config in self._configs.values():
            server_config = {
                "name": config.name,
                "label": config.label,
                "host": config.host,
                "port": config.port,
                "database": config.database
            }

            # 添加下载配置（如果存在）
            if config.download:
                server_config["download"] = {
                    "base_url": config.download.base_url,
                    "docs_prefix": config.download.docs_prefix,
                    "kb_prefix": config.download.kb_prefix,
                    "kb_path_prefix": config.download.kb_path_prefix
                }

            result.append(server_config)

        return result
    
    async def reload_config(self):
        """重新加载配置"""
        logger.info("重新加载DOC数据库配置...")
        
        # 关闭现有连接
        await self.close_all()
        
        # 清空缓存
        self._engines.clear()
        self._session_makers.clear()
        self._configs.clear()
        self._initialized = False
        
        # 重新初始化
        await self.initialize()

    def _calculate_config_file_md5(self) -> Optional[str]:
        """计算配置文件的MD5值"""
        try:
            config_file = Path(settings.DOC_DATABASES_CONFIG_FILE)
            if not config_file.exists():
                return None

            with open(config_file, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()

        except Exception as e:
            logger.error(f"计算配置文件MD5失败: {e}")
            return None

    def _check_and_reload_config(self):
        """检查配置文件是否有更新，如果有则自动重新加载"""
        try:
            current_md5 = self._calculate_config_file_md5()

            # 如果MD5值发生变化，说明配置文件被修改了
            if current_md5 and current_md5 != self._config_file_md5:
                logger.info(f"检测到配置文件变化，自动重新加载配置 (MD5: {self._config_file_md5} -> {current_md5})")

                # 同步重新加载配置（简化处理，避免异步复杂性）
                self._reload_config_sync()

        except Exception as e:
            logger.error(f"检查配置文件更新失败: {e}")

    def _reload_config_sync(self):
        """同步重新加载配置（用于动态更新）"""
        try:
            # 清空现有配置
            self._configs.clear()

            # 重新加载配置
            configs = settings.load_doc_databases_config()
            for config in configs:
                self._configs[config.name] = config

            # 更新MD5缓存
            self._config_file_md5 = self._calculate_config_file_md5()

            logger.info(f"配置文件动态重新加载完成，共加载 {len(configs)} 个数据库配置")

        except Exception as e:
            logger.error(f"同步重新加载配置失败: {e}")
    
    async def close_all(self):
        """关闭所有数据库连接"""
        for name, engine in self._engines.items():
            try:
                await engine.dispose()
                logger.info(f"数据库连接已关闭: {name}")
            except Exception as e:
                logger.error(f"关闭数据库连接失败 {name}: {e}")
    
    async def test_connection(self, database_name: str) -> bool:
        """测试数据库连接"""
        try:
            async with await self.get_session(database_name) as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败 {database_name}: {e}")
            return False


# 全局DOC数据库管理器实例
doc_db_manager = DOCDatabaseManager()


async def get_doc_db_session(database_name: str) -> AsyncSession:
    """获取DOC数据库会话的依赖注入函数"""
    return await doc_db_manager.get_session(database_name)


async def init_doc_databases():
    """初始化DOC数据库连接"""
    await doc_db_manager.initialize()


async def close_doc_databases():
    """关闭DOC数据库连接"""
    await doc_db_manager.close_all()
