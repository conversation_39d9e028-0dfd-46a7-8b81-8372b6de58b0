#!/usr/bin/env python3
"""
测试 sglang_manager 修复
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_import():
    """测试导入"""
    try:
        from services.sglang_manager import SglangManager
        print("✅ sglang_manager 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_gpu_detection():
    """测试 GPU 检测"""
    try:
        from services.sglang_manager import SglangManager
        manager = SglangManager()
        
        # 测试 GPU 检测
        available_gpus = manager.get_available_gpus()
        print(f"✅ GPU 检测成功: {available_gpus}")
        
        # 测试设备选择
        selected_devices = manager.select_cuda_devices()
        print(f"✅ 设备选择成功: {selected_devices}")
        
        return True
    except Exception as e:
        print(f"❌ GPU 检测失败: {e}")
        return False

def test_command_generation():
    """测试命令生成"""
    try:
        from services.sglang_manager import SglangManager
        import os
        
        manager = SglangManager()
        
        # 模拟命令生成过程
        cuda_devices = manager.select_cuda_devices()
        cmd = [
            "mineru-sglang-server",
            "--port", str(manager.port),
            "--host", manager.host
        ]
        
        env = os.environ.copy()
        if cuda_devices:
            env["CUDA_VISIBLE_DEVICES"] = cuda_devices
        
        print(f"✅ 命令生成成功: {' '.join(cmd)}")
        print(f"✅ 环境变量: CUDA_VISIBLE_DEVICES={env.get('CUDA_VISIBLE_DEVICES', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"❌ 命令生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试 sglang_manager 修复")
    print("=" * 40)
    
    success = True
    
    # 测试导入
    if not test_import():
        success = False
    
    # 测试 GPU 检测
    if not test_gpu_detection():
        success = False
    
    # 测试命令生成
    if not test_command_generation():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！")
        print("修复成功，现在可以正常使用 start_server.py 启动服务了")
    else:
        print("\n❌ 测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
