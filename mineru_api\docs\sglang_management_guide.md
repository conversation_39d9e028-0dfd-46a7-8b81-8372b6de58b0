# sglang 服务管理增强方案

## 🎯 解决的问题

### 核心问题
1. **sglang 服务僵死**：端口还在监听但不响应请求
2. **进程过多**：fork 出超过30个进程，难以管理
3. **手动清理困难**：需要复杂的命令行操作才能彻底清理
4. **缺乏监控**：无法及时发现服务异常

### 解决方案
提供完整的 sglang 服务管理和监控系统，包括：
- 自动健康检查和恢复
- 强制进程清理功能
- 便捷的管理 API
- 详细的状态监控

## 🛠️ 新增组件

### 1. sglang 健康监控器 (`sglang_health_monitor.py`)

#### 核心功能
- **健康检查**：定期检查服务响应状态
- **进程监控**：跟踪所有相关进程的状态
- **自动恢复**：连续失败后自动重启服务
- **强制清理**：彻底杀死所有相关进程

#### 监控指标
- HTTP 响应状态和时间
- 进程数量和资源使用
- 端口监听状态
- 连续失败次数

### 2. 增强的 sglang 管理器 (`sglang_manager.py`)

#### 新增功能
- **强制停止**：`stop_server(force_kill=True)`
- **强制重启**：`force_restart_server()`
- **健康状态**：`get_health_status()`

### 3. sglang 管理 API (`sglang_management.py`)

#### 提供的接口
```
GET  /sglang/status              # 获取基本状态
GET  /sglang/health              # 获取详细健康状态
GET  /sglang/processes           # 列出所有相关进程
GET  /sglang/recommendations     # 获取优化建议

POST /sglang/start               # 启动服务
POST /sglang/stop                # 停止服务
POST /sglang/restart             # 重启服务
POST /sglang/force-restart       # 强制重启（推荐）
POST /sglang/kill-all-processes  # 杀死所有进程
```

### 4. 定时健康检查 (`sglang_health_task.py`)

#### 定时任务
- **健康检查**：每30秒检查一次服务状态
- **进程清理**：每小时检查是否需要清理进程
- **启动检查**：服务启动时自动检查和启动 sglang

## 📋 使用方法

### 1. 基本状态查询

```bash
# 查看基本状态
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/status

# 查看详细健康状态
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/health

# 查看所有进程
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/processes
```

### 2. 服务管理

```bash
# 启动服务
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/start

# 普通重启
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/restart

# 强制重启（推荐，解决僵死问题）
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/force-restart

# 只杀死进程不重启（清理用）
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/kill-all-processes
```

### 3. Python 代码示例

```python
import httpx

# 检查服务状态
async def check_sglang_status():
    async with httpx.AsyncClient() as client:
        response = await client.get(
            "http://localhost:8000/sglang/health",
            headers={"Authorization": "Bearer your-api-key"}
        )
        return response.json()

# 强制重启服务（解决僵死问题）
async def force_restart_sglang():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/sglang/force-restart",
            headers={"Authorization": "Bearer your-api-key"}
        )
        return response.json()
```

## 🔧 自动化功能

### 1. 自动健康检查
- 每30秒检查一次服务健康状态
- 连续失败3次后自动触发重启
- 最小重启间隔5分钟（避免频繁重启）

### 2. 自动进程清理
- 每小时检查进程状态
- 进程数超过20个时自动清理
- 内存使用超过8GB时自动清理
- 服务不健康且进程较多时自动清理

### 3. 启动时检查
- 服务启动时自动检查 sglang 状态
- 如果未运行则自动启动
- 如果运行但不健康则记录警告

## 🚨 故障处理

### 场景1：sglang 服务僵死
**症状**：端口30000还在监听，但不响应请求

**解决方案**：
```bash
# 方法1：通过 API 强制重启
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/force-restart

# 方法2：只清理进程
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/kill-all-processes
```

### 场景2：进程过多（超过30个）
**症状**：`ss -tunlp | grep :30000` 显示很多进程

**解决方案**：
```bash
# 自动检测并清理
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/force-restart
```

### 场景3：完全无响应
**症状**：API 调用也失败

**解决方案**：
```bash
# 使用原来的命令行方法作为备用
ps aux | grep mineru-sglang-s | grep -v grep | awk '{print $2}' | xargs kill -9

# 然后通过 API 重新启动
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/start
```

## 📊 监控指标

### 健康状态指标
```json
{
  "health_check": {
    "healthy": true,
    "response_time": 0.15,
    "process_info": {
      "total_processes": 8,
      "memory_usage": 2048.5,
      "cpu_usage": 15.2
    }
  },
  "monitor_info": {
    "consecutive_failures": 0,
    "last_health_check": "2024-01-01T12:00:00",
    "last_restart_time": null
  },
  "recommendations": []
}
```

### 进程信息
```json
{
  "main_process": {
    "pid": 12345,
    "memory_mb": 1024.5,
    "cpu_percent": 10.2,
    "create_time": **********
  },
  "child_processes": [
    {
      "pid": 12346,
      "ppid": 12345,
      "memory_mb": 256.3,
      "cpu_percent": 2.1
    }
  ],
  "total_processes": 8
}
```

## ⚙️ 配置选项

### 健康检查配置
```python
# 在 sglang_health_monitor.py 中可调整
health_check_interval = 30          # 检查间隔（秒）
max_consecutive_failures = 3        # 最大连续失败次数
min_restart_interval = 300          # 最小重启间隔（秒）
```

### 进程清理阈值
```python
# 在 sglang_health_task.py 中可调整
max_processes = 20                  # 最大进程数
max_memory_mb = 8000               # 最大内存使用（MB）
```

## 🔮 使用建议

### 1. 日常监控
```bash
# 定期检查服务状态
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/health | jq '.recommendations'
```

### 2. 预防性维护
```bash
# 每天强制重启一次（可选）
curl -X POST -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/force-restart
```

### 3. 故障排查
```bash
# 查看详细进程信息
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/processes

# 查看优化建议
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/sglang/recommendations
```

## 📁 文件结构

```
mineru_api/
├── services/
│   ├── sglang_health_monitor.py    # 健康监控器（新增）
│   └── sglang_manager.py           # 管理器（已增强）
├── api/
│   └── sglang_management.py        # 管理API（新增）
├── tasks/
│   └── sglang_health_task.py       # 定时任务（新增）
└── docs/
    └── sglang_management_guide.md  # 本文档（新增）
```

## ✅ 优势特点

1. **彻底解决僵死问题** - 强制进程清理确保彻底重启
2. **自动化监控** - 无需人工干预，自动检测和恢复
3. **便捷的API** - 通过HTTP接口轻松管理服务
4. **详细的状态信息** - 全面了解服务和进程状态
5. **智能建议** - 根据状态提供优化建议
6. **向后兼容** - 不影响现有功能，可随时禁用

---

**总结**：这个方案完美解决了 sglang 服务的所有问题，提供了从监控到管理的完整解决方案。推荐使用 `/sglang/force-restart` 接口来解决服务僵死问题。
