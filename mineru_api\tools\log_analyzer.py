"""
日志分析工具 - 用于分析和查询结构化日志
"""
import json
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import argparse
from collections import defaultdict, Counter


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_file: Path):
        self.log_file = log_file
    
    def parse_log_line(self, line: str) -> Optional[Dict]:
        """解析日志行"""
        try:
            # 查找结构化日志
            if "TASK_EVENT:" in line:
                json_part = line.split("TASK_EVENT:", 1)[1].strip()
                return {"type": "event", "data": json.loads(json_part)}
            elif "TASK_ERROR:" in line:
                json_part = line.split("TASK_ERROR:", 1)[1].strip()
                return {"type": "error", "data": json.loads(json_part)}
            elif "TASK_PERFORMANCE:" in line:
                json_part = line.split("TASK_PERFORMANCE:", 1)[1].strip()
                return {"type": "performance", "data": json.loads(json_part)}
            return None
        except (json.JSONDecodeError, IndexError):
            return None
    
    def read_logs(self, start_time: Optional[datetime] = None, 
                  end_time: Optional[datetime] = None) -> List[Dict]:
        """读取日志"""
        logs = []
        
        if not self.log_file.exists():
            return logs
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line in f:
                parsed = self.parse_log_line(line)
                if parsed:
                    log_time = datetime.fromisoformat(parsed["data"]["timestamp"])
                    
                    if start_time and log_time < start_time:
                        continue
                    if end_time and log_time > end_time:
                        continue
                    
                    logs.append(parsed)
        
        return logs
    
    def analyze_task_performance(self, task_id: str = None) -> Dict[str, Any]:
        """分析任务性能"""
        logs = self.read_logs()
        performance_logs = [log for log in logs if log["type"] == "performance"]
        
        if task_id:
            performance_logs = [log for log in performance_logs 
                              if log["data"]["task_id"] == task_id]
        
        if not performance_logs:
            return {"message": "没有找到性能数据"}
        
        # 按操作类型分组
        by_operation = defaultdict(list)
        for log in performance_logs:
            operation = log["data"]["operation"]
            duration = log["data"]["duration_seconds"]
            by_operation[operation].append(duration)
        
        # 统计分析
        stats = {}
        for operation, durations in by_operation.items():
            stats[operation] = {
                "count": len(durations),
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "total_duration": sum(durations)
            }
        
        return stats
    
    def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析错误模式"""
        logs = self.read_logs()
        error_logs = [log for log in logs if log["type"] == "error"]
        
        if not error_logs:
            return {"message": "没有找到错误日志"}
        
        # 错误类型统计
        error_types = Counter(log["data"]["error_type"] for log in error_logs)
        
        # 错误消息模式
        error_messages = [log["data"]["error_message"] for log in error_logs]
        
        # 按时间分组的错误
        errors_by_hour = defaultdict(int)
        for log in error_logs:
            hour = datetime.fromisoformat(log["data"]["timestamp"]).strftime("%Y-%m-%d %H:00")
            errors_by_hour[hour] += 1
        
        return {
            "total_errors": len(error_logs),
            "error_types": dict(error_types),
            "errors_by_hour": dict(errors_by_hour),
            "recent_errors": error_messages[-10:]  # 最近10个错误
        }
    
    def trace_task(self, task_id: str) -> List[Dict]:
        """追踪特定任务的完整生命周期"""
        logs = self.read_logs()
        task_logs = [log for log in logs if log["data"].get("task_id") == task_id]
        
        # 按时间排序
        task_logs.sort(key=lambda x: x["data"]["timestamp"])
        
        return task_logs
    
    def generate_report(self, days: int = 7) -> Dict[str, Any]:
        """生成分析报告"""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        logs = self.read_logs(start_time, end_time)
        
        # 基本统计
        total_logs = len(logs)
        event_logs = [log for log in logs if log["type"] == "event"]
        error_logs = [log for log in logs if log["type"] == "error"]
        performance_logs = [log for log in logs if log["type"] == "performance"]
        
        # 任务统计
        task_events = defaultdict(list)
        for log in event_logs:
            task_id = log["data"]["task_id"]
            task_events[task_id].append(log["data"]["event"])
        
        completed_tasks = sum(1 for events in task_events.values() 
                            if "ocr_processing_completed" in events)
        failed_tasks = len([log for log in error_logs])
        
        # 性能统计
        performance_stats = self.analyze_task_performance()
        error_analysis = self.analyze_error_patterns()
        
        return {
            "period": f"{days} days",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "summary": {
                "total_logs": total_logs,
                "total_tasks": len(task_events),
                "completed_tasks": completed_tasks,
                "failed_tasks": failed_tasks,
                "success_rate": completed_tasks / len(task_events) * 100 if task_events else 0
            },
            "performance": performance_stats,
            "errors": error_analysis
        }


def main():
    """命令行工具"""
    parser = argparse.ArgumentParser(description="MineruAPI 日志分析工具")
    parser.add_argument("--log-file", type=str, default="../logs/mineru_api.log",
                       help="日志文件路径")
    parser.add_argument("--task-id", type=str, help="追踪特定任务")
    parser.add_argument("--report", action="store_true", help="生成分析报告")
    parser.add_argument("--days", type=int, default=7, help="分析天数")
    parser.add_argument("--performance", action="store_true", help="性能分析")
    parser.add_argument("--errors", action="store_true", help="错误分析")
    
    args = parser.parse_args()
    
    log_file = Path(args.log_file)
    analyzer = LogAnalyzer(log_file)
    
    if args.task_id:
        # 追踪特定任务
        task_logs = analyzer.trace_task(args.task_id)
        print(f"任务 {args.task_id} 的完整日志:")
        for log in task_logs:
            print(json.dumps(log, indent=2, ensure_ascii=False))
    
    elif args.report:
        # 生成报告
        report = analyzer.generate_report(args.days)
        print("分析报告:")
        print(json.dumps(report, indent=2, ensure_ascii=False))
    
    elif args.performance:
        # 性能分析
        stats = analyzer.analyze_task_performance()
        print("性能分析:")
        print(json.dumps(stats, indent=2, ensure_ascii=False))
    
    elif args.errors:
        # 错误分析
        errors = analyzer.analyze_error_patterns()
        print("错误分析:")
        print(json.dumps(errors, indent=2, ensure_ascii=False))
    
    else:
        print("请指定分析类型，使用 --help 查看帮助")


if __name__ == "__main__":
    main()
