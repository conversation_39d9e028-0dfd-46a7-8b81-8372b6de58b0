# 智能僵尸任务清理机制

## 🎯 设计目标

替换原有的固定超时清理机制，实现基于文档大小和类型的智能超时判断，避免误杀正常运行的大文件任务。

## 🧠 核心原理

### 智能超时计算
根据文档特征动态计算合理的超时时间：

```python
超时时间 = 基础时间 + (文件大小MB × 大小系数) × 解析模式系数
```

### 超时参数表

| 服务类型 | 基础时间 | 大小系数 | 最小超时 | 最大超时 |
|---------|---------|---------|---------|---------|
| 文档解读 | 10分钟 | 0.5分钟/MB | 5分钟 | 60分钟 |
| 知识库 | 30分钟 | 2.0分钟/MB | 15分钟 | 180分钟 |
| 通用 | 15分钟 | 1.0分钟/MB | 8分钟 | 90分钟 |

### 解析模式系数
- `pipeline`: 1.0x (标准模式)
- `sglang`: 1.5x (VLM模式更慢)
- `auto`: 1.2x (自动模式稍慢)

## 📊 超时时间示例

| 文件大小 | 文件类型 | 解析模式 | 计算超时时间 | 说明 |
|---------|---------|---------|-------------|------|
| 1MB | PDF | pipeline | 10.5分钟 | 小文档快速处理 |
| 10MB | PDF | pipeline | 15分钟 | 中等文档 |
| 10MB | PDF | sglang | 22.5分钟 | VLM模式更慢 |
| 50MB | PDF | pipeline | 35分钟 | 大文档 |
| 100MB | ZIP | pipeline | 230分钟 | 大知识库 |
| 500MB | ZIP | auto | 180分钟 | 超大文件受最大限制 |

## 🔧 实现细节

### 1. 智能清理函数

```python
async def smart_cleanup_zombie_tasks_cron(ctx):
    """智能僵尸任务清理 - 基于文档大小的动态超时判断"""
    
    # 1. 查找所有PARSING状态的任务
    parsing_tasks = await db.execute(
        select(Document).where(
            and_(
                Document.status == DocumentStatus.PARSING,
                Document.started_at.isnot(None),  # 必须有开始时间
                Document.file_size.isnot(None),   # 必须有文件大小
                Document.node_id.isnot(None)      # 必须占用了节点
            )
        )
    )
    
    # 2. 对每个任务进行智能超时判断
    for task in tasks:
        # 推断服务类型
        service_type = _infer_service_type_from_file(task.file_type)
        
        # 计算智能超时阈值
        timeout_minutes = TimeoutCalculator.calculate_timeout_minutes(
            service_type, task.file_size, task.current_parse_mode or "auto"
        )
        
        # 计算实际运行时间
        running_minutes = (datetime.now() - task.started_at).total_seconds() / 60
        
        # 判断是否超时
        if running_minutes > timeout_minutes:
            await _smart_cleanup_single_task(db, task, timeout_minutes, running_minutes)
```

### 2. 服务类型推断

```python
def _infer_service_type_from_file(file_type: str) -> ServiceType:
    """根据文件类型推断服务类型"""
    if file_type in ["pdf", "doc", "docx", "ppt", "pptx"]:
        return ServiceType.DOCUMENT
    elif file_type in ["zip", "rar", "tar", "gz"]:
        return ServiceType.KNOWLEDGE_BASE
    else:
        return ServiceType.UNIVERSAL
```

### 3. 清理操作

```python
async def _smart_cleanup_single_task(db, task, timeout_minutes, running_minutes):
    """智能清理单个超时任务"""
    
    # 1. 释放节点资源
    await node_concurrency_manager.release_task_slot(...)
    
    # 2. 更新任务状态
    task.retry_count += 1
    if task.retry_count >= task.max_retries:
        task.status = DocumentStatus.FAILED
    else:
        task.status = DocumentStatus.RETRY_PENDING
    
    # 3. 记录详细的清理日志
    await doc_service.add_document_log(
        task.document_id, "WARNING",
        f"智能超时清理：{file_size_mb:.1f}MB文件运行{running_minutes:.1f}分钟超过阈值{timeout_minutes}分钟"
    )
```

## 🚀 优势对比

### 原有机制 vs 智能机制

| 特性 | 原有机制 | 智能机制 |
|------|---------|---------|
| 超时判断 | 固定30分钟 | 基于文件大小动态计算 |
| 文件区分 | 无区分 | 区分文档/知识库/通用 |
| 解析模式 | 不考虑 | 考虑VLM模式更慢 |
| 误杀风险 | 高（大文件容易误杀） | 低（智能判断） |
| 资源利用 | 低（小文件等待过久） | 高（小文件快速超时） |
| 日志信息 | 简单 | 详细（包含计算过程） |

## 📈 监控指标

### 关键日志
- `"智能超时清理"`: 任务被清理
- `"任务正常运行"`: 任务在合理时间内
- `"运行X分钟 > 阈值Y分钟"`: 具体超时信息

### 监控建议
1. **清理频率**: 监控每5分钟的清理任务数量
2. **超时分布**: 统计不同文件大小的超时情况
3. **误杀率**: 检查是否有正常任务被误清理
4. **资源利用**: 观察节点资源释放效率

## 🧪 测试验证

使用测试脚本验证：
```bash
python test_smart_zombie_cleanup.py
```

### 测试内容
1. **超时计算测试**: 验证不同文件大小的超时时间计算
2. **清理逻辑测试**: 模拟执行智能清理流程
3. **超时场景测试**: 检查实际任务的超时判断

## 🔧 配置调优

### 调整超时参数
如需调整超时计算参数，修改 `timeout_calculator.py`:

```python
# 基础超时时间（分钟）
BASE_TIMEOUT = {
    ServiceType.DOCUMENT: 10,        # 可调整
    ServiceType.KNOWLEDGE_BASE: 30,  # 可调整
    ServiceType.UNIVERSAL: 15,       # 可调整
}

# 文件大小系数（每MB增加的分钟数）
SIZE_FACTOR = {
    ServiceType.DOCUMENT: 0.5,       # 可调整
    ServiceType.KNOWLEDGE_BASE: 2.0, # 可调整
    ServiceType.UNIVERSAL: 1.0,      # 可调整
}
```

### 调整清理频率
修改 `arq_app.py` 中的定时任务配置：

```python
# 每5分钟清理（可调整为其他间隔）
cron(
    coroutine=smart_cleanup_zombie_tasks_cron,
    minute=interval_set(5),  # 可改为其他值
    run_at_startup=True,
),
```

## 🚨 注意事项

1. **生产环境部署**: 建议先在测试环境验证超时参数合理性
2. **监控告警**: 设置清理频率异常告警
3. **日志保留**: 保留详细的清理日志用于问题排查
4. **回滚准备**: 保留原有清理机制代码以备回滚
