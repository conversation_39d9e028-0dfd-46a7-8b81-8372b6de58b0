<template>
  <div class="parse-tasks">
    <div class="header">
      <h2>解析任务管理</h2>
      <el-button type="primary" @click="showUploadDialog = true">
        <el-icon><Upload /></el-icon>
        上传文档
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.total_tasks }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-number">{{ stats.pending_tasks }}</div>
              <div class="stat-label">等待中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card processing">
            <div class="stat-content">
              <div class="stat-number">{{ stats.processing_tasks }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card completed">
            <div class="stat-content">
              <div class="stat-number">{{ stats.completed_tasks }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card failed">
            <div class="stat-content">
              <div class="stat-number">{{ stats.failed_tasks }}</div>
              <div class="stat-label">失败</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ stats.success_rate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 过滤器 -->
    <div class="filters">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="filters.status" placeholder="任务状态" clearable>
            <el-option label="等待中" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
            <el-option label="重试中" value="retry" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-input v-model="filters.filename" placeholder="文件名搜索" clearable />
        </el-col>
        <el-col :span="6">
          <el-button @click="loadTasks">刷新</el-button>
          <el-button @click="autoRefresh = !autoRefresh" :type="autoRefresh ? 'success' : 'default'">
            {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 任务列表 -->
    <el-table :data="filteredTasks" v-loading="loading" stripe>
      <el-table-column prop="task_id" label="任务ID" width="120">
        <template #default="{ row }">
          <el-tooltip :content="row.task_id" placement="top">
            <span>{{ row.task_id.substring(0, 8) }}...</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="filename" label="文件名" width="200" />
      <el-table-column prop="file_size" label="文件大小" width="100">
        <template #default="{ row }">
          {{ formatFileSize(row.file_size) }}
        </template>
      </el-table-column>
      <el-table-column prop="parse_mode" label="解析模式" width="100">
        <template #default="{ row }">
          <el-tag :type="parseModeTagType(row.parse_mode)">
            {{ parseModeText(row.parse_mode) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="statusTagType(row.status)">
            {{ statusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="progress" label="进度" width="120">
        <template #default="{ row }">
          <el-progress
            v-if="row.status === 'processing' && row.progress !== null"
            :percentage="row.progress"
            :stroke-width="6"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="retry_count" label="重试次数" width="100" />
      <el-table-column prop="created_at" label="创建时间" width="150">
        <template #default="{ row }">
          {{ formatTime(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="completed_at" label="完成时间" width="150">
        <template #default="{ row }">
          {{ formatTime(row.completed_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="viewTask(row)">详情</el-button>
          <el-button 
            size="small" 
            @click="downloadResult(row)" 
            :disabled="row.status !== 'completed'"
          >
            下载
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="deleteTask(row)"
            :disabled="row.status === 'processing'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadTasks"
        @current-change="loadTasks"
      />
    </div>

    <!-- 上传文档对话框 -->
    <el-dialog title="上传文档" v-model="showUploadDialog" width="500px">
      <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef" label-width="100px">
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :before-remove="handleFileRemove"
            accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word、PowerPoint、Excel 文件，最大 100MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="服务类型" prop="service_type">
          <el-select v-model="uploadForm.service_type" placeholder="请选择服务类型">
            <el-option label="自动选择" value="auto" />
            <el-option label="文档解析" value="document" />
            <el-option label="知识库解析" value="knowledge_base" />
          </el-select>
        </el-form-item>
        <el-form-item label="解析模式" prop="parse_mode">
          <el-select v-model="uploadForm.parse_mode" placeholder="请选择解析模式">
            <el-option label="自动选择" value="auto" />
            <el-option label="Pipeline" value="pipeline" />
            <el-option label="SGLang" value="sglang" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务优先级" prop="priority">
          <el-input-number v-model="uploadForm.priority" :min="1" :max="10" />
        </el-form-item>
        <el-form-item label="最大重试" prop="max_retries">
          <el-input-number v-model="uploadForm.max_retries" :min="0" :max="5" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploading">
          上传并解析
        </el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" v-model="showDetailDialog" width="800px">
      <div v-if="selectedTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.task_id }}</el-descriptions-item>
          <el-descriptions-item label="文件名">{{ selectedTask.filename }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(selectedTask.file_size) }}</el-descriptions-item>
          <el-descriptions-item label="文件类型">{{ selectedTask.file_type }}</el-descriptions-item>
          <el-descriptions-item label="解析模式">{{ parseModeText(selectedTask.parse_mode) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="statusTagType(selectedTask.status)">
              {{ statusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">
            {{ selectedTask.progress !== null ? selectedTask.progress + '%' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="重试次数">{{ selectedTask.retry_count }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(selectedTask.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatTime(selectedTask.started_at) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatTime(selectedTask.completed_at) }}</el-descriptions-item>
          <el-descriptions-item label="执行节点">{{ selectedTask.node_id || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedTask.error_message" style="margin-top: 20px;">
          <h4>错误信息：</h4>
          <el-alert :title="selectedTask.error_message" type="error" :closable="false" />
        </div>
        
        <div v-if="selectedTask.result_data" style="margin-top: 20px;">
          <h4>解析结果：</h4>
          <el-input
            type="textarea"
            :rows="10"
            :value="JSON.stringify(selectedTask.result_data, null, 2)"
            readonly
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { parseTaskApi } from '@/api/parseTask'
import type { ParseTask, ParseTaskStats } from '@/types/mineruNode'

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const autoRefresh = ref(false)
const showUploadDialog = ref(false)
const showDetailDialog = ref(false)
const uploadFormRef = ref()
const uploadRef = ref()
const selectedTask = ref<ParseTask | null>(null)

const tasks = ref<ParseTask[]>([])
const stats = ref<ParseTaskStats>({
  total_tasks: 0,
  pending_tasks: 0,
  processing_tasks: 0,
  completed_tasks: 0,
  failed_tasks: 0,
  success_rate: 0
})

const filters = reactive({
  status: '',
  filename: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const uploadForm = reactive({
  file: null as File | null,
  service_type: 'auto',
  parse_mode: 'auto',
  priority: 1,
  max_retries: 2
})

const uploadRules = {
  file: [{ required: true, message: '请选择文件', trigger: 'change' }],
  service_type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  parse_mode: [{ required: true, message: '请选择解析模式', trigger: 'change' }],
  priority: [{ required: true, message: '请设置任务优先级', trigger: 'change' }]
}

let refreshTimer: number | null = null

// 计算属性
const filteredTasks = computed(() => {
  let result = tasks.value
  
  if (filters.status) {
    result = result.filter(task => task.status === filters.status)
  }
  
  if (filters.filename) {
    result = result.filter(task => 
      task.filename.toLowerCase().includes(filters.filename.toLowerCase())
    )
  }
  
  return result
})

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    const response = await parseTaskApi.getTasks({
      skip: (pagination.page - 1) * pagination.pageSize,
      limit: pagination.pageSize,
      status: filters.status || undefined
    })
    tasks.value = response
    pagination.total = response.length
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    stats.value = await parseTaskApi.getStats()
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 其他方法...
const handleFileChange = (file: any) => {
  uploadForm.file = file.raw
}

const handleFileRemove = () => {
  uploadForm.file = null
}

const submitUpload = async () => {
  if (!uploadFormRef.value || !uploadForm.file) return

  try {
    await uploadFormRef.value.validate()
    uploading.value = true

    await parseTaskApi.uploadFile(
      uploadForm.file,
      uploadForm.parse_mode,
      uploadForm.service_type,
      uploadForm.priority,
      uploadForm.max_retries
    )

    ElMessage.success('文件上传成功，开始解析')
    showUploadDialog.value = false
    resetUploadForm()
    await loadTasks()
    await loadStats()
  } catch (error) {
    ElMessage.error('文件上传失败')
  } finally {
    uploading.value = false
  }
}

const viewTask = (task: ParseTask) => {
  selectedTask.value = task
  showDetailDialog.value = true
}

const downloadResult = async (task: ParseTask) => {
  try {
    const result = await parseTaskApi.getTaskResult(task.task_id)
    ElMessage.success('结果获取成功')
    console.log('Task result:', result)
  } catch (error) {
    ElMessage.error('获取结果失败')
  }
}

const deleteTask = async (task: ParseTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.filename}" 吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    await parseTaskApi.deleteTask(task.task_id)
    ElMessage.success('任务删除成功')
    await loadTasks()
    await loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('任务删除失败')
    }
  }
}

const resetUploadForm = () => {
  uploadForm.file = null
  uploadForm.service_type = 'auto'
  uploadForm.parse_mode = 'auto'
  uploadForm.priority = 1
  uploadForm.max_retries = 2
  uploadRef.value?.clearFiles()
}

const startAutoRefresh = () => {
  if (refreshTimer) return
  refreshTimer = window.setInterval(() => {
    loadTasks()
    loadStats()
  }, 5000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 辅助方法
const statusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    processing: 'warning',
    completed: 'success',
    failed: 'danger',
    retry: 'warning'
  }
  return types[status] || 'info'
}

const statusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '等待中',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    retry: '重试中'
  }
  return texts[status] || status
}

const parseModeTagType = (mode: string) => {
  const types: Record<string, string> = {
    pipeline: 'primary',
    sglang: 'success',
    auto: 'warning'
  }
  return types[mode] || 'info'
}

const parseModeText = (mode: string) => {
  const texts: Record<string, string> = {
    pipeline: 'Pipeline',
    sglang: 'SGLang',
    auto: '自动'
  }
  return texts[mode] || mode
}

const formatTime = (time: string | null) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
}

// 监听自动刷新状态
watch(() => autoRefresh.value, (newVal) => {
  if (newVal) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadTasks()
  loadStats()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.parse-tasks {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-card.pending {
  border-left: 4px solid #909399;
}

.stat-card.processing {
  border-left: 4px solid #e6a23c;
}

.stat-card.completed {
  border-left: 4px solid #67c23a;
}

.stat-card.failed {
  border-left: 4px solid #f56c6c;
}

.stat-card.success {
  border-left: 4px solid #409eff;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
