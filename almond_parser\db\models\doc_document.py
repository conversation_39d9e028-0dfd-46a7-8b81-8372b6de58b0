# -*- encoding: utf-8 -*-
"""
DOC文档数据模型
"""
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base

# 创建独立的Base，用于DOC数据库表
DOCBase = declarative_base()


class ParseTask(DOCBase):
    """解析任务表 (对应 parse_tasks 表)"""
    __tablename__ = "parse_tasks"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), nullable=False, comment="用户名")
    file_name = Column(String(100), nullable=False, comment="文件名")
    file_path = Column(Text, nullable=False, comment="文件路径")
    doc_id = Column(String(100), nullable=True, comment="文档ID")
    batch_id = Column(String(100), nullable=False, comment="批次ID")
    almond_parser_id = Column(String(100), nullable=False, comment="杏仁解析器ID")
    status = Column(String(20), nullable=True, comment="解析状态")
    maxkb_url = Column(Text, nullable=True, comment="maxkb url")
    maxkb_id = Column(String(100), nullable=True, comment="maxkb id")
    error_message = Column(Text, nullable=True, comment="错误消息")
    created_at = Column(DateTime, nullable=True, comment="创建时间")
    updated_at = Column(DateTime, nullable=True, comment="更新时间")
    session_id = Column(String(100), nullable=True, comment="会话ID")
    task_type = Column(String(20), nullable=True, comment="任务类型")
    kb_type = Column(String(20), default="personal", comment="知识库类型: personal | project")
    file_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    file_type = Column(String(20), nullable=True, comment="文件类型（扩展名）")
    parsed_content_path = Column(Text, nullable=True, comment="解析内容存储路径")


class DocReaderTask(DOCBase):
    """文档解读任务表 (对应 doc_reader_tasks 表)"""
    __tablename__ = "doc_reader_tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), nullable=False, unique=True, comment="任务ID")
    username = Column(String(100), nullable=False, comment="用户名")
    session_id = Column(String(100), nullable=False, comment="会话ID")
    doc_id = Column(String(100), nullable=False, comment="文档ID（MongoDB）")
    batch_id = Column(String(100), nullable=False, comment="批次ID")
    filename = Column(String(255), nullable=False, comment="文件名")
    file_type = Column(String(50), nullable=False, comment="文件类型")
    parse_type = Column(String(20), nullable=False, comment="解析类型")
    parser_doc_id = Column(String(100), nullable=True, comment="杏仁解析器返回的文档ID（仅OCR类型）")
    almond_batch_id = Column(String(100), nullable=True, comment="杏仁解析器批次ID（仅OCR类型）")
    status = Column(String(20), nullable=True, comment="任务状态")
    chunks_count = Column(Integer, nullable=True, comment="分块数量")
    error_message = Column(Text, nullable=True, comment="错误消息")
    meta_info = Column(JSON, nullable=True, comment="任务元数据")
    created_at = Column(DateTime, nullable=True, comment="创建时间")
    updated_at = Column(DateTime, nullable=True, comment="更新时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    retry_count = Column(Integer, default=0, comment="重试次数")


def create_doc_document_from_parse_task(task: ParseTask) -> Dict[str, Any]:
    """将ParseTask转换为统一的DOC文档格式"""
    duration = None
    if task.created_at and task.updated_at and task.updated_at > task.created_at:
        duration = int((task.updated_at - task.created_at).total_seconds())
        # 如果耗时异常大（超过1天），设为None
        if duration > 86400:  # 86400秒 = 1天
            duration = None

    return {
        "id": task.id,
        "document_id": task.doc_id or f"parse_{task.id}",
        "batch_id": task.batch_id,
        "username": task.username,
        "file_name": task.file_name,
        "file_path": task.file_path,
        "file_type": task.file_type,
        "file_size": task.file_size,
        "status": map_parse_task_status(task.status),
        "kb_type": task.kb_type,
        "parse_type": None,  # 解析类型放到详情中
        "error_message": task.error_message,
        "created_at": task.created_at.strftime("%Y-%m-%d %H:%M:%S") if task.created_at else None,
        "updated_at": task.updated_at.strftime("%Y-%m-%d %H:%M:%S") if task.updated_at else None,
        "completed_at": None,
        "duration": duration,
        "remarks": None,
        "extra_info": {
            "almond_parser_id": task.almond_parser_id,
            "maxkb_url": task.maxkb_url,
            "maxkb_id": task.maxkb_id,
            "session_id": task.session_id,
            "task_type": task.task_type,
            "parsed_content_path": task.parsed_content_path,
            "batch_id": task.batch_id,  # 批次ID放到详情
            "document_id": task.doc_id or f"parse_{task.id}"  # 文档ID放到详情
        },
        "source_table": "parse_tasks",
        "source_name": "知识库"  # 数据来源名称
    }


def create_doc_document_from_doc_reader_task(task: DocReaderTask) -> Dict[str, Any]:
    """将DocReaderTask转换为统一的DOC文档格式"""
    duration = None
    # 耗时计算：更新时间 - 创建时间
    if task.created_at and task.updated_at and task.updated_at > task.created_at:
        duration = int((task.updated_at - task.created_at).total_seconds())
        # 如果耗时异常大（超过1天），设为None
        if duration > 86400:  # 86400秒 = 1天
            duration = None

    # 从meta_info中提取文件大小
    file_size = None
    if task.meta_info:
        try:
            import json
            if isinstance(task.meta_info, str):
                meta_data = json.loads(task.meta_info)
            else:
                meta_data = task.meta_info
            file_size = meta_data.get("file_size")
        except (json.JSONDecodeError, AttributeError):
            file_size = None

    return {
        "id": task.id,
        "document_id": task.doc_id,
        "batch_id": task.batch_id,
        "username": task.username,
        "file_name": task.filename,
        "file_type": task.file_type,
        "file_size": file_size,  # 从meta_info中提取
        "status": map_doc_reader_task_status(task.status),
        "kb_type": None,  # doc_reader_tasks表没有kb_type字段
        "parse_type": task.parse_type,  # 解析类型放到详情中
        "error_message": task.error_message,
        "created_at": task.created_at.isoformat() if task.created_at else None,
        "updated_at": task.updated_at.isoformat() if task.updated_at else None,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "duration": duration,
        "remarks": None,
        "extra_info": {
            "task_id": task.task_id,
            "session_id": task.session_id,
            "parser_doc_id": task.parser_doc_id,
            "almond_batch_id": task.almond_batch_id,
            "chunks_count": task.chunks_count,
            "retry_count": task.retry_count,
            "meta_info": task.meta_info,
            "parse_type": task.parse_type,  # 解析类型放到详情
            "batch_id": task.batch_id,  # 批次ID放到详情
            "document_id": task.doc_id  # 文档ID放到详情
        },
        "source_table": "doc_reader_tasks",
        "source_name": "多文档"  # 数据来源名称
    }


def map_parse_task_status(status: Optional[str]) -> str:
    """映射parse_tasks表的状态到统一状态"""
    if not status:
        return "PENDING"

    status_map = {
        "pending": "PENDING",
        "processing": "PROCESSING",
        "done": "COMPLETED",  # parse_tasks表用done表示完成
        "completed": "COMPLETED",
        "failed": "FAILED",
        "cancelled": "CANCELLED"
    }

    return status_map.get(status.lower(), status.upper())


def map_doc_reader_task_status(status: Optional[str]) -> str:
    """映射doc_reader_tasks表的状态到统一状态"""
    if not status:
        return "PENDING"

    status_map = {
        "pending": "PENDING",
        "processing": "PROCESSING",
        "completed": "COMPLETED",  # doc_reader_tasks表用completed表示完成
        "failed": "FAILED",
        "cancelled": "CANCELLED"
    }

    return status_map.get(status.lower(), status.upper())
