#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
动态超时时间计算器
根据文件大小和任务类型计算合理的超时阈值
"""
from datetime import timedelta
from typing import Dict, Any
from enum import Enum

from almond_parser.db.models.mineru_node import ServiceType


class TimeoutCalculator:
    """动态超时时间计算器"""
    
    # 基础超时时间（分钟）
    BASE_TIMEOUT = {
        ServiceType.DOCUMENT: 10,        # 文档解读：10分钟基础
        ServiceType.KNOWLEDGE_BASE: 30,  # 知识库：30分钟基础  
        ServiceType.UNIVERSAL: 15,       # 通用：15分钟基础
    }
    
    # 文件大小系数（每MB增加的分钟数）
    SIZE_FACTOR = {
        ServiceType.DOCUMENT: 0.5,       # 文档解读：每MB +0.5分钟
        ServiceType.KNOWLEDGE_BASE: 2.0, # 知识库：每MB +2分钟
        ServiceType.UNIVERSAL: 1.0,      # 通用：每MB +1分钟
    }
    
    # 最大超时时间（分钟）
    MAX_TIMEOUT = {
        ServiceType.DOCUMENT: 60,        # 文档解读：最多60分钟
        ServiceType.KNOWLEDGE_BASE: 180, # 知识库：最多180分钟（3小时）
        ServiceType.UNIVERSAL: 90,       # 通用：最多90分钟
    }
    
    # 最小超时时间（分钟）
    MIN_TIMEOUT = {
        ServiceType.DOCUMENT: 5,         # 文档解读：最少5分钟
        ServiceType.KNOWLEDGE_BASE: 15,  # 知识库：最少15分钟
        ServiceType.UNIVERSAL: 8,        # 通用：最少8分钟
    }
    
    @classmethod
    def calculate_timeout_minutes(
        cls, 
        service_type: ServiceType, 
        file_size_bytes: int,
        parse_mode: str = "auto"
    ) -> int:
        """
        计算超时时间（分钟）
        
        Args:
            service_type: 服务类型
            file_size_bytes: 文件大小（字节）
            parse_mode: 解析模式
            
        Returns:
            超时时间（分钟）
        """
        # 转换为MB
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        # 获取基础超时时间
        base_timeout = cls.BASE_TIMEOUT.get(service_type, cls.BASE_TIMEOUT[ServiceType.UNIVERSAL])
        
        # 计算文件大小增量
        size_factor = cls.SIZE_FACTOR.get(service_type, cls.SIZE_FACTOR[ServiceType.UNIVERSAL])
        size_increment = file_size_mb * size_factor
        
        # 解析模式调整
        mode_factor = cls._get_parse_mode_factor(parse_mode)
        
        # 计算总超时时间
        total_timeout = int((base_timeout + size_increment) * mode_factor)
        
        # 应用最大最小限制
        min_timeout = cls.MIN_TIMEOUT.get(service_type, cls.MIN_TIMEOUT[ServiceType.UNIVERSAL])
        max_timeout = cls.MAX_TIMEOUT.get(service_type, cls.MAX_TIMEOUT[ServiceType.UNIVERSAL])
        
        return max(min_timeout, min(total_timeout, max_timeout))
    
    @classmethod
    def _get_parse_mode_factor(cls, parse_mode: str) -> float:
        """获取解析模式的时间系数"""
        mode_factors = {
            "pipeline": 1.0,    # 标准模式
            "sglang": 1.5,      # VLM模式通常更慢
            "auto": 1.2,        # 自动模式稍慢
        }
        return mode_factors.get(parse_mode.lower(), 1.0)
    
    @classmethod
    def calculate_timeout_timedelta(
        cls, 
        service_type: ServiceType, 
        file_size_bytes: int,
        parse_mode: str = "auto"
    ) -> timedelta:
        """
        计算超时时间（timedelta对象）
        
        Args:
            service_type: 服务类型
            file_size_bytes: 文件大小（字节）
            parse_mode: 解析模式
            
        Returns:
            超时时间（timedelta）
        """
        minutes = cls.calculate_timeout_minutes(service_type, file_size_bytes, parse_mode)
        return timedelta(minutes=minutes)
    
    @classmethod
    def get_timeout_info(
        cls, 
        service_type: ServiceType, 
        file_size_bytes: int,
        parse_mode: str = "auto"
    ) -> Dict[str, Any]:
        """
        获取详细的超时信息
        
        Args:
            service_type: 服务类型
            file_size_bytes: 文件大小（字节）
            parse_mode: 解析模式
            
        Returns:
            超时信息字典
        """
        file_size_mb = file_size_bytes / (1024 * 1024)
        timeout_minutes = cls.calculate_timeout_minutes(service_type, file_size_bytes, parse_mode)
        
        return {
            "service_type": service_type.value,
            "file_size_mb": round(file_size_mb, 2),
            "parse_mode": parse_mode,
            "timeout_minutes": timeout_minutes,
            "timeout_seconds": timeout_minutes * 60,
            "base_timeout": cls.BASE_TIMEOUT.get(service_type, cls.BASE_TIMEOUT[ServiceType.UNIVERSAL]),
            "size_factor": cls.SIZE_FACTOR.get(service_type, cls.SIZE_FACTOR[ServiceType.UNIVERSAL]),
            "mode_factor": cls._get_parse_mode_factor(parse_mode),
            "min_timeout": cls.MIN_TIMEOUT.get(service_type, cls.MIN_TIMEOUT[ServiceType.UNIVERSAL]),
            "max_timeout": cls.MAX_TIMEOUT.get(service_type, cls.MAX_TIMEOUT[ServiceType.UNIVERSAL]),
        }


# 便捷函数
def calculate_task_timeout(service_type: str, file_size_bytes: int, parse_mode: str = "auto") -> int:
    """
    便捷函数：计算任务超时时间（分钟）
    
    Args:
        service_type: 服务类型字符串
        file_size_bytes: 文件大小（字节）
        parse_mode: 解析模式
        
    Returns:
        超时时间（分钟）
    """
    try:
        service_type_enum = ServiceType(service_type.lower())
    except ValueError:
        service_type_enum = ServiceType.UNIVERSAL
    
    return TimeoutCalculator.calculate_timeout_minutes(
        service_type_enum, 
        file_size_bytes, 
        parse_mode
    )


def get_zombie_task_threshold(service_type: str, file_size_bytes: int, parse_mode: str = "auto") -> int:
    """
    获取僵尸任务检测阈值（分钟）
    通常设置为正常超时时间的1.5倍
    
    Args:
        service_type: 服务类型字符串
        file_size_bytes: 文件大小（字节）
        parse_mode: 解析模式
        
    Returns:
        僵尸任务阈值（分钟）
    """
    normal_timeout = calculate_task_timeout(service_type, file_size_bytes, parse_mode)
    return int(normal_timeout * 1.5)
