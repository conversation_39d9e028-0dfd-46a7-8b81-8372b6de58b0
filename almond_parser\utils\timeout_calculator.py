#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
统一超时时间计算器
统一使用15分钟超时时间，支持环境变量配置
"""
import os
from datetime import timedelta
from typing import Dict, Any
from enum import Enum

from almond_parser.db.models.mineru_node import ServiceType


class TimeoutCalculator:
    """统一超时时间计算器"""

    # 统一超时时间（分钟）- 支持环境变量配置
    UNIFIED_TIMEOUT_MINUTES = int(os.getenv("TASK_TIMEOUT_MINUTES", "15"))

    # 基础超时时间（分钟）- 统一使用15分钟
    BASE_TIMEOUT = {
        ServiceType.DOCUMENT: UNIFIED_TIMEOUT_MINUTES,        # 文档解读：15分钟
        ServiceType.KNOWLEDGE_BASE: UNIFIED_TIMEOUT_MINUTES,  # 知识库：15分钟
        ServiceType.UNIVERSAL: UNIFIED_TIMEOUT_MINUTES,       # 通用：15分钟
    }

    # 文件大小系数（每MB增加的分钟数）- 统一为0，不再根据文件大小调整
    SIZE_FACTOR = {
        ServiceType.DOCUMENT: 0,         # 文档解读：不再根据大小调整
        ServiceType.KNOWLEDGE_BASE: 0,   # 知识库：不再根据大小调整
        ServiceType.UNIVERSAL: 0,        # 通用：不再根据大小调整
    }

    # 最大超时时间（分钟）- 统一为15分钟
    MAX_TIMEOUT = {
        ServiceType.DOCUMENT: UNIFIED_TIMEOUT_MINUTES,        # 文档解读：15分钟
        ServiceType.KNOWLEDGE_BASE: UNIFIED_TIMEOUT_MINUTES,  # 知识库：15分钟
        ServiceType.UNIVERSAL: UNIFIED_TIMEOUT_MINUTES,       # 通用：15分钟
    }

    # 最小超时时间（分钟）- 统一为15分钟
    MIN_TIMEOUT = {
        ServiceType.DOCUMENT: UNIFIED_TIMEOUT_MINUTES,        # 文档解读：15分钟
        ServiceType.KNOWLEDGE_BASE: UNIFIED_TIMEOUT_MINUTES,  # 知识库：15分钟
        ServiceType.UNIVERSAL: UNIFIED_TIMEOUT_MINUTES,       # 通用：15分钟
    }
    
    @classmethod
    def calculate_timeout_minutes(
        cls,
        service_type: ServiceType,
        file_size_bytes: int,
        parse_mode: str = "auto"
    ) -> int:
        """
        计算超时时间（分钟）- 统一返回15分钟

        Args:
            service_type: 服务类型（已忽略，统一处理）
            file_size_bytes: 文件大小（字节）（已忽略，统一处理）
            parse_mode: 解析模式（已忽略，统一处理）

        Returns:
            超时时间（分钟）- 统一15分钟
        """
        # 统一返回15分钟，不再根据文件大小、类型、模式进行调整
        return cls.UNIFIED_TIMEOUT_MINUTES
    
    @classmethod
    def _get_parse_mode_factor(cls, parse_mode: str) -> float:
        """获取解析模式的时间系数 - 统一返回1.0，不再调整"""
        # 统一返回1.0，不再根据解析模式调整超时时间
        return 1.0
    
    @classmethod
    def calculate_timeout_timedelta(
        cls, 
        service_type: ServiceType, 
        file_size_bytes: int,
        parse_mode: str = "auto"
    ) -> timedelta:
        """
        计算超时时间（timedelta对象）
        
        Args:
            service_type: 服务类型
            file_size_bytes: 文件大小（字节）
            parse_mode: 解析模式
            
        Returns:
            超时时间（timedelta）
        """
        minutes = cls.calculate_timeout_minutes(service_type, file_size_bytes, parse_mode)
        return timedelta(minutes=minutes)
    
    @classmethod
    def get_timeout_info(
        cls, 
        service_type: ServiceType, 
        file_size_bytes: int,
        parse_mode: str = "auto"
    ) -> Dict[str, Any]:
        """
        获取详细的超时信息
        
        Args:
            service_type: 服务类型
            file_size_bytes: 文件大小（字节）
            parse_mode: 解析模式
            
        Returns:
            超时信息字典
        """
        file_size_mb = file_size_bytes / (1024 * 1024)
        timeout_minutes = cls.calculate_timeout_minutes(service_type, file_size_bytes, parse_mode)
        
        return {
            "service_type": service_type.value,
            "file_size_mb": round(file_size_mb, 2),
            "parse_mode": parse_mode,
            "timeout_minutes": timeout_minutes,
            "timeout_seconds": timeout_minutes * 60,
            "base_timeout": cls.UNIFIED_TIMEOUT_MINUTES,
            "size_factor": 0,  # 不再根据大小调整
            "mode_factor": 1.0,  # 不再根据模式调整
            "min_timeout": cls.UNIFIED_TIMEOUT_MINUTES,
            "max_timeout": cls.UNIFIED_TIMEOUT_MINUTES,
            "unified_timeout": True,  # 标识使用统一超时
        }


# 便捷函数
def calculate_task_timeout(service_type: str, file_size_bytes: int, parse_mode: str = "auto") -> int:
    """
    便捷函数：计算任务超时时间（分钟）- 统一返回15分钟

    Args:
        service_type: 服务类型字符串（已忽略）
        file_size_bytes: 文件大小（字节）（已忽略）
        parse_mode: 解析模式（已忽略）

    Returns:
        超时时间（分钟）- 统一15分钟
    """
    # 统一返回15分钟，不再根据参数调整
    return TimeoutCalculator.UNIFIED_TIMEOUT_MINUTES


def get_zombie_task_threshold(service_type: str, file_size_bytes: int, parse_mode: str = "auto") -> int:
    """
    获取僵尸任务检测阈值（分钟）- 统一返回15分钟

    Args:
        service_type: 服务类型字符串（已忽略）
        file_size_bytes: 文件大小（字节）（已忽略）
        parse_mode: 解析模式（已忽略）

    Returns:
        僵尸任务阈值（分钟）- 统一15分钟
    """
    # 统一返回15分钟，不再使用1.5倍计算
    return TimeoutCalculator.UNIFIED_TIMEOUT_MINUTES
