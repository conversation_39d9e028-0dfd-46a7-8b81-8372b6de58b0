<template>
  <div class="icon-test">
    <h3>图标测试</h3>
    <div class="icon-list">
      <div class="icon-item">
        <el-icon><Plus /></el-icon>
        <span>Plus</span>
      </div>
      <div class="icon-item">
        <el-icon><Edit /></el-icon>
        <span>Edit</span>
      </div>
      <div class="icon-item">
        <el-icon><Delete /></el-icon>
        <span>Delete</span>
      </div>
      <div class="icon-item">
        <el-icon><Refresh /></el-icon>
        <span>Refresh</span>
      </div>
      <div class="icon-item">
        <el-icon><Setting /></el-icon>
        <span>Setting</span>
      </div>
      <div class="icon-item">
        <el-icon><Loading /></el-icon>
        <span>Loading</span>
      </div>
      <div class="icon-item">
        <el-icon><Warning /></el-icon>
        <span>Warning</span>
      </div>
      <div class="icon-item">
        <el-icon><SuccessFilled /></el-icon>
        <span>SuccessFilled</span>
      </div>
      <div class="icon-item">
        <el-icon><QuestionFilled /></el-icon>
        <span>QuestionFilled</span>
      </div>
    </div>
    
    <h4>Emoji图标测试</h4>
    <div class="emoji-list">
      <span class="emoji">⚡</span>
      <span class="emoji">💾</span>
      <span class="emoji">🎮</span>
      <span class="emoji">💻</span>
      <span class="emoji">🖥️</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Plus, Edit, Delete, Refresh, Setting, Loading, Warning, SuccessFilled, QuestionFilled 
} from '@element-plus/icons-vue'
</script>

<style scoped>
.icon-test {
  padding: 20px;
}

.icon-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
}

.emoji-list {
  display: flex;
  gap: 16px;
}

.emoji {
  font-size: 24px;
}
</style>
