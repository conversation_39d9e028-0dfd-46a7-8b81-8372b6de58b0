# 🐳 杏仁解析项目 Docker 部署指南

## 📋 项目概述

本项目提供完全解耦的Docker部署方案，支持以下服务的独立或组合部署：

- **infrastructure** - 基础设施（MySQL + Redis + Nginx）
- **almond-parser** - 杏仁解析服务（任务调度中心）
- **mineru-api** - MinerU解析引擎（解析节点）
- **web** - Web管理后台（Dashboard）

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │  Almond Parser  │    │   MinerU API    │
│   (Dashboard)   │◄──►│ (Task Manager)  │◄──►│ (Parse Engine)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Infrastructure  │
                    │ MySQL + Redis   │
                    │    + Nginx      │
                    └─────────────────┘
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository>
cd aicenter-parserflow/docker

# 复制环境配置
cp config/.env.infrastructure.example config/.env.infrastructure
cp config/.env.almond-parser.example config/.env.almond-parser
cp config/.env.mineru-api.example config/.env.mineru-api
cp config/.env.web.example config/.env.web

# 编辑配置文件（根据实际环境修改）
```

### 2. 构建镜像

```bash
# 构建所有镜像
./scripts/build.sh

# 或单独构建
docker build -f dockerfiles/almond_parser.Dockerfile -t almond-parser:latest ../almond_parser
docker build -f dockerfiles/mineru_api.Dockerfile -t mineru-api:latest ../mineru_api
docker build -f dockerfiles/web.Dockerfile -t web-frontend:latest ../web
```

## 📦 部署模式

### 🔧 独立服务部署

#### 基础设施
```bash
./scripts/deploy-infrastructure.sh
# 包含：MySQL + Redis + Nginx
# 端口：3306, 6379, 80
```

#### 杏仁解析服务
```bash
./scripts/deploy-almond-parser.sh
# 端口：8000
# 依赖：MySQL + Redis
```

#### MinerU解析引擎
```bash
./scripts/deploy-mineru-api.sh
# 端口：8001
# 支持：GPU加速 + LibreOffice
```

#### Web管理后台
```bash
./scripts/deploy-web.sh
# 端口：通过Nginx代理
# 依赖：Almond Parser API
```

### 🎯 组合部署

#### 管理平台（推荐）
```bash
./scripts/deploy-management.sh
# 包含：Infrastructure + Almond Parser + Web
# 适用：管理中心，无GPU解析
```

#### 解析集群
```bash
./scripts/deploy-parsing-cluster.sh
# 包含：多个MinerU API节点
# 适用：GPU解析农场
```

#### 完整部署
```bash
./scripts/deploy-full.sh
# 包含：所有服务
# 适用：单机全功能部署
```

## 🔄 扩展操作

### 添加解析节点
```bash
./scripts/add-parsing-node.sh
# 动态添加MinerU API节点
```

### 扩展解析集群
```bash
./scripts/scale-mineru.sh 3
# 扩展到3个解析节点
```

## 🌐 网络配置

所有服务使用 `parserflow-network` 网络：
- 服务间通过服务名访问
- 支持跨主机部署（overlay网络）
- 统一的DNS解析

## 📊 端口映射

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|---------|---------|------|
| Nginx | 80 | 80 | Web入口 |
| Almond Parser | 8000 | 8000 | API服务 |
| MinerU API | 8001 | 8001 | 解析服务 |
| MySQL | 3306 | 3306 | 数据库 |
| Redis | 6379 | 6379 | 缓存 |

## 🔧 配置说明

### 环境变量文件

- `.env.infrastructure` - 数据库、Redis、Nginx配置
- `.env.almond-parser` - 杏仁解析服务配置
- `.env.mineru-api` - 解析引擎配置（GPU、LibreOffice）
- `.env.web` - Web前端配置

### 数据持久化

```bash
# 数据目录挂载
./data/mysql/          # MySQL数据
./data/redis/          # Redis数据
./data/uploads/        # 上传文件
./logs/               # 日志文件
```

## 🚨 故障排除

### 常见问题

1. **GPU不可用**
   ```bash
   # 检查GPU支持
   docker run --gpus all nvidia/cuda:11.8-base nvidia-smi
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8000
   ```

3. **网络连接问题**
   ```bash
   # 检查网络
   docker network ls
   docker network inspect parserflow-network
   ```

## 📝 维护操作

### 日志查看
```bash
# 查看服务日志
docker-compose -f compose/almond-parser.yml logs -f

# 查看所有服务日志
docker-compose -f compose/full.yml logs -f
```

### 服务重启
```bash
# 重启单个服务
docker-compose -f compose/almond-parser.yml restart

# 重启所有服务
docker-compose -f compose/full.yml restart
```

### 数据备份
```bash
# 备份MySQL数据
docker exec mysql-container mysqldump -u root -p parserflow > backup.sql

# 备份上传文件
tar -czf uploads-backup.tar.gz ./data/uploads/
```

## 🎯 部署建议

### 生产环境
- 使用 `deploy-management.sh` 部署管理平台
- 使用 `deploy-parsing-cluster.sh` 部署GPU集群
- 分离数据库到独立服务器

### 开发环境
- 使用 `deploy-full.sh` 单机部署
- 启用开发模式配置

### 测试环境
- 使用 `deploy-management.sh` 无GPU部署
- 使用内存数据库加速测试

## 🚀 快速开始示例

### 场景1：完整部署
```bash
# 1. 构建镜像
./scripts/build.sh

# 2. 一键部署所有服务
./scripts/deploy-full.sh

# 3. 访问系统
# Web管理平台: http://localhost
# API文档: http://localhost:8000/docs
```

### 场景2：管理平台部署（无GPU环境）
```bash
# 1. 构建镜像
./scripts/build.sh

# 2. 部署管理平台
./scripts/deploy-management.sh

# 3. 后续添加解析节点
./scripts/deploy-mineru-api.sh
```

### 场景3：分布式部署
```bash
# 服务器A：部署管理平台
./scripts/deploy-management.sh

# 服务器B：部署解析节点
./scripts/deploy-mineru-api.sh

# 服务器C：扩展解析集群
./scripts/scale-mineru.sh 3
```

## 📋 部署前准备

### 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ 内存
- 20GB+ 磁盘空间
- GPU支持（可选，用于加速解析）

### 权限设置
```bash
# 设置脚本执行权限
chmod +x scripts/*.sh
```

---

**🌰 杏仁解析 - 智能文档解析的容器化部署方案**
