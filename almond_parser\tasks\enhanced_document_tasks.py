# -*- encoding: utf-8 -*-
"""
增强的文档处理任务 - 支持资源保护型节点分配和智能降级重试
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from urllib.parse import urljoin
import base64

from loguru import logger
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from almond_parser.db import db_manager
from almond_parser.db.models.document import Document, DocumentStatus, DocumentLog
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
from almond_parser.services.document_service import DocumentService
from almond_parser.utils.node_selector import NodeSelector
from almond_parser.config import settings


async def enhanced_process_document(
        ctx: Dict[str, Any],
        document_id: str,
        user_id: str,
        service_type: str = "auto",
        parse_mode: str = "auto",
        max_retries: int = 2,
        config: Optional[Dict[str, Any]] = None,
        is_fallback_retry: bool = False
) -> Dict[str, Any]:
    """
    增强的文档处理任务 - 支持资源保护型节点分配和智能降级重试

    Args:
        ctx: ARQ 上下文
        document_id: 文档ID
        user_id: 用户ID
        service_type: 服务类型
        parse_mode: 解析模式
        max_retries: 最大重试次数
        config: 解析配置
        is_fallback_retry: 是否为降级重试
    """
    logger.info(f"开始处理文档: {document_id}, 模式: {parse_mode}, 降级重试: {is_fallback_retry}")

    # 🔒 幂等性保护：检查文档状态，防止重复处理
    async with db_manager.session_factory() as db:
        try:
            # 使用行锁获取文档，确保原子性检查和更新
            result = await db.execute(
                select(Document)
                .where(Document.document_id == document_id)
                .with_for_update()  # 行锁，防止并发冲突
            )
            document = result.scalar_one_or_none()

            if not document:
                error_msg = f"文档不存在: {document_id}"
                logger.error(error_msg)
                return {"success": False, "error": error_msg}

            # 智能幂等性检查：区分正常处理和卡住的任务
            if document.status == DocumentStatus.COMPLETED:
                logger.info(f"🔄 文档 {document_id} 已完成，跳过重复处理")
                return {
                    "success": True,
                    "message": f"任务已完成，当前状态: {document.status.value}",
                    "document_id": document_id,
                    "status": document.status.value,
                    "progress": document.progress,
                    "idempotent_skip": True
                }
            elif document.status == DocumentStatus.PARSING:
                # PARSING状态需要进一步检查是否真的在处理中
                if document.node_id and document.started_at and document.task_id:
                    # 有完整的处理信息，说明正在正常处理，跳过
                    logger.info(f"🔄 文档 {document_id} 正在正常处理中 (node_id: {document.node_id}, task_id: {document.task_id})，跳过重复处理")
                    return {
                        "success": True,
                        "message": f"任务正在处理中，当前状态: {document.status.value}",
                        "document_id": document_id,
                        "status": document.status.value,
                        "progress": document.progress,
                        "idempotent_skip": True
                    }
                else:
                    # 缺少关键信息，说明可能卡住了，允许重新处理
                    logger.info(f"🔧 检测到卡住的PARSING任务，允许重新处理: {document_id} (node_id: {document.node_id}, started_at: {document.started_at}, task_id: {document.task_id})")
                    # 重置状态，继续处理
                    document.status = DocumentStatus.PENDING
                    document.node_id = None
                    document.started_at = None
                    document.task_id = None

            # 更新状态为PENDING，记录ARQ提交时间防冲突
            try:
                if document.status != DocumentStatus.PENDING:  # 避免重复设置
                    logger.info(f"更新文档状态: {document.status.value} -> PENDING")
                    document.status = DocumentStatus.PENDING
                document.progress = 0  # 初始进度
                document.started_at = datetime.now()  # 记录ARQ提交时间，用于防冲突
                await db.commit()

                logger.info(f"✅ 文档 {document_id} 状态已更新为PENDING，已提交到ARQ队列")
            except Exception as status_error:
                logger.error(f"❌ 状态更新失败: {document_id}, 当前状态: {document.status}, 错误: {status_error}")
                await db.rollback()
                raise

        except Exception as e:
            logger.error(f"❌ 幂等性检查失败: {document_id}, 错误: {e}")
            await db.rollback()
            return {"success": False, "error": f"幂等性检查失败: {str(e)}"}

    # 继续原有的处理逻辑...
    # 注意：document 对象已经在上面的幂等性检查中获取并更新了状态

    async with db_manager.session_factory() as db:
        try:
            # 重新获取文档信息（因为使用了新的数据库会话）
            result = await db.execute(
                select(Document).where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                raise ValueError(f"文档 {document_id} 不存在")

            # 记录原始解析模式
            if not document.original_parse_mode:
                document.original_parse_mode = parse_mode
                document.current_parse_mode = parse_mode

            # 如果是降级重试，更新解析模式
            if is_fallback_retry:
                document.current_parse_mode = "pipeline"
                document.has_fallback = True
                document.status = DocumentStatus.FALLBACK_RETRY
                await _log_document_event(
                    db, document_id, "INFO",
                    f"开始降级重试: {document.original_parse_mode} -> pipeline"
                )

            # 使用并发控制管理器分配节点
            from almond_parser.utils.node_concurrency_manager import node_concurrency_manager
            from almond_parser.db.models import ServiceType

            # 映射服务类型 - 优先使用任务参数，然后使用文档中存储的信息
            if service_type == "auto":
                # 如果任务参数是auto，尝试从文档类型推断
                if document.file_type in ["pdf", "doc", "docx", "ppt", "pptx"]:
                    service_type_enum = ServiceType.DOCUMENT
                else:
                    service_type_enum = ServiceType.UNIVERSAL
            else:
                service_type_enum = {
                    "document": ServiceType.DOCUMENT,
                    "knowledge_base": ServiceType.KNOWLEDGE_BASE,
                    "universal": ServiceType.UNIVERSAL
                }.get(service_type, ServiceType.UNIVERSAL)

            # 🔑 关键修复：使用原子操作进行容量检查和节点分配
            # 将容量检查和节点分配合并到一个事务中，避免竞态条件
            from almond_parser.utils.simple_node_manager import simple_node_manager

            logger.info(f"🔒 开始原子性节点分配: {document_id}")

            node, allocation_reason = await simple_node_manager.allocate_node(
                db=db,
                service_type=service_type_enum,
                parse_mode=document.current_parse_mode,
                document_id=document_id
            )

            if not node:
                # 根据失败原因决定处理策略
                if allocation_reason == "nodes_at_capacity":
                    # 节点容量已满，保持PENDING状态等待
                    logger.info(f"节点容量已满，任务保持PENDING状态: {document_id}")
                    return {
                        "success": False,
                        "document_id": document_id,
                        "error": "nodes_at_capacity",
                        "retry_scheduled": False,  # 不需要重试，等待容量释放
                        "should_remain_pending": True
                    }
                else:
                    # 节点真正不可用（离线、错误等），标记为系统重试
                    await _handle_node_unavailable(db, document, service_type, parse_mode)
                    return {
                        "success": False,
                        "document_id": document_id,
                        "error": allocation_reason,
                        "retry_scheduled": True
                    }

            # 先分配节点但不更新状态
            document.node_id = node.id
            await db.commit()

            # 获取实际执行模式
            execution_mode = getattr(node, '_execution_mode', document.current_parse_mode)

            # 记录节点分配详情
            await _log_document_event(
                db, document_id, "INFO",
                f"节点分配成功: {node.name} (节点模式: {node.parse_mode.value}, 执行模式: {execution_mode})",
                {
                    "node_id": node.id,
                    "node_name": node.name,
                    "node_mode": node.parse_mode.value,
                    "execution_mode": execution_mode,
                    "service_type": node.service_type.value,
                    "resource_protection": node.parse_mode.value == "pipeline" and document.current_parse_mode == "pipeline"
                }
            )

            # 调用 MinerU API
            api_result = await _call_mineru_api_enhanced(
                node, document, config, execution_mode
            )

            if api_result["success"]:
                # API调用成功，直接更新状态为PARSING
                returned_task_id = api_result["task_id"]
                logger.info(f"🔑 MinerU返回task_id: {returned_task_id} (document: {document_id})")

                document.status = DocumentStatus.PARSING
                document.progress = 10
                document.started_at = datetime.now()
                document.task_id = returned_task_id

                logger.info(f"📝 准备提交事务: document_id={document_id}, task_id={returned_task_id}")

                try:
                    await db.commit()
                    logger.info(f"✅ 事务提交成功: document_id={document_id}, task_id={returned_task_id}")
                except Exception as commit_error:
                    logger.error(f"❌ 事务提交失败: document_id={document_id}, task_id={returned_task_id}, 错误: {commit_error}")
                    raise

                await _log_document_event(
                    db, document_id, "INFO",
                    f"MinerU API 调用成功，任务开始解析: {api_result.get('message', '任务已提交')} (task_id: {returned_task_id})"
                )

                return {
                    "success": True,
                    "document_id": document_id,
                    "status": "submitted_to_mineru",
                    "node": node.name,
                    "task_id": api_result.get("task_id"),
                    "parse_mode": document.current_parse_mode,
                    "execution_mode": execution_mode
                }
            else:
                document.status = DocumentStatus.RETRY_PENDING
                db.commit()
                # API 调用失败，释放任务槽位
                await simple_node_manager.release_node(
                    db=db,
                    node_id=node.id,
                    task_id="failed_submission",
                    success=False,
                    reason="API调用失败"
                )

                # 判断是否需要降级重试
                await _handle_api_failure(
                    db, document, api_result["error"],
                    user_id, service_type, config
                )

                return {
                    "success": False,
                    "document_id": document_id,
                    "error": api_result["error"],
                    "fallback_scheduled": not is_fallback_retry and document.original_parse_mode != "pipeline"
                }

        except Exception as e:
            logger.error(f"文档处理异常: {document_id}, 错误: {e}")

            # 更新文档状态为失败
            try:
                document.status = DocumentStatus.FAILED
                document.error_message = str(e)
                await db.commit()

                await _log_document_event(
                    db, document_id, "ERROR",
                    f"文档处理异常: {str(e)}"
                )
            except Exception as commit_error:
                logger.error(f"更新文档状态失败: {commit_error}")

            return {
                "success": False,
                "document_id": document_id,
                "error": str(e)
            }


async def enhanced_process_document_result(
        ctx: Dict[str, Any],
        document_id: str,
        result_data: Dict[str, Any],
        task_id: str,
        success: bool = True
) -> Dict[str, Any]:
    """
    增强的文档结果处理 - 支持结果检查和自动降级重试

    Args:
        ctx: ARQ 上下文
        document_id: 文档ID
        result_data: 解析结果数据
        task_id: 任务ID
        success: 是否成功
    """
    logger.info(f"处理文档结果: {document_id}, 任务: {task_id}, 成功: {success}")

    async with db_manager.session_factory() as db:
        try:
            # 获取文档信息 - 直接从数据库查询Document模型
            from sqlalchemy import select
            result = await db.execute(
                select(Document).where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()

            if not document:
                raise ValueError(f"文档 {document_id} 不存在")

            if success and result_data:
                # 检查解析结果质量
                quality_check = await _check_result_quality(result_data)

                if quality_check["is_valid"]:
                    # 结果有效，保存成功结果
                    document.status = DocumentStatus.COMPLETED
                    document.result_data = result_data
                    document.completed_at = datetime.now()
                    document.progress = 100

                    await _log_document_event(
                        db, document_id, "INFO",
                        f"解析完成: {quality_check['summary']}"
                    )

                    await db.commit()

                    return {
                        "success": True,
                        "document_id": document_id,
                        "status": "completed",
                        "quality_check": quality_check
                    }
                else:
                    # 结果质量不佳，考虑降级重试
                    return await _handle_poor_quality_result(
                        db, document, quality_check, result_data
                    )
            else:
                # 解析失败，考虑降级重试
                return await _handle_parsing_failure(
                    db, document, result_data.get("error", "解析失败")
                )

        except Exception as e:
            logger.error(f"处理文档结果异常: {document_id}, 错误: {e}")

            try:
                document.status = DocumentStatus.FAILED
                document.error_message = str(e)
                await db.commit()

                await _log_document_event(
                    db, document_id, "ERROR",
                    f"结果处理异常: {str(e)}"
                )
            except Exception as commit_error:
                logger.error(f"更新文档状态失败: {commit_error}")

            return {
                "success": False,
                "document_id": document_id,
                "error": str(e)
            }


async def _handle_node_unavailable(
        db: AsyncSession,
        document: Document,
        service_type: str,
        parse_mode: str
) -> None:
    """处理节点不可用情况 - 增强版"""
    document.status = DocumentStatus.RETRY_PENDING
    document.is_system_retry = True
    document.retry_reason = "node_unavailable"
    document.next_retry_at = datetime.now() + timedelta(minutes=5)  # 5分钟后重试

    await db.commit()

    await _log_document_event(
        db, document.document_id, "WARNING",
        f"节点不可用，已安排系统重试: {document.next_retry_at}",
        {
            "service_type": service_type,
            "parse_mode": parse_mode,
            "retry_reason": "node_unavailable"
        }
    )


async def _handle_api_failure(
        db: AsyncSession,
        document: Document,
        error_message: str,
        user_id: str,
        service_type: str,
        config: Optional[Dict[str, Any]]
) -> None:
    """处理API调用失败 - 增强版"""
    # 判断是否为VLM模式且未进行过降级重试
    can_fallback = (
        document.original_parse_mode in ["sglang", "vlm", "vlm-sglang-client"] and
        not document.has_fallback and
        document.current_parse_mode != "pipeline"
    )

    if can_fallback:
        # 安排降级重试
        logger.info(f"安排降级重试: {document.document_id}")

        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        job_id = await arq_manager.enqueue_task(
            "enhanced_process_document",
            document_id=document.document_id,
            user_id=user_id,
            service_type=service_type,
            parse_mode="pipeline",
            config=config,
            is_fallback_retry=True
        )

        await _log_document_event(
            db, document.document_id, "INFO",
            f"VLM解析失败，已安排降级重试任务: {job_id}",
            {
                "original_mode": document.original_parse_mode,
                "fallback_mode": "pipeline",
                "job_id": job_id,
                "error": error_message
            }
        )
    else:
        # 标记为失败
        document.status = DocumentStatus.FAILED
        document.error_message = error_message
        await db.commit()

        await _log_document_event(
            db, document.document_id, "ERROR",
            f"解析失败: {error_message}",
            {"cannot_fallback": True, "reason": "已经是pipeline模式或已尝试降级"}
        )


async def _handle_poor_quality_result(
        db: AsyncSession,
        document: Document,
        quality_check: Dict[str, Any],
        result_data: Dict[str, Any]
) -> Dict[str, Any]:
    """处理质量不佳的解析结果"""
    # 判断是否可以降级重试
    can_fallback = (
        document.original_parse_mode in ["sglang", "vlm", "vlm-sglang-client"] and
        not document.has_fallback and
        document.current_parse_mode != "pipeline"
    )

    if can_fallback:
        # 安排降级重试
        logger.info(f"解析结果质量不佳，安排降级重试: {document.document_id}")

        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        job_id = await arq_manager.enqueue_task(
            "enhanced_process_document",
            document_id=document.document_id,
            user_id=document.user_id,
            service_type="auto",
            parse_mode="pipeline",
            config={},
            is_fallback_retry=True
        )

        await _log_document_event(
            db, document.document_id, "WARNING",
            f"解析结果质量不佳，已安排降级重试: {job_id}",
            {
                "quality_issues": quality_check["issues"],
                "original_mode": document.original_parse_mode,
                "fallback_mode": "pipeline",
                "job_id": job_id
            }
        )

        return {
            "success": False,
            "document_id": document.document_id,
            "error": "poor_quality_result",
            "fallback_scheduled": True,
            "quality_check": quality_check
        }
    else:
        # 无法降级，接受当前结果
        document.status = DocumentStatus.COMPLETED
        document.result_data = result_data
        document.completed_at = datetime.now()
        document.progress = 100

        await _log_document_event(
            db, document.document_id, "WARNING",
            f"解析结果质量不佳但无法降级，接受当前结果: {quality_check['summary']}",
            {"quality_issues": quality_check["issues"]}
        )

        await db.commit()

        return {
            "success": True,
            "document_id": document.document_id,
            "status": "completed_with_issues",
            "quality_check": quality_check
        }


async def _handle_parsing_failure(
        db: AsyncSession,
        document: Document,
        error_message: str
) -> Dict[str, Any]:
    """处理解析失败"""
    # 判断是否可以降级重试
    can_fallback = (
        document.original_parse_mode in ["sglang", "vlm", "vlm-sglang-client"] and
        not document.has_fallback and
        document.current_parse_mode != "pipeline"
    )

    if can_fallback:
        # 安排降级重试
        logger.info(f"解析失败，安排降级重试: {document.document_id}")

        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        job_id = await arq_manager.enqueue_task(
            "enhanced_process_document",
            document_id=document.document_id,
            user_id=document.user_id,
            service_type="auto",
            parse_mode="pipeline",
            config={},
            is_fallback_retry=True
        )

        await _log_document_event(
            db, document.document_id, "INFO",
            f"解析失败，已安排降级重试: {job_id}",
            {
                "original_mode": document.original_parse_mode,
                "fallback_mode": "pipeline",
                "job_id": job_id,
                "error": error_message
            }
        )

        return {
            "success": False,
            "document_id": document.document_id,
            "error": error_message,
            "fallback_scheduled": True
        }
    else:
        # 标记为失败
        document.status = DocumentStatus.FAILED
        document.error_message = error_message
        await db.commit()

        await _log_document_event(
            db, document.document_id, "ERROR",
            f"解析失败且无法降级: {error_message}",
            {"cannot_fallback": True}
        )

        return {
            "success": False,
            "document_id": document.document_id,
            "error": error_message,
            "fallback_scheduled": False
        }


async def _check_result_quality(result_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查解析结果质量

    Args:
        result_data: 解析结果数据

    Returns:
        质量检查结果
    """
    try:
        issues = []

        # 检查是否有内容
        content = result_data.get("content", "")
        if not content or len(content.strip()) < 10:
            issues.append("内容过短或为空")

        # 检查是否有结构化数据
        markdown = result_data.get("markdown", "")
        if not markdown or len(markdown.strip()) < 10:
            issues.append("Markdown内容过短或为空")

        # 检查是否有页面信息
        pages = result_data.get("pages", [])
        if not pages:
            issues.append("缺少页面信息")

        # 检查是否有异常错误信息
        error_indicators = ["error", "failed", "exception", "timeout"]
        content_lower = content.lower() if content else ""
        for indicator in error_indicators:
            if indicator in content_lower:
                issues.append(f"内容中包含错误指示词: {indicator}")

        # 综合评估
        is_valid = len(issues) == 0

        # 生成摘要
        if is_valid:
            summary = f"解析成功，内容长度: {len(content)}, 页面数: {len(pages)}"
        else:
            summary = f"解析质量不佳，发现 {len(issues)} 个问题"

        return {
            "is_valid": is_valid,
            "issues": issues,
            "summary": summary,
            "content_length": len(content) if content else 0,
            "page_count": len(pages) if pages else 0
        }

    except Exception as e:
        logger.error(f"质量检查异常: {e}")
        return {
            "is_valid": False,
            "issues": [f"质量检查异常: {str(e)}"],
            "summary": "质量检查失败",
            "content_length": 0,
            "page_count": 0
        }


async def _call_mineru_api_enhanced(
        node: MinerUNode,
        document: Document,
        config: Optional[Dict[str, Any]],
        parse_mode: str
) -> Dict[str, Any]:
    """增强的 MinerU API 调用 - 支持资源保护策略"""
    try:
        import aiohttp

        # 构建请求数据
        parse_url = urljoin(node.base_url, "/predict")
        callback_url = f"{settings.HOST_URL}/api/v1/document/callback"

        # 读取文件并编码
        with open(document.file_path, "rb") as f:
            file_bytes = f.read()
        file_b64 = base64.b64encode(file_bytes).decode("utf-8")

        # 构建请求数据，确保使用正确的解析模式
        request_data = {
            "document_id": document.document_id,
            "file_name": document.file_name,
            "file_content": file_b64,
            "callback_url": callback_url,
            "callback_headers": {"Authorization": f"Bearer {node.auth_token}"},
            "backend": parse_mode,  # 使用当前解析模式
            **(config or {})
        }

        logger.info(f"调用MinerU API: {parse_url}, 模式: {parse_mode}, 节点: {node.name}")

        # 发送请求
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                parse_url,
                json=request_data,
                headers={"Authorization": f"Bearer {node.auth_token}"}
            ) as response:

                if response.status == 200:
                    result = await response.json()
                    returned_task_id = result.get("task_id")
                    logger.info(f"✅ MinerU API 调用成功: 节点={node.name}, 返回task_id={returned_task_id}")
                    logger.info(f"📋 MinerU完整响应: {result}")

                    if not returned_task_id:
                        logger.error(f"❌ MinerU返回成功但task_id为空: {result}")
                        return {
                            "success": False,
                            "error": "MinerU返回成功但task_id为空"
                        }

                    return {
                        "success": True,
                        "task_id": returned_task_id,
                        "message": result.get("message", "任务提交成功")
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"MinerU API 调用失败: {node.name}, HTTP {response.status}: {error_text}")
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}"
                    }

    except Exception as e:
        logger.error(f"调用 MinerU API 异常: {node.name}, 错误: {e}")
        return {
            "success": False,
            "error": f"API调用异常: {str(e)}"
        }


async def _log_document_event(
        db: AsyncSession,
        document_id: str,
        level: str,
        message: str,
        extra_data: Optional[Dict[str, Any]] = None
) -> None:
    """记录文档事件日志"""
    try:
        log_entry = DocumentLog(
            document_id=document_id,
            level=level,
            message=message,
            source="enhanced_document_tasks",
            extra_data=extra_data
        )
        db.add(log_entry)
        await db.commit()
    except Exception as e:
        logger.error(f"记录文档日志失败: {e}")
