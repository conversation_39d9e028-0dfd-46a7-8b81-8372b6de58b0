<template>
  <div class="system-resource-test">
    <h3>系统资源显示测试</h3>
    
    <div class="test-section">
      <h4>当前样式测试</h4>
      <div class="test-container">
        <div class="summary-trigger">
          <div class="summary-item">
            <img src="/CPU.png" alt="CPU" class="icon-img" />
            <span class="value usage-low">0.1%</span>
          </div>
          <div class="summary-item">
            <img src="/Memory.png" alt="Memory" class="icon-img" />
            <span class="value usage-medium">62.0%</span>
          </div>
          <div class="summary-item">
            <img src="/GPU.png" alt="GPU" class="icon-img" />
            <span class="value usage-high">77.6%</span>
          </div>
          <div class="summary-status">
            <el-icon color="#67C23A"><SuccessFilled /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>不同宽度测试</h4>
      <div class="width-tests">
        <div class="width-test" style="width: 140px;">
          <div class="width-label">140px (原宽度)</div>
          <div class="summary-trigger">
            <div class="summary-item">
              <img src="/CPU.png" alt="CPU" class="icon-img" />
              <span class="value">0.1%</span>
            </div>
            <div class="summary-item">
              <img src="/Memory.png" alt="Memory" class="icon-img" />
              <span class="value">62.0%</span>
            </div>
            <div class="summary-item">
              <img src="/GPU.png" alt="GPU" class="icon-img" />
              <span class="value">77.6%</span>
            </div>
            <div class="summary-status">
              <el-icon color="#67C23A"><SuccessFilled /></el-icon>
            </div>
          </div>
        </div>

        <div class="width-test" style="width: 200px;">
          <div class="width-label">200px (新宽度)</div>
          <div class="summary-trigger">
            <div class="summary-item">
              <img src="/CPU.png" alt="CPU" class="icon-img" />
              <span class="value">0.1%</span>
            </div>
            <div class="summary-item">
              <img src="/Memory.png" alt="Memory" class="icon-img" />
              <span class="value">62.0%</span>
            </div>
            <div class="summary-item">
              <img src="/GPU.png" alt="GPU" class="icon-img" />
              <span class="value">77.6%</span>
            </div>
            <div class="summary-status">
              <el-icon color="#67C23A"><SuccessFilled /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>GPU信息详细测试</h4>
      <div class="gpu-tests">
        <div class="gpu-test-item">
          <div class="test-label">无GPU</div>
          <div class="summary-trigger">
            <div class="summary-item">
              <img src="/CPU.png" alt="CPU" class="icon-img" />
              <span class="value">15.2%</span>
            </div>
            <div class="summary-item">
              <img src="/Memory.png" alt="Memory" class="icon-img" />
              <span class="value">68.5%</span>
            </div>
            <div class="summary-status">
              <el-icon color="#67C23A"><SuccessFilled /></el-icon>
            </div>
          </div>
        </div>

        <div class="gpu-test-item">
          <div class="test-label">1个GPU</div>
          <div class="summary-trigger">
            <div class="summary-item">
              <img src="/CPU.png" alt="CPU" class="icon-img" />
              <span class="value">15.2%</span>
            </div>
            <div class="summary-item">
              <img src="/Memory.png" alt="Memory" class="icon-img" />
              <span class="value">68.5%</span>
            </div>
            <div class="summary-item">
              <img src="/GPU.png" alt="GPU" class="icon-img" />
              <span class="value">65.2%</span>
            </div>
            <div class="summary-status">
              <el-icon color="#67C23A"><SuccessFilled /></el-icon>
            </div>
          </div>
        </div>

        <div class="gpu-test-item">
          <div class="test-label">2个GPU</div>
          <div class="summary-trigger">
            <div class="summary-item">
              <img src="/CPU.png" alt="CPU" class="icon-img" />
              <span class="value">15.2%</span>
            </div>
            <div class="summary-item">
              <img src="/Memory.png" alt="Memory" class="icon-img" />
              <span class="value">68.5%</span>
            </div>
            <div class="summary-item">
              <img src="/GPU.png" alt="GPU" class="icon-img" />
              <span class="value">82.3%</span>
            </div>
            <div class="summary-status">
              <el-icon color="#67C23A"><SuccessFilled /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SuccessFilled } from '@element-plus/icons-vue'
</script>

<style scoped>
.system-resource-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.test-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
}

.width-tests {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.width-test {
  padding: 15px;
  border: 2px solid var(--el-border-color);
  border-radius: 8px;
  background: var(--el-fill-color-extra-light);
}

.width-label {
  text-align: center;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--el-text-color-primary);
}

.gpu-tests {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.gpu-test-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
}

.test-label {
  min-width: 80px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

/* 复制NodeSystemSummary的样式 */
.summary-trigger {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  min-width: 180px;
  width: auto;
  flex-wrap: nowrap;
  border: 1px solid var(--el-border-color-light);
}

.summary-trigger:hover {
  background-color: var(--el-fill-color-light);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  min-width: 0;
  flex-shrink: 0;
  white-space: nowrap;
}

.summary-item .icon {
  font-size: 14px;
  display: inline-block;
  width: 16px;
  text-align: center;
}

/* GPU图标现在使用PNG文件，不再需要emoji样式 */

.summary-item .icon-img {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 2px;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.summary-trigger:hover .icon-img {
  opacity: 1;
}

.summary-item .value {
  font-weight: 500;
}

.summary-item .value.usage-high {
  color: var(--el-color-danger);
}

.summary-item .value.usage-medium {
  color: var(--el-color-warning);
}

.summary-item .value.usage-low {
  color: var(--el-color-success);
}

.summary-status {
  display: flex;
  align-items: center;
}
</style>
