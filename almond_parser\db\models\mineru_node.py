# -*- encoding: utf-8 -*-
"""
MinerU 节点管理数据模型
"""
import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import String, DateTime, Enum as SQLEnum, Integer, Text, JSON, Boolean, func, Column
from almond_parser.db.database import Base


class NodeStatus(str, enum.Enum):
    """节点状态枚举"""
    ONLINE = "online"  # 在线
    OFFLINE = "offline"  # 离线
    BUSY = "busy"  # 繁忙
    ERROR = "error"  # 错误


class ParseMode(str, enum.Enum):
    """解析模式枚举"""
    PIPELINE = "pipeline"  # Pipeline 模式
    SGLANG = "sglang"  # SGLang 模式
    AUTO = "auto"  # 自动选择


class ServiceType(str, enum.Enum):
    """节点服务类型枚举"""
    DOCUMENT = "document"  # 文档解析专用
    KNOWLEDGE_BASE = "knowledge_base"  # 知识库解析专用
    UNIVERSAL = "universal"  # 通用节点（支持所有类型）


class SglangStatus(str, enum.Enum):
    """SGLang服务状态枚举"""
    ONLINE = "ONLINE"  # 在线
    OFFLINE = "OFFLINE"  # 离线
    ERROR = "ERROR"  # 错误
    UNKNOWN = "UNKNOWN"  # 未知（未检测）
    RESTARTING = "RESTARTING"  # 重启中


class TaskStatus(str, enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 完成
    FAILED = "failed"  # 失败
    RETRY = "retry"  # 重试中


class MinerUNode(Base):
    """MinerU 节点信息表"""
    __tablename__ = "mineru_nodes"

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, comment="节点名称")
    host = Column(String(255), nullable=False, comment="节点主机地址")
    port = Column(Integer, nullable=False, comment="节点端口")
    base_url = Column(String(500), nullable=False, unique=True, comment="节点完整URL")

    # 节点配置
    parse_mode = Column(SQLEnum(ParseMode), nullable=False, comment="支持的解析模式")
    service_type = Column(SQLEnum(ServiceType), default=ServiceType.UNIVERSAL, comment="节点服务类型")
    max_concurrent_tasks = Column(Integer, default=3, comment="最大并发任务数")
    priority = Column(Integer, default=1, comment="节点优先级(数字越大优先级越高)")

    # 状态信息
    status = Column(SQLEnum(NodeStatus), default=NodeStatus.OFFLINE, comment="节点状态")
    current_tasks = Column(Integer, default=0, comment="当前任务数")
    reserved_tasks = Column(Integer, default=0, comment="预留任务数")
    total_tasks = Column(Integer, default=0, comment="总任务数")
    success_tasks = Column(Integer, default=0, comment="成功任务数")
    failed_tasks = Column(Integer, default=0, comment="失败任务数")
    last_task_assigned_at = Column(DateTime, nullable=True, comment="最后分配任务时间")

    # 健康检查
    last_health_check = Column(DateTime, nullable=True, comment="最后健康检查时间")
    health_check_interval = Column(Integer, default=60, comment="健康检查间隔(秒)")
    consecutive_failures = Column(Integer, default=0, comment="连续失败次数")

    # SGLang服务监控
    sglang_status = Column(SQLEnum(SglangStatus), default=SglangStatus.UNKNOWN, comment="SGLang服务状态")
    sglang_port = Column(Integer, nullable=True, comment="SGLang服务端口")
    sglang_url = Column(String(500), nullable=True, comment="SGLang服务URL")
    sglang_last_check = Column(DateTime, nullable=True, comment="SGLang最后检查时间")
    sglang_consecutive_failures = Column(Integer, default=0, comment="SGLang连续失败次数")
    sglang_last_restart = Column(DateTime, nullable=True, comment="SGLang最后重启时间")
    sglang_restart_count = Column(Integer, default=0, comment="SGLang重启次数")

    # SGLang重启周期管理
    sglang_current_cycle_restarts = Column(Integer, default=0, comment="当前重启周期的重启次数")
    sglang_restart_cycle_start = Column(DateTime, nullable=True, comment="重启周期开始时间")
    sglang_alert_sent = Column(Boolean, default=False, comment="是否已发送告警")
    sglang_alert_sent_at = Column(DateTime, nullable=True, comment="告警发送时间")

    # 认证信息
    auth_token = Column(String(500), nullable=True, comment="认证令牌")
    username = Column(String(100), nullable=True, comment="认证用户名")
    password = Column(String(500), nullable=True, comment="认证密码")

    # 节点信息
    version = Column(String(50), nullable=True, comment="节点版本")
    description = Column(Text, nullable=True, comment="节点描述")
    tags = Column(JSON, nullable=True, comment="节点标签")

    # 时间戳
    is_enabled = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<MinerUNode(id={self.id}, name='{self.name}', base_url='{self.base_url}', status='{self.status}')>"


class ParseTask(Base):
    """解析任务记录表"""
    __tablename__ = "parse_tasks"

    id = Column(Integer, primary_key=True)
    task_id = Column(String(100), unique=True, nullable=False, comment="任务ID")

    # 关联信息
    node_id = Column(Integer, nullable=True, comment="执行节点ID")
    user_id = Column(String(255), nullable=False, comment="用户ID")
    document_id = Column(String(100), nullable=True, comment="文档ID")

    # 任务信息
    filename = Column(String(255), nullable=False, comment="文件名")
    file_size = Column(Integer, nullable=False, comment="文件大小")
    file_type = Column(String(50), nullable=False, comment="文件类型")

    # 解析配置
    parse_mode = Column(SQLEnum(ParseMode), nullable=False, comment="解析模式")
    parse_config = Column(JSON, nullable=True, comment="解析配置")

    # 状态信息
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    progress = Column(Integer, nullable=True, comment="进度百分比")
    error_message = Column(Text, nullable=True, comment="错误信息")

    # 重试信息
    retry_count = Column(Integer, default=0, comment="重试次数")
    max_retries = Column(Integer, default=2, comment="最大重试次数")
    retry_nodes = Column(JSON, nullable=True, comment="已尝试的节点列表")

    # 结果信息
    result_data = Column(JSON, nullable=True, comment="解析结果")
    output_path = Column(String(500), nullable=True, comment="输出路径")

    # 时间信息
    started_at = Column(DateTime, nullable=True, comment="开始时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<ParseTask(id={self.id}, task_id='{self.task_id}', status='{self.status}')>"


class NodeHealthCheck(Base):
    """节点健康检查记录表"""
    __tablename__ = "node_health_checks"

    id = Column(Integer, primary_key=True)
    node_id = Column(Integer, nullable=False, comment="节点ID")

    # 检查结果
    is_healthy = Column(Boolean, nullable=False, comment="是否健康")
    response_time = Column(Integer, nullable=True, comment="响应时间(毫秒)")
    error_message = Column(Text, nullable=True, comment="错误信息")

    # 节点信息快照
    node_status = Column(String(50), nullable=True, comment="节点状态快照")
    node_version = Column(String(50), nullable=True, comment="节点版本快照")
    current_tasks = Column(Integer, nullable=True, comment="当前任务数快照")

    # 新增字段：服务器类型和扩展信息
    server_type = Column(String(50), nullable=True, comment="服务器类型(litserver/custom/unknown)")
    sglang_available = Column(Boolean, nullable=True, comment="sglang是否可用")
    detected_parse_mode = Column(String(50), nullable=True, comment="检测到的解析模式")
    raw_response = Column(Text, nullable=True, comment="原始响应内容(用于调试)")

    # 检查时间
    checked_at = Column(DateTime, default=func.now(), comment="检查时间")

    def __repr__(self):
        return f"<NodeHealthCheck(id={self.id}, node_id={self.node_id}, is_healthy={self.is_healthy}, server_type={self.server_type})>"
