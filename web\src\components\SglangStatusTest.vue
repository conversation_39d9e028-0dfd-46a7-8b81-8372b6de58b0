<template>
  <div class="sglang-status-test">
    <h3>SGLang状态显示测试</h3>
    
    <div class="test-section">
      <h4>主界面表格显示效果</h4>
      <div class="table-simulation">
        <div class="table-header">
          <div class="col">节点名称</div>
          <div class="col">SGLang状态</div>
        </div>
        
        <div class="table-row" v-for="node in testNodes" :key="node.id">
          <div class="col">{{ node.name }}</div>
          <div class="col">
            <div class="sglang-status-cell">
              <el-tooltip 
                :content="`健康检查失败${(node.sglang_consecutive_failures || 0)}次，重启${(node.sglang_restart_count || 0)}次`"
                placement="top"
                :disabled="(node.sglang_consecutive_failures || 0) === 0 && (node.sglang_restart_count || 0) === 0"
              >
                <div class="sglang-status-wrapper">
                  <el-tag :type="getStatusTagType(node.sglang_status)" size="small">
                    {{ getStatusText(node.sglang_status) }}
                  </el-tag>
                  <el-icon 
                    v-if="(node.sglang_consecutive_failures || 0) > 0 || (node.sglang_restart_count || 0) > 0"
                    class="status-info-icon"
                    size="12"
                  >
                    <InfoFilled />
                  </el-icon>
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>不同状态展示</h4>
      <div class="status-examples">
        <div class="example-item" v-for="example in statusExamples" :key="example.name">
          <div class="example-label">{{ example.name }}</div>
          <div class="example-status">
            <el-tooltip 
              :content="example.tooltip"
              placement="top"
              :disabled="!example.showTooltip"
            >
              <div class="sglang-status-wrapper">
                <el-tag :type="example.tagType" size="small">
                  {{ example.statusText }}
                </el-tag>
                <el-icon 
                  v-if="example.showIcon"
                  class="status-info-icon"
                  size="12"
                >
                  <InfoFilled />
                </el-icon>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h4>交互说明</h4>
      <div class="instructions">
        <div class="instruction-item">
          <el-icon class="instruction-icon"><InfoFilled /></el-icon>
          <span>当SGLang状态有失败或重启记录时，会显示信息图标</span>
        </div>
        <div class="instruction-item">
          <el-icon class="instruction-icon"><Mouse /></el-icon>
          <span>鼠标悬浮在状态标签上可查看详细的失败和重启次数</span>
        </div>
        <div class="instruction-item">
          <el-icon class="instruction-icon"><View /></el-icon>
          <span>信息图标会在悬浮时高亮，提醒用户查看Tooltip</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { InfoFilled, Mouse, View } from '@element-plus/icons-vue'

// 测试节点数据
const testNodes = ref([
  {
    id: 1,
    name: '正常节点',
    sglang_status: 'ONLINE',
    sglang_consecutive_failures: 0,
    sglang_restart_count: 0
  },
  {
    id: 2,
    name: '失败节点',
    sglang_status: 'ERROR',
    sglang_consecutive_failures: 5,
    sglang_restart_count: 2
  },
  {
    id: 3,
    name: '离线节点',
    sglang_status: 'OFFLINE',
    sglang_consecutive_failures: 3,
    sglang_restart_count: 1
  },
  {
    id: 4,
    name: 'None值节点',
    sglang_status: 'UNKNOWN',
    sglang_consecutive_failures: null,
    sglang_restart_count: null
  }
])

// 状态示例
const statusExamples = ref([
  {
    name: '正常状态',
    statusText: '在线',
    tagType: 'success',
    tooltip: '健康检查失败0次，重启0次',
    showTooltip: false,
    showIcon: false
  },
  {
    name: '有失败记录',
    statusText: '错误',
    tagType: 'danger',
    tooltip: '健康检查失败5次，重启2次',
    showTooltip: true,
    showIcon: true
  },
  {
    name: '仅有重启',
    statusText: '在线',
    tagType: 'success',
    tooltip: '健康检查失败0次，重启1次',
    showTooltip: true,
    showIcon: true
  },
  {
    name: 'None值处理',
    statusText: '未知',
    tagType: 'info',
    tooltip: '健康检查失败0次，重启0次',
    showTooltip: false,
    showIcon: false
  }
])

// 状态相关方法
const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    ONLINE: 'success',
    OFFLINE: 'danger',
    ERROR: 'danger',
    UNKNOWN: 'info',
    RESTARTING: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    ONLINE: '在线',
    OFFLINE: '离线',
    ERROR: '错误',
    UNKNOWN: '未知',
    RESTARTING: '重启中'
  }
  return texts[status] || status
}
</script>

<style scoped>
.sglang-status-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}

.table-simulation {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: var(--el-fill-color-light);
  font-weight: bold;
}

.table-row {
  display: flex;
  border-top: 1px solid var(--el-border-color-lighter);
}

.table-row:hover {
  background: var(--el-fill-color-extra-light);
}

.col {
  flex: 1;
  padding: 12px;
  display: flex;
  align-items: center;
}

.col:not(:last-child) {
  border-right: 1px solid var(--el-border-color-lighter);
}

.status-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.example-item {
  padding: 15px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-fill-color-extra-light);
}

.example-label {
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--el-text-color-primary);
}

.example-status {
  display: flex;
  justify-content: center;
}

.instructions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: var(--el-fill-color-extra-light);
  border-radius: 6px;
}

.instruction-icon {
  color: var(--el-color-primary);
  font-size: 16px;
}

/* 复制主组件的样式 */
.sglang-status-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sglang-status-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-info-icon {
  color: var(--el-color-info);
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.status-info-icon:hover {
  opacity: 1;
  color: var(--el-color-primary);
}
</style>
