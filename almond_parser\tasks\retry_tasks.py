# -*- encoding: utf-8 -*-
"""
重试任务处理 - 定时轮询和处理需要重试的文档
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any

from loguru import logger
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus, DocumentLog
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
from almond_parser.services.document_service import DocumentService
from almond_parser.utils.node_selector import NodeSelector


async def process_retry_documents(ctx: Dict[str, Any]) -> Dict[str, Any]:
    """
    定时处理需要重试的文档

    这个任务会：
    1. 查找所有需要重试的文档
    2. 检查重试条件
    3. 重新提交解析任务
    """
    logger.info("开始处理重试文档...")

    processed_count = 0
    failed_count = 0

    async with get_async_session() as db:
        try:
            # 查找需要重试的文档
            retry_documents = await _find_retry_documents(db)

            logger.info(f"找到 {len(retry_documents)} 个需要重试的文档")

            for document in retry_documents:
                try:
                    success = await _process_single_retry(db, document)
                    if success:
                        processed_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    logger.error(f"处理重试文档失败: {document.document_id}, 错误: {e}")
                    failed_count += 1

                    # 记录错误日志
                    await _log_retry_event(
                        db, document.document_id, "ERROR",
                        f"重试处理失败: {str(e)}"
                    )

            logger.info(f"重试任务完成: 成功 {processed_count}, 失败 {failed_count}")

            return {
                "success": True,
                "processed_count": processed_count,
                "failed_count": failed_count,
                "total_found": len(retry_documents)
            }

        except Exception as e:
            logger.error(f"重试任务异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }


async def _find_retry_documents(db: AsyncSession) -> List[Document]:
    """查找需要重试的文档"""
    current_time = datetime.now()

    # 查找条件：
    # 1. 状态为 RETRY_PENDING
    # 2. 重试时间已到
    # 3. 重试次数未超过最大限制
    result = await db.execute(
        select(Document)
        .where(
            and_(
                Document.status == DocumentStatus.RETRY_PENDING,
                or_(
                    Document.next_retry_at.is_(None),
                    Document.next_retry_at <= current_time
                ),
                Document.retry_count < Document.max_retries
            )
        )
        .order_by(Document.next_retry_at.asc())
        .limit(50)  # 每次最多处理50个
    )

    return result.scalars().all()


async def _process_single_retry(db: AsyncSession, document: Document) -> bool:
    """处理单个重试文档"""
    try:
        logger.info(f"处理重试文档: {document.document_id}, 原因: {document.retry_reason}")

        # 检查是否有可用节点
        node_selector = NodeSelector(db)

        # 根据文档的服务类型和解析模式选择节点
        service_type = "auto"  # 可以从文档配置中获取
        parse_mode = document.current_parse_mode or "auto"

        # 使用增强的节点选择器检查可用性
        node = await node_selector.select_node(
            service_type=service_type,
            parse_mode=parse_mode,
            use_compatibility=True
        )

        if not node:
            # 仍然没有可用节点，延迟重试
            await _schedule_next_retry(db, document, "no_nodes_available")
            return False

        # 更新重试计数
        document.retry_count += 1
        document.next_retry_at = None

        # 重新提交任务
        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        job_id = await arq_manager.enqueue_task(
            "enhanced_process_document",
            document_id=document.document_id,
            user_id=document.user_id,
            service_type=service_type,
            parse_mode=parse_mode,
            config=document.result_data or {}
        )

        # 更新文档状态
        document.status = DocumentStatus.QUEUED
        document.is_system_retry = False
        document.retry_reason = None

        await db.commit()

        await _log_retry_event(
            db, document.document_id, "INFO",
            f"重试任务已提交: {job_id}, 重试次数: {document.retry_count}",
            {
                "job_id": job_id,
                "retry_count": document.retry_count,
                "parse_mode": parse_mode,
                "selected_node": node.name
            }
        )

        logger.info(f"重试任务提交成功: {document.document_id}, job_id: {job_id}")
        return True

    except Exception as e:
        logger.error(f"处理重试文档异常: {document.document_id}, 错误: {e}")

        # 检查是否超过最大重试次数
        if document.retry_count >= document.max_retries:
            document.status = DocumentStatus.FAILED
            document.error_message = f"重试次数超过限制: {str(e)}"

            await _log_retry_event(
                db, document.document_id, "ERROR",
                f"重试次数超过限制，标记为失败: {str(e)}"
            )
        else:
            # 安排下次重试
            await _schedule_next_retry(db, document, f"retry_error: {str(e)}")

        await db.commit()
        return False


async def _schedule_next_retry(
        db: AsyncSession,
        document: Document,
        reason: str
) -> None:
    """安排下次重试"""
    # 计算下次重试时间（指数退避）
    base_delay = 5  # 基础延迟5分钟
    retry_delay = base_delay * (2 ** document.retry_count)  # 指数退避
    max_delay = 60  # 最大延迟60分钟

    delay_minutes = min(retry_delay, max_delay)
    next_retry_time = datetime.now() + timedelta(minutes=delay_minutes)

    document.next_retry_at = next_retry_time
    document.retry_reason = reason

    await _log_retry_event(
        db, document.document_id, "INFO",
        f"安排下次重试: {next_retry_time}, 原因: {reason}",
        {
            "next_retry_at": next_retry_time.isoformat(),
            "delay_minutes": delay_minutes,
            "retry_count": document.retry_count
        }
    )


async def cleanup_old_retry_records(ctx: Dict[str, Any]) -> Dict[str, Any]:
    """
    清理过期的重试记录

    清理超过最大重试次数且状态仍为 RETRY_PENDING 的文档
    """
    logger.info("开始清理过期重试记录...")

    async with get_async_session() as db:
        try:
            # 查找超过最大重试次数的文档
            result = await db.execute(
                select(Document)
                .where(
                    and_(
                        Document.status == DocumentStatus.RETRY_PENDING,
                        Document.retry_count >= Document.max_retries
                    )
                )
            )

            expired_documents = result.scalars().all()
            cleaned_count = 0

            for document in expired_documents:
                document.status = DocumentStatus.FAILED
                document.error_message = "重试次数超过限制"

                await _log_retry_event(
                    db, document.document_id, "WARNING",
                    f"重试次数超过限制，标记为失败: {document.retry_count}/{document.max_retries}"
                )

                cleaned_count += 1

            await db.commit()

            logger.info(f"清理完成: {cleaned_count} 个过期重试记录")

            return {
                "success": True,
                "cleaned_count": cleaned_count
            }

        except Exception as e:
            logger.error(f"清理过期重试记录失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }


async def _log_retry_event(
        db: AsyncSession,
        document_id: str,
        level: str,
        message: str,
        extra_data: Dict[str, Any] = None
) -> None:
    """记录重试事件日志"""
    try:
        log_entry = DocumentLog(
            document_id=document_id,
            level=level,
            message=message,
            source="retry_tasks",
            extra_data=extra_data
        )
        db.add(log_entry)
        await db.commit()
    except Exception as e:
        logger.error(f"记录重试日志失败: {e}")


# ARQ 定时任务配置
async def schedule_retry_tasks():
    """安排定时重试任务"""
    try:
        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        # 每5分钟检查一次重试文档
        await arq_manager.enqueue_task(
            "process_retry_documents",
            _defer_by=timedelta(minutes=5)
        )

        # 每小时清理一次过期记录
        await arq_manager.enqueue_task(
            "cleanup_old_retry_records",
            _defer_by=timedelta(hours=1)
        )

        logger.info("定时重试任务已安排")

    except Exception as e:
        logger.error(f"安排定时重试任务失败: {e}")
