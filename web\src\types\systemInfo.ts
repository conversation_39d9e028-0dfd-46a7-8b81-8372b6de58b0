// 系统信息类型定义

export interface SystemInfo {
  platform: string
  platform_release: string
  platform_version: string
  architecture: string
  hostname: string
  processor: string
  python_version: string
}

export interface CpuInfo {
  physical_cores: number
  total_cores: number
  max_frequency: number | null
  min_frequency: number | null
  current_frequency: number | null
  cpu_usage: number
  cpu_usage_per_core: number[]
}

export interface MemoryInfo {
  total: number  // GB
  available: number  // GB
  used: number  // GB
  percentage: number
}

export interface DiskInfo {
  device: string
  mountpoint: string
  file_system: string
  total: number  // GB
  used: number  // GB
  free: number  // GB
  percentage: number
}

export interface GpuInfo {
  index: number
  name: string
  memory_total_mb: number
  memory_used_mb: number
  memory_free_mb: number
  utilization_gpu: number
  utilization_memory: number
  temperature: number | null
  power_draw: number | null
  power_limit: number | null
}

export interface NetworkInterface {
  name: string
  addresses: Array<{
    type: string
    address: string
    netmask: string
  }>
  mac_address?: string
  is_up: boolean
  speed: number | null
}

export interface NodeSystemInfo {
  system: SystemInfo
  cpu: CpuInfo
  memory: MemoryInfo
  disk: DiskInfo[]
  gpu: GpuInfo[]
  network: NetworkInterface[]
  timestamp: number
}

export interface SystemResources {
  cpu: {
    usage_percent: number
    usage_per_core: number[]
    load_avg: number[] | null
  }
  memory: {
    total_gb: number
    used_gb: number
    available_gb: number
    usage_percent: number
  }
  disk_io: {
    read_bytes: number
    write_bytes: number
    read_count: number
    write_count: number
  } | null
  network_io: {
    bytes_sent: number
    bytes_recv: number
    packets_sent: number
    packets_recv: number
  } | null
  processes: {
    total_count: number
  }
  gpu: Array<{
    index: number
    utilization_gpu: number
    utilization_memory: number
    memory_used_mb: number
    memory_total_mb: number
    temperature: number | null
  }>
  timestamp: number
}

export interface NodeSystemInfoResponse {
  success: boolean
  node_id: number
  node_name: string
  system_info: NodeSystemInfo
}

export interface NodeSystemResourcesResponse {
  success: boolean
  node_id: number
  node_name: string
  resources: SystemResources
}
