<template>
  <div class="node-system-summary">
    <el-popover
      placement="right"
      :width="600"
      trigger="hover"
      :disabled="!hasData"
      popper-style="max-height: 500px; overflow-y: auto;"
    >
      <template #reference>
        <div class="summary-trigger" @click="refreshData">
          <!-- 节点禁用状态 -->
          <div v-if="props.isEnabled === false" class="disabled-summary">
            <span class="disabled-text">已禁用</span>
            <el-icon color="#C0C4CC"><QuestionFilled /></el-icon>
          </div>
          <!-- 正常状态 -->
          <template v-else>
            <div class="summary-item">
              <img src="/CPU.png" alt="CPU" class="icon-img" />
              <span class="value" :class="getCpuClass()">{{ cpuUsage }}%</span>
            </div>
            <div class="summary-item">
              <img src="/Memory.png" alt="Memory" class="icon-img" />
              <span class="value" :class="getMemoryClass()">{{ memoryUsage }}%</span>
            </div>
            <div class="summary-item" v-if="gpuCount > 0">
              <img src="/GPU.png" alt="GPU" class="icon-img" />
              <span class="value" :class="getGpuMemoryUsageClass()">{{ getGpuMemoryUsage() }}%</span>
            </div>
            <div class="summary-status">
              <el-icon v-if="loading" class="is-loading"><Loading /></el-icon>
              <el-icon v-else-if="hasError" color="#F56C6C"><Warning /></el-icon>
              <el-icon v-else-if="hasData" color="#67C23A"><SuccessFilled /></el-icon>
              <el-icon v-else color="#909399"><QuestionFilled /></el-icon>
            </div>
          </template>
        </div>
      </template>

      <!-- 悬浮详情 -->
      <div class="system-details" v-if="hasData">
        <div class="detail-section">
          <h4>💻 系统信息</h4>
          <div class="detail-item">
            <span class="label">主机:</span>
            <span class="value">{{ systemInfo?.system.hostname || 'N/A' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">系统:</span>
            <span class="value">{{ systemInfo?.system.platform || 'N/A' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>⚡ CPU</h4>
          <div class="detail-item">
            <span class="label">核心:</span>
            <span class="value">{{ systemInfo?.cpu.physical_cores || 0 }}物理 / {{ systemInfo?.cpu.total_cores || 0 }}逻辑</span>
          </div>
          <div class="detail-item">
            <span class="label">使用率:</span>
            <span class="value" :class="getCpuClass()">{{ cpuUsage }}%</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>💾 内存</h4>
          <div class="detail-item">
            <span class="label">总量:</span>
            <span class="value">{{ systemInfo?.memory.total.toFixed(1) || 0 }}GB</span>
          </div>
          <div class="detail-item">
            <span class="label">使用:</span>
            <span class="value" :class="getMemoryClass()">
              {{ (resources?.memory.used_gb || 0).toFixed(1) }}GB ({{ memoryUsage }}%)
            </span>
          </div>
        </div>

        <div class="detail-section" v-if="systemInfo?.gpu && systemInfo.gpu.length > 0">
          <h4><img src="/GPU.png" alt="GPU" class="section-icon" /> GPU</h4>
          <div v-for="gpu in systemInfo.gpu" :key="gpu.index" class="gpu-item">
            <div class="gpu-header-compact">
              <span class="gpu-name-compact">GPU{{ gpu.index }}</span>
              <span class="gpu-usage" :class="getGpuMemoryUsageClassForGpu(gpu)">
                {{ getGpuMemoryUsageForGpu(gpu) }}%
              </span>
            </div>
            <div class="gpu-details">
              <div class="gpu-detail-row">
                <span class="gpu-memory">
                  {{ formatGpuMemory(gpu.memory_used_mb, gpu.memory_total_mb) }}
                </span>
                <span v-if="gpu.temperature" class="gpu-temp">
                  {{ gpu.temperature }}°C
                </span>
              </div>
              <div class="gpu-detail-row">
                <span class="gpu-compute-usage">
                  计算: {{ getGpuUsage(gpu.index) }}%
                </span>
                <span v-if="gpu.power_draw" class="gpu-power">
                  {{ gpu.power_draw.toFixed(1) }}W
                </span>
              </div>
              <div class="gpu-name-full" :title="gpu.name">
                {{ gpu.name.length > 25 ? gpu.name.substring(0, 25) + '...' : gpu.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="update-info">
          <el-text type="info" size="small">
            更新: {{ formatTime(lastUpdateTime) }}
          </el-text>
        </div>
      </div>

      <div v-else-if="loading" class="loading-content">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>

      <div v-else-if="hasError" class="error-content">
        <el-icon color="#F56C6C"><Warning /></el-icon>
        <span>加载失败</span>
        <el-button type="text" size="small" @click="refreshData">重试</el-button>
      </div>

      <div v-else-if="props.isEnabled === false" class="disabled-content">
        <el-icon color="#C0C4CC"><QuestionFilled /></el-icon>
        <span>节点已禁用</span>
      </div>

      <div v-else class="no-data-content">
        <el-icon color="#909399"><QuestionFilled /></el-icon>
        <span>暂无数据</span>
        <el-button type="text" size="small" @click="refreshData">加载</el-button>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Loading, Warning, SuccessFilled, QuestionFilled
} from '@element-plus/icons-vue'
import { mineruNodeApi } from '@/api/mineruNode'
import type { NodeSystemInfo, SystemResources } from '@/types/systemInfo'

const props = defineProps<{
  nodeId: number
  autoRefresh?: boolean
  isEnabled?: boolean
}>()

const loading = ref(false)
const hasError = ref(false)
const systemInfo = ref<NodeSystemInfo | null>(null)
const resources = ref<SystemResources | null>(null)
const lastUpdateTime = ref<Date | null>(null)
const refreshTimer = ref<number | null>(null)

// 计算属性
const hasData = computed(() => systemInfo.value && resources.value)

const cpuUsage = computed(() => {
  return resources.value?.cpu.usage_percent?.toFixed(1) || '0.0'
})

const memoryUsage = computed(() => {
  return resources.value?.memory.usage_percent?.toFixed(1) || '0.0'
})

const gpuCount = computed(() => {
  return systemInfo.value?.gpu?.length || 0
})

// 计算GPU内存使用率（平均值）
const getGpuMemoryUsage = () => {
  if (!systemInfo.value?.gpu || systemInfo.value.gpu.length === 0) {
    return '0.0'
  }

  let totalUsage = 0
  let validGpuCount = 0

  systemInfo.value.gpu.forEach(gpu => {
    if (gpu.memory_total_mb > 0) {
      const usage = (gpu.memory_used_mb / gpu.memory_total_mb) * 100
      totalUsage += usage
      validGpuCount++
    }
  })

  if (validGpuCount === 0) return '0.0'

  const avgUsage = totalUsage / validGpuCount
  return avgUsage.toFixed(1)
}

// 获取GPU内存使用率样式类
const getGpuMemoryUsageClass = () => {
  const usage = parseFloat(getGpuMemoryUsage())
  if (usage >= 85) return 'usage-high'
  if (usage >= 70) return 'usage-medium'
  return 'usage-low'
}

// 获取单个GPU内存使用率
const getGpuMemoryUsageForGpu = (gpu: any) => {
  if (!gpu || gpu.memory_total_mb <= 0) return '0.0'
  const usage = (gpu.memory_used_mb / gpu.memory_total_mb) * 100
  return usage.toFixed(1)
}

// 获取单个GPU内存使用率样式类
const getGpuMemoryUsageClassForGpu = (gpu: any) => {
  const usage = parseFloat(getGpuMemoryUsageForGpu(gpu))
  if (usage >= 85) return 'usage-high'
  if (usage >= 70) return 'usage-medium'
  return 'usage-low'
}

// 获取数据
const refreshData = async () => {
  if (loading.value) return

  // 如果节点被禁用，不获取系统信息
  if (props.isEnabled === false) {
    systemInfo.value = null
    resources.value = null
    hasError.value = false
    return
  }

  loading.value = true
  hasError.value = false

  try {
    const [systemResponse, resourcesResponse] = await Promise.all([
      mineruNodeApi.getNodeSystemInfo(props.nodeId),
      mineruNodeApi.getNodeSystemResources(props.nodeId)
    ])

    if (systemResponse.success) {
      systemInfo.value = systemResponse.system_info
    }

    if (resourcesResponse.success) {
      resources.value = resourcesResponse.resources
      lastUpdateTime.value = new Date()
    }
  } catch (error) {
    console.error('获取系统信息失败:', error)
    hasError.value = true
  } finally {
    loading.value = false
  }
}

// 获取GPU使用率
const getGpuUsage = (gpuIndex: number) => {
  const gpuResource = resources.value?.gpu.find(g => g.index === gpuIndex)
  return gpuResource?.utilization_gpu || 0
}

// 样式类
const getCpuClass = () => {
  const usage = parseFloat(cpuUsage.value)
  if (usage >= 80) return 'usage-high'
  if (usage >= 60) return 'usage-medium'
  return 'usage-low'
}

const getMemoryClass = () => {
  const usage = parseFloat(memoryUsage.value)
  if (usage >= 85) return 'usage-high'
  if (usage >= 70) return 'usage-medium'
  return 'usage-low'
}

const getGpuUsageClass = (usage: number) => {
  if (usage >= 80) return 'usage-high'
  if (usage >= 50) return 'usage-medium'
  return 'usage-low'
}

// 格式化GPU内存显示
const formatGpuMemory = (usedMb: number, totalMb: number) => {
  const usedGb = usedMb / 1024
  const totalGb = totalMb / 1024

  // 如果小于1GB，显示MB
  if (totalGb < 1) {
    return `${usedMb}/${totalMb}MB`
  }

  // 如果是整数GB，不显示小数
  const usedStr = usedGb % 1 === 0 ? usedGb.toFixed(0) : usedGb.toFixed(1)
  const totalStr = totalGb % 1 === 0 ? totalGb.toFixed(0) : totalGb.toFixed(1)

  return `${usedStr}/${totalStr}GB`
}

// 格式化时间
const formatTime = (time: Date | null) => {
  if (!time) return 'N/A'
  return time.toLocaleTimeString()
}

// 自动刷新
const startAutoRefresh = () => {
  if (refreshTimer.value) return

  refreshTimer.value = window.setInterval(() => {
    // 只有启用的节点才自动刷新
    if (!loading.value && props.isEnabled !== false) {
      refreshData()
    }
  }, 30000) // 30秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

onMounted(() => {
  refreshData()
  
  if (props.autoRefresh) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.node-system-summary {
  display: inline-block;
}

.summary-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  min-width: 200px;
  width: auto;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.summary-trigger:hover {
  background-color: var(--el-fill-color-light);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  min-width: 0;
  flex-shrink: 0;
  white-space: nowrap;
}

.summary-item .icon {
  font-size: 14px;
  display: inline-block;
  width: 16px;
  text-align: center;
}

/* GPU图标现在使用PNG文件，不再需要emoji样式 */

.summary-item .icon-img {
  width: 16px;
  height: 16px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 2px;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.summary-trigger:hover .icon-img {
  opacity: 1;
}

.section-icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 4px;
  opacity: 0.8;
}

.summary-item .value {
  font-weight: 500;
}

.summary-item .value.usage-high {
  color: var(--el-color-danger);
}

.summary-item .value.usage-medium {
  color: var(--el-color-warning);
}

.summary-item .value.usage-low {
  color: var(--el-color-success);
}

.summary-status {
  display: flex;
  align-items: center;
  margin-left: auto;
  flex-shrink: 0;
}

.summary-status .el-icon {
  font-size: 12px;
}

/* 悬浮详情样式 */
.system-details {
  max-height: 400px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.detail-item .label {
  color: var(--el-text-color-regular);
}

.detail-item .value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.detail-item .value.usage-high {
  color: var(--el-color-danger);
}

.detail-item .value.usage-medium {
  color: var(--el-color-warning);
}

.detail-item .value.usage-low {
  color: var(--el-color-success);
}

.gpu-item {
  margin-bottom: 8px;
  padding: 6px 8px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.gpu-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.gpu-name-compact {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 12px;
}

.gpu-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.gpu-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.gpu-name-full {
  font-size: 10px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
  margin-top: 2px;
}

.gpu-temp {
  color: var(--el-text-color-secondary);
  font-size: 10px;
}

.gpu-compute-usage {
  color: var(--el-text-color-regular);
  font-size: 10px;
}

.gpu-power {
  color: var(--el-text-color-secondary);
  font-size: 10px;
}

.gpu-usage.usage-high {
  color: var(--el-color-danger);
}

.gpu-usage.usage-medium {
  color: var(--el-color-warning);
}

.gpu-usage.usage-low {
  color: var(--el-color-success);
}

.update-info {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--el-border-color-lighter);
  text-align: center;
}

.loading-content,
.error-content,
.no-data-content,
.disabled-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  text-align: center;
}

.disabled-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-disabled);
}

.disabled-text {
  font-size: 12px;
  color: var(--el-text-color-disabled);
}
</style>
