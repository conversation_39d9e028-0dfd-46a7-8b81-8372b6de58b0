# DOC文档下载配置示例

## 概述

本文档说明如何配置DOC文档的nginx代理下载功能。

## 配置项说明

### 配置文件修改

在 `almond_parser/config/doc_databases.yml` 文件中为每个服务器配置下载信息：

```yaml
databases:
  - name: "internal"
    label: "AI中心内部"
    host: "localhost"
    port: 3306
    user: "root"
    password: "123456"
    database: "bdo_xxr2"
    charset: "utf8mb4"
    # 下载配置
    download:
      base_url: "http://127.0.0.1"          # 下载基础URL
      docs_prefix: "/download-docs/"        # 多文档下载路径前缀
      kb_prefix: "/download-kb/"            # 知识库下载路径前缀
      kb_path_prefix: "/app/python/files/"  # 知识库文件路径前缀（用于截断）

  - name: "external"
    label: "AI中心外部"
    host: "************"
    port: 3306
    user: "root"
    password: "123456"
    database: "bdo_xxr2"
    charset: "utf8mb4"
    # 下载配置
    download:
      base_url: "http://************"       # 不同服务器可以有不同的下载URL
      docs_prefix: "/download-docs/"
      kb_prefix: "/download-kb/"
      kb_path_prefix: "/app/python/files/"
```

### nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 多文档下载代理
    location /download-docs/ {
        # 代理到多文档服务器
        proxy_pass http://127.0.0.1:8080/files/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 支持大文件下载
        proxy_buffering off;
        proxy_request_buffering off;
        client_max_body_size 1G;
    }

    # 知识库下载代理
    location /download-kb/ {
        # 代理到知识库服务器
        proxy_pass http://************:9000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 支持大文件下载
        proxy_buffering off;
        proxy_request_buffering off;
        client_max_body_size 1G;
    }
}
```

## 下载URL生成规则

### 多文档下载
- 从 `meta_info.temp_file_path` 获取文件路径
- 提取文件名部分
- 生成URL：`${DOWNLOAD_BASE_URL}${DOCS_DOWNLOAD_PREFIX}${filename}`

**示例：**
```
meta_info.temp_file_path: "/tmp/uploads/document.pdf"
生成URL: http://************/download-docs/document.pdf
```

### 知识库下载
- 从 `file_path` 获取完整路径
- 截断 `KB_FILE_PATH_PREFIX` 前缀
- 生成URL：`${DOWNLOAD_BASE_URL}${KB_DOWNLOAD_PREFIX}${relative_path}`

**示例：**
```
file_path: "/app/python/files/hu.zijian/v/新建 文本文档.txt"
截断前缀: "/app/python/files/"
相对路径: "hu.zijian/v/新建 文本文档.txt"
生成URL: http://************/download-kb/hu.zijian/v/新建 文本文档.txt
```

## 部署注意事项

1. **URL编码**：文件名包含中文或特殊字符时，浏览器会自动进行URL编码

2. **HTTPS支持**：生产环境建议使用HTTPS，修改 `DOWNLOAD_BASE_URL` 为 `https://` 开头

3. **跨域配置**：如果前端和下载服务不在同一域名，需要配置CORS

4. **文件权限**：确保nginx有权限访问文件存储目录

5. **降级机制**：如果nginx代理失败，系统会自动降级到API下载方式

## 测试验证

### 1. 配置验证
访问服务器列表接口验证配置是否正确：
```bash
curl -H "Authorization: Bearer your-token" \
     http://your-domain/api/doc-document/servers
```

检查返回的JSON中是否包含 `download` 配置项。

### 2. 下载测试
在DOC文档列表页面点击下载按钮，检查：
- 浏览器开发者工具中的网络请求
- 下载的文件是否正确
- 控制台是否有错误信息

### 3. 降级测试
临时修改配置（如错误的base_url），验证是否能正常降级到API下载

## 故障排查

### 常见问题

1. **404错误**：检查nginx代理配置和文件路径是否正确
2. **403错误**：检查文件权限和nginx访问权限
3. **下载失败**：检查文件是否存在，路径是否正确
4. **配置不生效**：重启服务，检查环境变量是否正确加载

### 日志查看
- nginx访问日志：`/var/log/nginx/access.log`
- nginx错误日志：`/var/log/nginx/error.log`
- 应用日志：检查后端服务日志
