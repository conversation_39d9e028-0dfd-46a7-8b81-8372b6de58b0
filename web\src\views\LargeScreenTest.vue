<template>
  <div class="large-screen-test">
    <div class="page-header">
      <h2>大屏幕表格测试</h2>
      <div class="screen-info">
        <el-tag :type="getScreenType()">{{ getScreenDescription() }}</el-tag>
        <span>{{ windowHeight }}px 高度</span>
      </div>
    </div>

    <div class="test-info">
      <el-alert
        :title="getRecommendation()"
        :type="getAlertType()"
        show-icon
        :closable="false"
      />
    </div>

    <div class="content-container">
      <div class="table-card">
        <div class="table-container">
          <el-table
            :data="testData"
            :max-height="calculatedTableHeight"
            border
            stripe
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="文档名称" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="文件大小" width="120" />
            <el-table-column prop="createTime" label="创建时间" width="180" />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" link>详情</el-button>
                <el-button type="warning" size="small" link>日志</el-button>
                <el-button type="success" size="small" link>重试</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              :current-page="1"
              :page-size="10"
              :page-sizes="[10, 20, 50]"
              :total="testData.length"
              layout="total, sizes, prev, pager, next, jumper"
              background
            />
          </div>
        </div>
      </div>
    </div>

    <div class="metrics-card">
      <h3>计算指标</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="视窗高度">
          {{ windowHeight }}px
        </el-descriptions-item>
        <el-descriptions-item label="可用高度">
          {{ availableHeight }}px
        </el-descriptions-item>
        <el-descriptions-item label="内容高度">
          {{ contentHeight }}px
        </el-descriptions-item>
        <el-descriptions-item label="最大允许高度">
          {{ maxAllowedHeight }}px
        </el-descriptions-item>
        <el-descriptions-item label="实际表格高度">
          {{ calculatedTableHeight }}px
        </el-descriptions-item>
        <el-descriptions-item label="空间利用率">
          {{ utilization }}%
        </el-descriptions-item>
        <el-descriptions-item label="数据行数">
          {{ testData.length }}行
        </el-descriptions-item>
        <el-descriptions-item label="是否需要滚动">
          {{ needsScroll ? '是' : '否' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

const windowHeight = ref(window.innerHeight)

// 生成测试数据
const testData = ref([
  { id: 1, name: '测试文档1.pdf', status: '已完成', size: '2.5MB', createTime: '2024-01-15 10:30:00' },
  { id: 2, name: '用户手册.docx', status: '处理中', size: '1.8MB', createTime: '2024-01-15 11:15:00' },
  { id: 3, name: '项目报告.pdf', status: '已完成', size: '4.2MB', createTime: '2024-01-15 12:00:00' },
  { id: 4, name: '技术文档.md', status: '失败', size: '0.5MB', createTime: '2024-01-15 13:20:00' },
  { id: 5, name: '设计稿.psd', status: '已完成', size: '15.6MB', createTime: '2024-01-15 14:10:00' },
  { id: 6, name: '会议纪要.txt', status: '等待', size: '0.2MB', createTime: '2024-01-15 15:30:00' },
  { id: 7, name: '产品规格书.pdf', status: '已完成', size: '3.1MB', createTime: '2024-01-15 16:45:00' },
  { id: 8, name: 'API文档.html', status: '处理中', size: '0.8MB', createTime: '2024-01-15 17:20:00' },
  { id: 9, name: '数据分析报告.xlsx', status: '已完成', size: '2.9MB', createTime: '2024-01-15 18:00:00' },
  { id: 10, name: '系统架构图.png', status: '已完成', size: '1.2MB', createTime: '2024-01-15 19:15:00' }
])

// 计算表格高度（与DocumentTable.vue保持一致）
const availableHeight = computed(() => windowHeight.value - 320)
const contentHeight = computed(() => testData.value.length * 60 + 60)

const maxAllowedHeight = computed(() => {
  const available = availableHeight.value
  if (windowHeight.value <= 768) {
    return Math.max(available * 0.8, 350)
  } else if (windowHeight.value <= 900) {
    return Math.max(available * 0.85, 400)
  } else if (windowHeight.value <= 1200) {
    return Math.max(available * 0.9, 500)
  } else {
    return Math.max(available * 0.95, 600)
  }
})

const calculatedTableHeight = computed(() => {
  const minHeight = 300
  return Math.min(Math.max(contentHeight.value, minHeight), maxAllowedHeight.value)
})

const utilization = computed(() => {
  return Math.round((calculatedTableHeight.value / availableHeight.value) * 100)
})

const needsScroll = computed(() => {
  return contentHeight.value > calculatedTableHeight.value
})

const getScreenType = () => {
  if (windowHeight.value <= 768) return 'info'
  if (windowHeight.value <= 900) return 'warning'
  if (windowHeight.value <= 1200) return 'success'
  return 'primary'
}

const getScreenDescription = () => {
  if (windowHeight.value <= 768) return '小屏幕'
  if (windowHeight.value <= 900) return '中小屏幕'
  if (windowHeight.value <= 1200) return '大屏幕'
  return '超大屏幕'
}

const getRecommendation = () => {
  if (windowHeight.value >= 1080 && needsScroll.value) {
    return '⚠️ 大屏幕设备上出现滚动条，建议增加空间利用率'
  }
  if (windowHeight.value >= 1080 && !needsScroll.value) {
    return '✅ 大屏幕空间利用良好，无需滚动'
  }
  if (utilization.value < 60) {
    return '💡 空间利用率较低，可以适当增加表格高度'
  }
  return '✅ 表格高度配置合理'
}

const getAlertType = () => {
  if (windowHeight.value >= 1080 && needsScroll.value) return 'warning'
  if (utilization.value < 60) return 'info'
  return 'success'
}

const getStatusType = (status: string) => {
  switch (status) {
    case '已完成': return 'success'
    case '失败': return 'danger'
    case '处理中': return 'warning'
    case '等待': return 'info'
    default: return 'info'
  }
}

const handleResize = () => {
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.large-screen-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.page-header h2 {
  margin: 0;
  color: #1e293b;
}

.screen-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.test-info {
  flex-shrink: 0;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0;
}

.table-card {
  background: transparent;
  display: flex;
  flex-direction: column;
}

.table-container {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.pagination-container {
  flex-shrink: 0;
  height: 60px;
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: #fafafa;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.02);
}

.metrics-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.metrics-card h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
}
</style>
