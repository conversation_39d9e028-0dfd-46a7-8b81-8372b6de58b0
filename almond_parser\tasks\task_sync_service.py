"""
任务状态同步服务 - 最小侵入性的解决方案
定期检查长时间未更新的任务，主动从 mineru-api 同步状态
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import aiohttp
from sqlalchemy import select, and_
from loguru import logger

from almond_parser.db import MinerUNode
from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus

from almond_parser.config import settings
from almond_parser.services.document_service import DocumentService


class TaskSyncService:
    """任务状态同步服务"""

    def __init__(self):
        self.sync_interval_minutes = 10  # 每10分钟检查一次
        self.stuck_task_threshold_minutes = 100  # 超过100分钟未更新的任务视为可能卡住
        self.max_sync_tasks_per_run = 20  # 每次最多同步20个任务

    async def sync_stuck_tasks(self) -> Dict[str, Any]:
        """同步卡住的任务状态并清理超时任务"""
        logger.info("🔄 开始同步可能卡住的任务状态...")

        sync_stats = {
            "checked_tasks": 0,
            "synced_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "timeout_tasks": 0,
            "cleaned_reservations": 0,
            "errors": []
        }

        async with get_async_session() as db:
            try:
                # 查找可能卡住的任务
                stuck_tasks = await self._find_stuck_tasks(db)
                sync_stats["checked_tasks"] = len(stuck_tasks)

                if not stuck_tasks:
                    logger.info("✅ 没有发现卡住的任务")
                    return sync_stats

                logger.info(f"🔍 发现 {len(stuck_tasks)} 个可能卡住的任务")

                # 限制每次处理的任务数量
                tasks_to_sync = stuck_tasks[:self.max_sync_tasks_per_run]

                for document in tasks_to_sync:
                    try:
                        result = await self._sync_single_task(db, document)
                        if result["synced"]:
                            sync_stats["synced_tasks"] += 1
                            if result["status"] == "completed":
                                sync_stats["completed_tasks"] += 1
                            elif result["status"] == "failed":
                                sync_stats["failed_tasks"] += 1

                    except Exception as e:
                        error_msg = f"同步任务失败 {document.document_id}: {str(e)}"
                        logger.error(error_msg)
                        sync_stats["errors"].append(error_msg)

                # 执行并发控制清理
                await self._perform_concurrency_cleanup(db, sync_stats)

                await db.commit()

                logger.info(f"✅ 任务状态同步完成: {sync_stats}")
                return sync_stats

            except Exception as e:
                await db.rollback()
                logger.error(f"❌ 任务状态同步失败: {e}")
                sync_stats["errors"].append(str(e))
                return sync_stats

    async def _find_stuck_tasks(self, db) -> List[Document]:
        """查找可能卡住的任务"""
        cutoff_time = datetime.now() - timedelta(minutes=self.stuck_task_threshold_minutes)

        result = await db.execute(
            select(Document)
            .where(
                and_(
                    Document.status == DocumentStatus.PARSING,
                    Document.task_id.isnot(None),  # 有 task_id 的任务
                    Document.updated_at < cutoff_time  # 超过阈值时间未更新
                )
            )
            .order_by(Document.updated_at.asc())  # 最久未更新的优先
        )

        return result.scalars().all()

    async def _sync_single_task(self, db, document: Document) -> Dict[str, Any]:
        """同步单个任务的状态"""
        result = {"synced": False, "status": None, "error": None}

        try:
            # 获取节点信息
            node = await self._get_document_node(db, document)
            if not node:
                logger.warning(f"文档 {document.document_id} 没有关联的节点信息")
                return result

            # 查询 mineru-api 的任务状态
            api_status = await self._query_mineru_api_status(node, document.task_id)
            if not api_status:
                logger.warning(f"无法从 mineru-api 获取任务状态: {document.task_id}")
                return result

            # 根据 API 返回的状态更新本地状态
            updated = await self._update_document_status(db, document, api_status)
            if updated:
                result["synced"] = True
                result["status"] = api_status.get("status")
                logger.info(f"✅ 已同步任务状态: {document.document_id} -> {api_status.get('status')}")

            return result

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"同步任务状态失败 {document.document_id}: {e}")
            return result

    async def _get_document_node(self, db, document: Document) -> Optional[MinerUNode]:
        """获取文档关联的节点"""
        if not document.node_id:
            return None

        result = await db.execute(
            select(MinerUNode).where(MinerUNode.id == document.node_id)
        )
        return result.scalar_one_or_none()

    async def _query_mineru_api_status(self, node: MinerUNode, task_id: str) -> Optional[Dict[str, Any]]:
        """查询 mineru-api 的任务状态"""
        try:
            url = f"{node.base_url}/tasks/{task_id}/status"
            headers = {"Authorization": f"Bearer {node.auth_token}"}

            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 404:
                        logger.warning(f"任务不存在于 mineru-api: {task_id}")
                        return {"status": "not_found"}
                    else:
                        logger.warning(f"查询任务状态失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"查询 mineru-api 状态失败: {e}")
            return None

    async def _update_document_status(self, db, document: Document, api_status: Dict[str, Any]) -> bool:
        """根据 API 状态更新文档状态"""
        api_task_status = api_status.get("status")

        if api_task_status == "completed":
            # 任务已完成，获取结果
            await self._handle_completed_task(db, document, api_status)
            return True

        elif api_task_status == "failed":
            # 任务失败
            document.status = DocumentStatus.FAILED
            document.error_message = api_status.get("error", "任务在 mineru-api 中失败")
            document.completed_at = datetime.now()

            # 记录日志
            await self._log_sync_event(
                db, document.document_id, "ERROR",
                f"从 mineru-api 同步到任务失败状态: {document.error_message}"
            )
            return True

        elif api_task_status == "not_found":
            # 任务在 mineru-api 中不存在，可能已被清理
            document.status = DocumentStatus.FAILED
            document.error_message = "任务在 mineru-api 中不存在"
            document.completed_at = datetime.now()

            await self._log_sync_event(
                db, document.document_id, "WARNING",
                "任务在 mineru-api 中不存在，可能已被清理"
            )
            return True

        elif api_task_status in ["pending", "processing"]:
            # 任务仍在处理中，更新时间戳
            document.updated_at = datetime.now()

            await self._log_sync_event(
                db, document.document_id, "INFO",
                f"任务仍在处理中: {api_task_status}"
            )
            return True

        return False

    async def _handle_completed_task(self, db, document: Document, api_status: Dict[str, Any]):
        """处理已完成的任务"""
        try:
            # 获取任务结果
            node = await self._get_document_node(db, document)
            if node:
                result_data = await self._get_task_result(node, document.task_id)
                if result_data:
                    # 使用现有的文档服务处理结果
                    document_service = DocumentService(db)
                    await document_service.update_document_result(document.task_id, result_data)

                    await self._log_sync_event(
                        db, document.document_id, "INFO",
                        "从 mineru-api 同步到任务完成状态并获取结果"
                    )
                    return

            # 如果无法获取结果，至少更新状态
            document.status = DocumentStatus.COMPLETED
            document.completed_at = datetime.now()

            await self._log_sync_event(
                db, document.document_id, "WARNING",
                "任务已完成但无法获取详细结果"
            )

        except Exception as e:
            logger.error(f"处理已完成任务失败 {document.document_id}: {e}")
            # 至少更新状态
            document.status = DocumentStatus.COMPLETED
            document.completed_at = datetime.now()

    async def _get_task_result(self, node: MinerUNode, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果"""
        try:
            url = f"{node.base_url}/tasks/{task_id}/result"
            headers = {"Authorization": f"Bearer {node.auth_token}"}

            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"获取任务结果失败: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"获取任务结果失败: {e}")
            return None

    async def _log_sync_event(self, db, document_id: str, level: str, message: str):
        """记录同步事件日志"""
        try:
            from almond_parser.db.models.document import DocumentLog

            log_entry = DocumentLog(
                document_id=document_id,
                level=level,
                message=message,
                source="task_sync_service"
            )
            db.add(log_entry)

        except Exception as e:
            logger.error(f"记录同步日志失败: {e}")

    async def _perform_concurrency_cleanup(self, db, sync_stats: Dict[str, Any]):
        """执行并发控制相关的清理操作"""
        try:
            from almond_parser.utils.node_concurrency_manager import node_concurrency_manager

            # 1. 清理超时任务（30分钟）
            timeout_stats = await node_concurrency_manager.cleanup_timeout_tasks(db)
            sync_stats["timeout_tasks"] = timeout_stats.get("timeout_tasks", 0)

            # 2. 清理过期预留（5分钟）
            reservation_stats = await node_concurrency_manager.cleanup_expired_reservations(db)
            sync_stats["cleaned_reservations"] = reservation_stats.get("released_reservations", 0)

            # 记录清理结果
            if timeout_stats.get("timeout_tasks", 0) > 0:
                logger.info(f"🧹 清理超时任务: {timeout_stats['timeout_tasks']} 个")

            if reservation_stats.get("released_reservations", 0) > 0:
                logger.info(f"🧹 清理过期预留: {reservation_stats['released_reservations']} 个")

            # 合并错误信息
            sync_stats["errors"].extend(timeout_stats.get("errors", []))
            sync_stats["errors"].extend(reservation_stats.get("errors", []))

        except Exception as e:
            error_msg = f"并发控制清理失败: {e}"
            logger.error(error_msg)
            sync_stats["errors"].append(error_msg)


# 全局服务实例
task_sync_service = TaskSyncService()


# ARQ 任务函数
async def sync_stuck_tasks(ctx: Dict[str, Any]) -> Dict[str, Any]:
    """ARQ 定时任务：同步卡住的任务状态"""
    try:
        result = await task_sync_service.sync_stuck_tasks()
        logger.info(f"定时任务状态同步完成: {result}")
        return result
    except Exception as e:
        logger.error(f"定时任务状态同步失败: {e}")
        raise
