import { StatusOption, StageOption } from '@/types/document'
import type { DocumentStatus } from '@/types/document'

// 状态选项
export const statusOptions: StatusOption[] = [
  { value: 'UPLOADING', label: '正在上传', type: 'info' },
  { value: 'UPLOADED', label: '已上传', type: 'warning' },
  { value: 'PARSING', label: '正在解析', type: 'warning' },
  { value: 'COMPLETED', label: '解析完成', type: 'success' },
  { value: 'FAILED', label: '失败', type: 'danger' }
]

// 处理阶段选项
export const stageOptions: StageOption[] = [
  { value: 'PENDING', label: '初始状态', type: 'info' },
  { value: 'PARSING', label: '正在解析', type: 'warning' },
  { value: 'PARSED', label: '解析成功', type: 'success' },
  { value: 'PUSHING', label: '正在上传MaxKB', type: 'warning' },
  { value: 'COMPLETED', label: '成功完成', type: 'success' },
  { value: 'FAILED_PARSE', label: '解析失败', type: 'danger' },
  { value: 'FAILED_PUSH', label: '推送失败', type: 'danger' }
]

type StatusType = 'success' | 'warning' | 'danger' | 'info'

/**
 * 获取文档状态对应的类型
 * @param status 文档状态
 * @returns Element Plus Tag 组件的类型
 */
export function getStatusType(status: DocumentStatus): 'info' | 'warning' | 'success' | 'danger' {
  const statusMap: Record<DocumentStatus, 'info' | 'warning' | 'success' | 'danger'> = {
    UPLOADING: 'info',
    UPLOADED: 'info',
    PARSING: 'warning',
    COMPLETED: 'success',
    FAILED: 'danger',
    RETRY_PENDING: 'warning',
    FALLBACK_RETRY: 'warning'
  }
  return statusMap[status] || 'info'
}

/**
 * 获取文档状态的显示文本
 * @param status 文档状态
 * @returns 状态显示文本
 */
export function getStatusText(status: DocumentStatus): string {
  const statusMap: Record<DocumentStatus, string> = {
    UPLOADING: '上传中',
    UPLOADED: '已上传',
    PARSING: '解析中',
    COMPLETED: '已完成',
    FAILED: '失败',
    RETRY_PENDING: '等待重试',
    FALLBACK_RETRY: '降级重试'
  }
  return statusMap[status] || status
}

/**
 * 获取处理阶段对应的类型
 * @param stage 处理阶段
 * @returns Element Plus Tag 组件的类型
 */
export function getProcessingStageType(stage: string | undefined): StatusType {
  if (!stage) return 'info'
  
  switch (stage) {
    case 'COMPLETED':
      return 'success'
    case 'PROCESSING':
      return 'warning'
    case 'FAILED':
      return 'danger'
    default:
      return 'info'
  }
}

/**
 * 获取处理阶段的显示文本
 * @param stage 处理阶段
 * @returns 阶段显示文本
 */
export function getProcessingStageText(stage: string | undefined): string {
  if (!stage) return '未知'
  
  const stageMap: Record<string, string> = {
    WAITING: '等待处理',
    PROCESSING: '处理中',
    COMPLETED: '已完成',
    FAILED: '失败'
  }
  
  return stageMap[stage] || '未知'
}

// 判断是否为进行中状态
export function isProcessing(status: DocumentStatus): boolean {
  return [
    'UPLOADING',
    'UPLOADED',
    'PARSING',
    'RETRY_PENDING',
    'FALLBACK_RETRY'
  ].includes(status)
}

// 判断是否可以重试
export function canRetry(status: DocumentStatus): boolean {
  return status === 'FAILED'
} 