# CUDA 配置指南

## 问题解决

### FileNotFoundError 问题

**问题描述**：通过 `start_server.py` 启动时出现 `FileNotFoundError`，但直接执行命令可以成功。

**原因**：之前的代码错误地将环境变量 `CUDA_VISIBLE_DEVICES=1` 作为命令参数，导致 subprocess 无法找到该"命令"。

**解决方案**：已修复命令执行方式，现在通过 `subprocess.Popen` 的 `env` 参数正确设置环境变量。

## CUDA 设备配置

### 配置选项

在 `.env` 文件中可以配置以下 CUDA 相关选项：

```bash
# CUDA 设备模式 (必须是: auto, manual, all)
CUDA_DEVICE_MODE=auto

# 手动指定设备（仅在 manual 模式下生效，不要注释掉）
CUDA_VISIBLE_DEVICES=0,1

# 自动选择可用 GPU (必须是: true, false)
CUDA_AUTO_SELECT=true
```

⚠️ **重要提示**：
- `CUDA_DEVICE_MODE` 只能是 `auto`、`manual`、`all` 三个值之一
- 使用 `manual` 模式时，必须设置 `CUDA_VISIBLE_DEVICES`，不能注释掉
- 数字值（如 `CUDA_DEVICE_MODE=1`）是无效的

### 配置模式说明

#### 1. 自动模式 (auto)
```bash
CUDA_DEVICE_MODE=auto
CUDA_AUTO_SELECT=true
```

- 自动检测可用的 GPU（显存使用率 < 10%）
- 选择第一个可用的 GPU
- 如果没有可用 GPU，使用默认设备（GPU 0）

#### 2. 手动模式 (manual)
```bash
CUDA_DEVICE_MODE=manual
CUDA_VISIBLE_DEVICES=1
```

- 手动指定要使用的 GPU
- 支持单个 GPU：`CUDA_VISIBLE_DEVICES=1`
- 支持多个 GPU：`CUDA_VISIBLE_DEVICES=0,1`

#### 3. 全部模式 (all)
```bash
CUDA_DEVICE_MODE=all
```

- 使用所有可用的 GPU
- 不设置 `CUDA_VISIBLE_DEVICES` 环境变量

## GPU 管理工具

### 使用 GPU 管理工具

```bash
# 检查 GPU 状态
python tools/gpu_manager.py --check

# 显示 GPU 详细信息
python tools/gpu_manager.py --info

# 显示 GPU 进程
python tools/gpu_manager.py --processes

# 测试 GPU 选择
python tools/gpu_manager.py --test-selection

# 检查 sglang 状态
python tools/gpu_manager.py --sglang-status

# 启动 sglang 服务
python tools/gpu_manager.py --start-sglang

# 停止 sglang 服务
python tools/gpu_manager.py --stop-sglang

# 执行所有检查
python tools/gpu_manager.py --all
```

### 示例输出

```bash
$ python tools/gpu_manager.py --all

🔧 GPU 管理工具
==================================================
✅ NVIDIA 驱动正常

🔍 GPU 详细信息:
index, name, memory.used [MiB], memory.total [MiB], utilization.gpu [%], temperature.gpu
0, NVIDIA GeForce RTX 4090, 1024 MiB, 24564 MiB, 15 %, 45
1, NVIDIA GeForce RTX 4090, 512 MiB, 24564 MiB, 5 %, 42

🧪 测试 GPU 选择:
  可用 GPU: [1]
  选中设备: 1

📋 当前配置:
  CUDA_DEVICE_MODE: auto
  CUDA_VISIBLE_DEVICES: N/A
  CUDA_AUTO_SELECT: True

🔍 sglang 服务状态:
  运行状态: ✅ 运行中
  进程 PID: 12345
  端口占用: 是
  健康检查: ✅ 正常
  服务地址: http://127.0.0.1:30000

🔍 CUDA 配置:
  设备模式: auto
  自动选择: True
  手动设备: N/A
  可用 GPU: [1]
  选中设备: 1
```

## 常见场景配置

### 场景 1：双卡服务器，一张卡被占用

```bash
# 自动选择可用的卡
CUDA_DEVICE_MODE=auto
CUDA_AUTO_SELECT=true
# CUDA_VISIBLE_DEVICES=  (注释掉或留空)
```

系统会自动检测哪张卡可用并使用。

### 场景 2：指定使用第二张卡

```bash
# 手动指定使用 GPU 1
CUDA_DEVICE_MODE=manual
CUDA_VISIBLE_DEVICES=1
CUDA_AUTO_SELECT=false
```

⚠️ **注意**：`CUDA_VISIBLE_DEVICES` 不能注释掉，必须明确设置值。

### 场景 3：使用多张卡

```bash
# 手动指定使用 GPU 0 和 1
CUDA_DEVICE_MODE=manual
CUDA_VISIBLE_DEVICES=0,1
CUDA_AUTO_SELECT=false
```

### 场景 4：让系统自动管理所有卡

```bash
# 使用所有可用的卡
CUDA_DEVICE_MODE=all
CUDA_AUTO_SELECT=false
# CUDA_VISIBLE_DEVICES=  (注释掉或留空)
```

## 配置验证工具

使用配置检查工具验证您的配置：

```bash
python tools/check_cuda_config.py
```

这个工具会：
- 检查配置值是否有效
- 显示当前设备选择结果
- 提供修复建议
- 显示配置示例

## 故障排除

### 1. 检查 NVIDIA 驱动

```bash
nvidia-smi
```

### 2. 检查 GPU 使用情况

```bash
python tools/gpu_manager.py --info --processes
```

### 3. 测试 GPU 选择逻辑

```bash
python tools/gpu_manager.py --test-selection
```

### 4. 查看 sglang 日志

```bash
tail -f logs/sglang.log
```

### 5. 手动测试 sglang 启动

```bash
# 设置环境变量并启动
export CUDA_VISIBLE_DEVICES=1
mineru-sglang-server --port 30000 --host 127.0.0.1
```

## 性能优化建议

1. **单卡场景**：使用 `manual` 模式指定性能最好的卡
2. **多卡场景**：根据任务负载选择合适的卡数量
3. **共享环境**：使用 `auto` 模式避免冲突
4. **专用环境**：使用 `all` 模式充分利用资源

## 监控和日志

- sglang 服务日志：`logs/sglang.log`
- 服务状态检查：`/sglang/status` API 端点
- GPU 状态监控：使用 `gpu_manager.py` 工具定期检查
