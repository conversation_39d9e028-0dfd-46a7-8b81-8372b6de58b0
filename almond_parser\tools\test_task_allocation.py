#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试任务分配逻辑的脚本
用于验证修复后的任务分配是否正确工作
"""
import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
from almond_parser.services.task_allocation_service import task_allocation_service
from sqlalchemy import select, func, and_, or_
from loguru import logger


async def test_task_allocation_logic():
    """测试任务分配逻辑"""
    print("=" * 60)
    print("测试任务分配逻辑")
    print("=" * 60)
    
    async with get_async_session() as db:
        # 1. 检查当前系统状态
        print("\n1. 当前系统状态:")
        await check_system_status(db)
        
        # 2. 检查等待任务
        print("\n2. 等待分配的任务:")
        await check_pending_tasks(db)
        
        # 3. 测试任务分配逻辑
        print("\n3. 测试任务分配:")
        await test_allocation_logic(db)
        
        # 4. 检查可能的问题任务
        print("\n4. 检查问题任务:")
        await check_problematic_tasks(db)


async def check_system_status(db):
    """检查系统状态"""
    try:
        # 节点状态
        node_result = await db.execute(
            select(
                func.count(MinerUNode.id).label("total_nodes"),
                func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                func.sum(MinerUNode.current_tasks).label("current_load"),
                func.sum(MinerUNode.reserved_tasks).label("reserved_load")
            ).where(
                MinerUNode.is_enabled == True,
                MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])  # 在线或繁忙状态
            )
        )
        
        node_row = node_result.first()
        total_nodes = node_row.total_nodes or 0
        total_capacity = node_row.total_capacity or 0
        current_load = node_row.current_load or 0
        reserved_load = node_row.reserved_load or 0
        available_capacity = total_capacity - current_load - reserved_load
        
        print(f"  活跃节点数: {total_nodes}")
        print(f"  总容量: {total_capacity}")
        print(f"  当前负载: {current_load}")
        print(f"  预留负载: {reserved_load}")
        print(f"  可用容量: {available_capacity}")
        print(f"  负载率: {(current_load/total_capacity*100):.1f}%" if total_capacity > 0 else "  负载率: N/A")
        
    except Exception as e:
        print(f"  检查系统状态失败: {e}")


async def check_pending_tasks(db):
    """检查等待任务"""
    try:
        current_time = datetime.now()
        
        # 按状态统计任务
        status_result = await db.execute(
            select(
                Document.status,
                func.count(Document.id).label("count")
            ).group_by(Document.status)
        )
        
        print("  任务状态统计:")
        for row in status_result:
            print(f"    {row.status.value}: {row.count}")
        
        # 检查符合分配条件的任务
        allocatable_result = await db.execute(
            select(func.count(Document.id)).where(
                and_(
                    or_(
                        # 新上传的文档
                        Document.status == DocumentStatus.UPLOADED,
                        # 等待重试的文档（需要额外检查重试条件）
                        and_(
                            Document.status == DocumentStatus.RETRY_PENDING,
                            or_(
                                Document.next_retry_at.is_(None),  # 没有设置重试时间
                                Document.next_retry_at <= current_time  # 重试时间已到
                            ),
                            Document.retry_count < Document.max_retries  # 未超过最大重试次数
                        )
                    ),
                    Document.task_id.is_(None),  # 确保没有正在处理
                )
            )
        )
        
        allocatable_count = allocatable_result.scalar() or 0
        print(f"  可分配任务数: {allocatable_count}")
        
        # 检查有task_id但状态为RETRY_PENDING的任务（这些是问题任务）
        problematic_result = await db.execute(
            select(func.count(Document.id)).where(
                and_(
                    Document.status == DocumentStatus.RETRY_PENDING,
                    Document.task_id.isnot(None)  # 有task_id但状态为RETRY_PENDING
                )
            )
        )
        
        problematic_count = problematic_result.scalar() or 0
        if problematic_count > 0:
            print(f"  ⚠️  问题任务数: {problematic_count} (有task_id但状态为RETRY_PENDING)")
        
    except Exception as e:
        print(f"  检查等待任务失败: {e}")


async def test_allocation_logic(db):
    """测试分配逻辑"""
    try:
        print("  执行任务分配测试...")
        
        # 调用分配服务
        stats = await task_allocation_service.allocate_pending_tasks(max_allocations=5)
        
        print(f"  分配结果: {stats}")
        
        if stats.get("allocated", 0) > 0:
            print("  ✅ 成功分配任务")
        elif stats.get("total_pending", 0) == 0:
            print("  ℹ️  没有等待分配的任务")
        else:
            print("  ⚠️  有等待任务但未能分配")
            
    except Exception as e:
        print(f"  测试分配逻辑失败: {e}")


async def check_problematic_tasks(db):
    """检查可能有问题的任务"""
    try:
        current_time = datetime.now()
        
        # 查找有task_id但状态为RETRY_PENDING的任务
        problematic_tasks = await db.execute(
            select(Document).where(
                and_(
                    Document.status == DocumentStatus.RETRY_PENDING,
                    Document.task_id.isnot(None)
                )
            ).limit(10)
        )
        
        tasks = problematic_tasks.scalars().all()
        
        if tasks:
            print(f"  发现 {len(tasks)} 个问题任务:")
            for task in tasks:
                print(f"    文档ID: {task.document_id}")
                print(f"      状态: {task.status.value}")
                print(f"      任务ID: {task.task_id}")
                print(f"      重试次数: {task.retry_count}/{task.max_retries}")
                print(f"      下次重试: {task.next_retry_at}")
                print(f"      更新时间: {task.updated_at}")
                print()
        else:
            print("  ✅ 没有发现问题任务")
            
    except Exception as e:
        print(f"  检查问题任务失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_task_allocation_logic())
