# MineruAPI 数据库设置指南

## 问题描述

在部署 MineruAPI 到服务器时，可能会遇到数据库文件不存在的错误。这是因为 SQLite 数据库文件需要在首次运行时创建。

## 解决方案

### 方法一：使用数据库初始化脚本（推荐）

在首次启动服务前，运行数据库初始化脚本：

```bash
# 进入 mineru_api 目录
cd mineru_api

# 运行数据库初始化脚本
python scripts/init_database.py
```

这个脚本会：
- 创建所有必要的目录（data、logs、output、temp）
- 初始化认证数据库（如果启用认证）
- 初始化任务历史数据库
- 验证所有数据库都能正常工作

### 方法二：使用数据库检查脚本

如果想检查现有数据库状态：

```bash
python scripts/check_database.py
```

### 方法三：自动初始化（已集成）

新版本的启动脚本已经集成了数据库检查，会在服务启动时自动检查和创建必要的数据库：

```bash
python start_server.py
```

如果数据库初始化失败，服务会拒绝启动并提示错误信息。

## 数据库文件位置

MineruAPI 使用以下 SQLite 数据库文件：

1. **认证数据库**：`auth.db`（如果启用认证）
   - 存储 API keys 和认证信息
   - 位置：项目根目录

2. **任务历史数据库**：`data/task_history.db`
   - 存储任务执行历史和统计信息
   - 位置：`mineru_api/data/` 目录

## 目录结构

服务运行时会创建以下目录结构：

```
mineru_api/
├── data/              # 数据库文件
│   └── task_history.db
├── logs/              # 日志文件
│   └── mineru_api.log
├── output/            # 解析结果输出
└── temp/              # 临时文件
```

## 常见问题

### Q: 为什么会出现数据库文件不存在的错误？

A: SQLite 数据库文件需要在首次使用时创建。如果目录不存在或权限不足，会导致创建失败。

### Q: 如何重置数据库？

A: 删除对应的数据库文件，然后重新运行初始化脚本：

```bash
# 删除数据库文件
rm -f auth.db data/task_history.db

# 重新初始化
python scripts/init_database.py
```

### Q: 数据库文件可以迁移吗？

A: 可以。SQLite 数据库文件是独立的，可以直接复制到新环境。确保新环境有正确的文件权限。

### Q: 如何备份数据库？

A: 直接复制数据库文件即可：

```bash
# 备份认证数据库
cp auth.db auth.db.backup

# 备份任务历史数据库
cp data/task_history.db data/task_history.db.backup
```

## 权限要求

确保运行服务的用户有以下权限：
- 在项目目录创建文件和子目录的权限
- 读写数据库文件的权限

## 部署建议

1. **首次部署**：先运行 `python scripts/init_database.py` 确保数据库正确初始化
2. **更新部署**：保留现有数据库文件，只更新代码
3. **生产环境**：定期备份数据库文件
4. **监控**：检查日志文件确保数据库操作正常
