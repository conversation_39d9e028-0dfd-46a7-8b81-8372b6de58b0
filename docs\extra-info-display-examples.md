# 扩展信息表格显示示例

## 功能说明

在文档列表表格中新增了"扩展信息"列，用于显示JSON格式的扩展信息。该列具有以下特性：

1. **简化显示**: 表格中显示关键信息的简化版本
2. **悬浮详情**: 鼠标悬浮时显示完整的扩展信息
3. **响应式设计**: 在小屏幕上自动隐藏以节省空间
4. **智能格式化**: 自动识别重要字段并优先显示

## 显示效果示例

### 1. AI中心调用场景

**原始JSON数据:**
```json
{
  "account": "AI中心",
  "user_name": "金烨",
  "department": "上海总部-信息技术部",
  "user_id": "jin.ye",
  "module": "多文档解读",
  "project": "个人库",
  "session_id": "sess_123456"
}
```

**表格中显示:** `AI中心 - 金烨 - 多文档解读`

**悬浮提示显示:**
```
account: AI中心
user_name: 金烨
department: 上海总部-信息技术部
user_id: jin.ye
module: 多文档解读
project: 个人库
session_id: sess_123456
```

### 2. 项目管理场景

**原始JSON数据:**
```json
{
  "project_name": "智能客服系统",
  "project_id": "proj_001",
  "team": "产品研发部",
  "priority_level": "high",
  "deadline": "2024-02-01"
}
```

**表格中显示:** `智能客服系统 - 产品研发部 - high`

**悬浮提示显示:**
```
project_name: 智能客服系统
project_id: proj_001
team: 产品研发部
priority_level: high
deadline: 2024-02-01
```

### 3. 文档分类场景

**原始JSON数据:**
```json
{
  "document_category": "技术文档",
  "document_type": "API文档",
  "version": "v1.2.0",
  "author": "技术团队",
  "tags": ["API", "接口", "开发"]
}
```

**表格中显示:** `技术文档 - API文档 - v1.2.0`

**悬浮提示显示:**
```
document_category: 技术文档
document_type: API文档
version: v1.2.0
author: 技术团队
tags: ["API", "接口", "开发"]
```

## 字段优先级

系统会按照以下优先级显示字段：

1. **account** (账号)
2. **user_name** (用户名)
3. **module** (模块)
4. **department** (部门)
5. **project** (项目)

如果没有找到优先字段，则显示前3个可用字段。

## 响应式行为

- **大屏幕 (>1400px)**: 扩展信息列宽度为250px
- **中等屏幕 (1200px-1400px)**: 扩展信息列宽度缩小为200px，字体变小
- **小屏幕 (<1200px)**: 隐藏扩展信息列以节省空间

## 样式特性

- **背景色**: 浅灰色背景 (#f8fafc)
- **边框**: 淡色边框 (#e2e8f0)
- **悬浮效果**: 鼠标悬浮时背景变深
- **文本截断**: 超长文本自动截断并显示省略号
- **光标样式**: help光标提示用户可以查看详情

## 测试数据

可以使用以下测试数据来验证显示效果：

```bash
# 测试1: AI中心场景
curl -X POST "http://localhost:8000/api/v1/document/upload" \
  -H "X-API-Key: your-api-key" \
  -F "files=@test.pdf" \
  -F "remarks=AI中心测试文档" \
  -F 'extra_info={"account":"AI中心","user_name":"金烨","department":"上海总部-信息技术部","user_id":"jin.ye","module":"多文档解读","project":"个人库"}'

# 测试2: 项目管理场景
curl -X POST "http://localhost:8000/api/v1/document/upload" \
  -H "X-API-Key: your-api-key" \
  -F "files=@test.pdf" \
  -F "remarks=项目文档" \
  -F 'extra_info={"project_name":"智能客服系统","project_id":"proj_001","team":"产品研发部","priority_level":"high","deadline":"2024-02-01"}'

# 测试3: 文档分类场景
curl -X POST "http://localhost:8000/api/v1/document/upload" \
  -H "X-API-Key: your-api-key" \
  -F "files=@test.pdf" \
  -F "remarks=技术文档" \
  -F 'extra_info={"document_category":"技术文档","document_type":"API文档","version":"v1.2.0","author":"技术团队","tags":["API","接口","开发"]}'
```

## 注意事项

1. **JSON格式**: 确保extra_info是有效的JSON格式
2. **字段命名**: 建议使用英文字段名，中文内容放在值中
3. **数据长度**: 避免单个字段值过长，影响显示效果
4. **嵌套对象**: 复杂嵌套对象会在悬浮提示中以JSON格式显示
5. **空值处理**: null和undefined值会被自动过滤
