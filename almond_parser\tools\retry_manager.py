#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
重试管理工具 - 手动管理重试任务和查看状态
"""
import asyncio
import sys
from datetime import datetime, timedelta
from typing import List, Optional
import click
from loguru import logger
from sqlalchemy import select, and_, or_, func
from tabulate import tabulate

from almond_parser.db.database import get_async_session, init_database
from almond_parser.db.models.document import Document, DocumentStatus, DocumentLog
from almond_parser.tasks.arq_app import arq_manager
from almond_parser.tasks.retry_tasks import process_retry_documents


@click.group()
def cli():
    """杏仁解析重试管理工具"""
    pass


@cli.command()
@click.option('--status', multiple=True, help='过滤状态 (可多选)')
@click.option('--limit', default=20, help='显示数量限制')
@click.option('--show-retry-only', is_flag=True, help='只显示重试相关文档')
async def list_documents(status, limit, show_retry_only):
    """列出文档状态"""
    await init_database()
    
    async with get_async_session() as db:
        try:
            # 构建查询条件
            conditions = []
            
            if status:
                status_list = [DocumentStatus(s.upper()) for s in status]
                conditions.append(Document.status.in_(status_list))
            
            if show_retry_only:
                conditions.append(
                    or_(
                        Document.status == DocumentStatus.RETRY_PENDING,
                        Document.status == DocumentStatus.FALLBACK_RETRY,
                        Document.retry_count > 0,
                        Document.has_fallback == True
                    )
                )
            
            # 执行查询
            query = select(Document)
            if conditions:
                query = query.where(and_(*conditions))
            
            query = query.order_by(Document.created_at.desc()).limit(limit)
            
            result = await db.execute(query)
            documents = result.scalars().all()
            
            if not documents:
                click.echo("未找到符合条件的文档")
                return
            
            # 格式化输出
            headers = [
                "ID", "文件名", "状态", "重试次数", "降级", 
                "重试原因", "下次重试", "创建时间"
            ]
            
            rows = []
            for doc in documents:
                rows.append([
                    doc.document_id[:8] + "...",
                    doc.file_name[:30] + "..." if len(doc.file_name) > 30 else doc.file_name,
                    doc.status.value,
                    f"{doc.retry_count}/{doc.max_retries}",
                    "✓" if doc.has_fallback else "✗",
                    doc.retry_reason or "-",
                    doc.next_retry_at.strftime("%m-%d %H:%M") if doc.next_retry_at else "-",
                    doc.created_at.strftime("%m-%d %H:%M")
                ])
            
            click.echo(tabulate(rows, headers=headers, tablefmt="grid"))
            click.echo(f"\n总计: {len(documents)} 个文档")
            
        except Exception as e:
            logger.error(f"查询文档失败: {e}")
            click.echo(f"错误: {e}")


@cli.command()
async def stats():
    """显示重试统计信息"""
    await init_database()
    
    async with get_async_session() as db:
        try:
            # 统计各状态文档数量
            status_stats = {}
            for status in DocumentStatus:
                result = await db.execute(
                    select(func.count(Document.id)).where(Document.status == status)
                )
                count = result.scalar()
                if count > 0:
                    status_stats[status.value] = count
            
            # 重试相关统计
            retry_stats_query = """
            SELECT 
                COUNT(*) as total_retries,
                COUNT(CASE WHEN has_fallback = 1 THEN 1 END) as fallback_count,
                COUNT(CASE WHEN is_system_retry = 1 THEN 1 END) as system_retry_count,
                AVG(retry_count) as avg_retry_count,
                MAX(retry_count) as max_retry_count
            FROM documents 
            WHERE retry_count > 0
            """
            
            result = await db.execute(text(retry_stats_query))
            retry_stats = result.fetchone()
            
            # 显示统计信息
            click.echo("📊 文档状态统计:")
            for status, count in status_stats.items():
                click.echo(f"  {status}: {count}")
            
            if retry_stats and retry_stats[0] > 0:
                click.echo(f"\n🔄 重试统计:")
                click.echo(f"  总重试文档: {retry_stats[0]}")
                click.echo(f"  降级重试: {retry_stats[1]}")
                click.echo(f"  系统重试: {retry_stats[2]}")
                click.echo(f"  平均重试次数: {retry_stats[3]:.1f}")
                click.echo(f"  最大重试次数: {retry_stats[4]}")
            else:
                click.echo("\n🔄 暂无重试记录")
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            click.echo(f"错误: {e}")


@cli.command()
@click.argument('document_id')
async def retry_document(document_id):
    """手动重试指定文档"""
    await init_database()
    
    async with get_async_session() as db:
        try:
            # 查找文档
            result = await db.execute(
                select(Document).where(Document.document_id == document_id)
            )
            document = result.scalar_one_or_none()
            
            if not document:
                click.echo(f"文档 {document_id} 不存在")
                return
            
            # 检查是否可以重试
            if document.retry_count >= document.max_retries:
                click.echo(f"文档 {document_id} 已超过最大重试次数")
                return
            
            # 提交重试任务
            job_id = await arq_manager.enqueue_task(
                'enhanced_process_document',
                document_id=document.document_id,
                user_id=document.user_id,
                service_type="auto",
                parse_mode=document.current_parse_mode or "auto",
                config=document.result_data or {}
            )
            
            # 更新文档状态
            document.status = DocumentStatus.QUEUED
            document.retry_count += 1
            document.next_retry_at = None
            document.is_system_retry = False
            document.retry_reason = "manual_retry"
            
            await db.commit()
            
            click.echo(f"✅ 文档 {document_id} 重试任务已提交")
            click.echo(f"任务ID: {job_id}")
            click.echo(f"重试次数: {document.retry_count}/{document.max_retries}")
            
        except Exception as e:
            logger.error(f"手动重试失败: {e}")
            click.echo(f"错误: {e}")


@cli.command()
async def process_pending():
    """手动处理所有待重试文档"""
    await init_database()
    
    click.echo("开始处理待重试文档...")
    
    try:
        # 调用重试任务处理函数
        result = await process_retry_documents({})
        
        if result["success"]:
            click.echo(f"✅ 处理完成:")
            click.echo(f"  成功: {result['processed_count']}")
            click.echo(f"  失败: {result['failed_count']}")
            click.echo(f"  总计: {result['total_found']}")
        else:
            click.echo(f"❌ 处理失败: {result['error']}")
            
    except Exception as e:
        logger.error(f"处理待重试文档失败: {e}")
        click.echo(f"错误: {e}")


@cli.command()
@click.argument('document_id')
async def show_logs(document_id):
    """显示文档处理日志"""
    await init_database()
    
    async with get_async_session() as db:
        try:
            # 查询文档日志
            result = await db.execute(
                select(DocumentLog)
                .where(DocumentLog.document_id == document_id)
                .order_by(DocumentLog.created_at.desc())
                .limit(50)
            )
            logs = result.scalars().all()
            
            if not logs:
                click.echo(f"文档 {document_id} 无日志记录")
                return
            
            click.echo(f"📋 文档 {document_id} 处理日志:")
            click.echo("-" * 80)
            
            for log in reversed(logs):  # 按时间正序显示
                timestamp = log.created_at.strftime("%m-%d %H:%M:%S")
                level_color = {
                    "INFO": "green",
                    "WARNING": "yellow", 
                    "ERROR": "red"
                }.get(log.level, "white")
                
                click.echo(f"[{timestamp}] ", nl=False)
                click.secho(f"{log.level:7}", fg=level_color, nl=False)
                click.echo(f" {log.message}")
                
                if log.source:
                    click.echo(f"           来源: {log.source}")
                    
        except Exception as e:
            logger.error(f"查询日志失败: {e}")
            click.echo(f"错误: {e}")


@cli.command()
@click.option('--dry-run', is_flag=True, help='仅显示将要清理的记录，不实际删除')
@click.option('--days', default=7, help='清理多少天前的记录')
async def cleanup(dry_run, days):
    """清理过期的失败记录"""
    await init_database()
    
    cutoff_date = datetime.now() - timedelta(days=days)
    
    async with get_async_session() as db:
        try:
            # 查找要清理的记录
            result = await db.execute(
                select(Document)
                .where(
                    and_(
                        Document.status == DocumentStatus.FAILED,
                        Document.created_at < cutoff_date
                    )
                )
            )
            documents = result.scalars().all()
            
            if not documents:
                click.echo(f"未找到 {days} 天前的失败记录")
                return
            
            click.echo(f"找到 {len(documents)} 个 {days} 天前的失败记录")
            
            if dry_run:
                click.echo("预览模式，不会实际删除:")
                for doc in documents[:10]:  # 只显示前10个
                    click.echo(f"  - {doc.document_id}: {doc.file_name}")
                if len(documents) > 10:
                    click.echo(f"  ... 还有 {len(documents) - 10} 个")
            else:
                if click.confirm(f"确定要删除这 {len(documents)} 个记录吗？"):
                    for doc in documents:
                        await db.delete(doc)
                    
                    await db.commit()
                    click.echo(f"✅ 已清理 {len(documents)} 个过期记录")
                else:
                    click.echo("取消清理")
                    
        except Exception as e:
            logger.error(f"清理失败: {e}")
            click.echo(f"错误: {e}")


if __name__ == "__main__":
    # 设置异步支持
    def async_command(f):
        def wrapper(*args, **kwargs):
            return asyncio.run(f(*args, **kwargs))
        return wrapper
    
    # 为所有命令添加异步支持
    for command in cli.commands.values():
        if asyncio.iscoroutinefunction(command.callback):
            command.callback = async_command(command.callback)
    
    cli()
