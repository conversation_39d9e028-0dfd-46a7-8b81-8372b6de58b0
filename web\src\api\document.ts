import request from '@/utils/request'
import type { Document, QueryParams, QueryResult, DocumentParseResult } from '@/types/document'

export interface QueryResponse {
  items: Document[]
  total: number
}

/**
 * 查询文档列表
 */
export const queryDocuments = (params: QueryParams): Promise<QueryResult> => {
  return request({
    url: '/document/documents',
    method: 'get',
    params
  })
}

/**
 * 重试处理文档
 * @param documentId 文档ID
 * @param force 是否强制重试
 */
export const retryDocument = (documentId: string, force: boolean = false): Promise<void> => {
  return request({
    url: `/document/documents/${documentId}/retry`,
    method: 'post',
    data: {
      force: force
    }
  })
}

/**
 * 获取文档解析结果
 * @param documentId 文档ID
 */
export const getDocumentResult = (documentId: string): Promise<DocumentParseResult> => {
  return request({
    url: `/document/${documentId}/result`,
    method: 'get',
    params: { content_type: 'json' }
  })
}

/**
 * 创建WebSocket连接获取文档日志
 * @param documentId 文档ID
 * @returns WebSocket实例
 */
export function createLogWebSocket(documentId: string): WebSocket {
  console.log('🔍 [DEBUG] createLogWebSocket 被调用，documentId:', documentId)

  // 获取认证token
  const token = localStorage.getItem('token')
  console.log('🔍 [DEBUG] Token获取结果:', token ? `存在(长度:${token.length})` : '不存在')

  let wsUrl: string

  // 统一使用当前页面的协议和主机，让代理或反向代理处理路由
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const host = window.location.host // 使用完整的host（包含端口）
  wsUrl = `${protocol}//${host}/api/v1/manage/ws/logs/document/${documentId}${token ? `?token=${token}` : ''}`

  console.log('🔍 [DEBUG] 环境:', import.meta.env.DEV ? '开发' : '生产')
  console.log('🔍 [DEBUG] 使用统一的相对路径方式，host:', host)

  console.log('🔍 [DEBUG] Protocol:', window.location.protocol)
  console.log('🔍 [DEBUG] Host:', window.location.host)
  console.log('🌐 [DEBUG] 完整WebSocket URL:', wsUrl)

  try {
    const ws = new WebSocket(wsUrl)
    console.log('✅ [DEBUG] WebSocket对象创建成功')
    return ws
  } catch (error) {
    console.error('❌ [ERROR] WebSocket创建失败:', error)
    throw error
  }
}

// 上传文档
export function uploadDocuments(data: FormData, apiKey: string) {
  const headers: Record<string, string> = {
    'Content-Type': 'multipart/form-data',
    'X-API-Key': apiKey
  }

  return request({
    url: '/document/upload',
    method: 'post',
    data,
    headers
  })
}

// 获取文档日志
export function getDocumentLogs(documentId: string, params: any) {
  return request({
    url: `/document/documents/${documentId}/logs`,
    method: 'get',
    params
  })
}

// 下载原始文件
export function downloadOriginalFile(documentId: string, fileName: string, filePath?: string) {
  // 先尝试使用nginx代理下载
  return tryNginxDownload(fileName, filePath).catch(() => {
    // 降级到API下载
    return request({
      url: `/document/documents/${documentId}/download`,
      method: 'get',
      responseType: 'blob'
    }).then(response => {
      // 检查响应是否为blob
      if (!(response instanceof Blob)) {
        throw new Error('响应格式错误')
      }

      // 检查blob大小
      if (response.size === 0) {
        throw new Error('文件为空')
      }

      // 创建下载链接
      const blob = new Blob([response])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }).catch(error => {
      console.error('下载文件失败:', error)
      throw error
    })
  })
}

// 尝试通过nginx代理下载
async function tryNginxDownload(fileName: string, filePath?: string): Promise<void> {
  try {
    // 导入下载配置函数
    const { getDownloadConfig } = await import('./docDocument')
    const config = await getDownloadConfig()

    let downloadUrl: string

    if (filePath && filePath.startsWith(config.kb_path_prefix)) {
      // 知识库文件：需要从file_path中截断前缀
      const relativePath = filePath.substring(config.kb_path_prefix.length)
      downloadUrl = `${config.base_url}${config.kb_prefix}${relativePath}`
    } else {
      // 多文档：直接使用文件名
      downloadUrl = `${config.base_url}${config.docs_prefix}${fileName}`
    }

    // 直接通过nginx代理下载
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

  } catch (error) {
    console.error('nginx代理下载失败:', error)
    throw error
  }
}

// 获取文档详情
export function getDocument(documentId: string) {
  return request({
    url: `/document/documents/${documentId}`,
    method: 'get'
  })
}

// 获取批次状态
export function getBatchStatus(batchId: string) {
  return request({
    url: `/document/batch/${batchId}`,
    method: 'get'
  })
} 