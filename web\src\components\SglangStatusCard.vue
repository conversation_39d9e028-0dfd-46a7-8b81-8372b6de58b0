<template>
  <el-card class="sglang-status-card" :class="statusClass">
    <template #header>
      <div class="card-header">
        <span>SGLang 服务状态</span>
        <el-button 
          type="primary" 
          size="small" 
          @click="checkStatus" 
          :loading="checking"
        >
          <el-icon><Refresh /></el-icon>
          检查
        </el-button>
      </div>
    </template>
    
    <div class="status-content">
      <div class="status-row">
        <span class="label">状态:</span>
        <el-tag :type="statusTagType" size="small">
          {{ statusText }}
        </el-tag>
      </div>
      
      <div class="status-row" v-if="node.sglang_url">
        <span class="label">地址:</span>
        <el-link :href="node.sglang_url" target="_blank" type="primary" size="small">
          {{ node.sglang_url }}
        </el-link>
      </div>
      
      <div class="status-row" v-if="(node.sglang_consecutive_failures || 0) > 0">
        <span class="label">健康检查失败:</span>
        <el-text type="danger" size="small">{{ node.sglang_consecutive_failures || 0 }} 次</el-text>
      </div>
      
      <div class="status-row" v-if="(node.sglang_restart_count || 0) > 0">
        <span class="label">重启次数:</span>
        <el-text type="warning" size="small">{{ node.sglang_restart_count || 0 }} 次</el-text>
      </div>
      
      <div class="status-row" v-if="node.sglang_last_check">
        <span class="label">最后检查:</span>
        <el-text size="small">{{ formatTime(node.sglang_last_check) }}</el-text>
      </div>
      
      <div class="status-row" v-if="node.sglang_last_restart">
        <span class="label">最后重启:</span>
        <el-text size="small">{{ formatTime(node.sglang_last_restart) }}</el-text>
      </div>
    </div>
    
    <div class="actions" v-if="showActions">
      <el-button 
        type="warning" 
        size="small" 
        @click="restartService" 
        :loading="restarting"
        :disabled="node.sglang_status === 'RESTARTING' || node.sglang_status === 'restarting'"
      >
        <el-icon><RefreshRight /></el-icon>
        重启服务
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, RefreshRight } from '@element-plus/icons-vue'
import { mineruNodeApi } from '@/api/mineruNode'
import type { MinerUNode } from '@/types/mineruNode'

const props = defineProps<{
  node: MinerUNode
  showActions?: boolean
}>()

const emit = defineEmits<{
  (e: 'refresh'): void
}>()

const checking = ref(false)
const restarting = ref(false)

const statusClass = computed(() => {
  const status = props.node.sglang_status.toLowerCase()
  return {
    'status-online': status === 'online',
    'status-offline': status === 'offline',
    'status-error': status === 'error',
    'status-restarting': status === 'restarting',
    'status-unknown': status === 'unknown'
  }
})

const statusTagType = computed(() => {
  const types: Record<string, string> = {
    ONLINE: 'success',
    OFFLINE: 'danger',
    ERROR: 'danger',
    UNKNOWN: 'info',
    RESTARTING: 'warning',
    // 兼容小写
    online: 'success',
    offline: 'danger',
    error: 'danger',
    unknown: 'info',
    restarting: 'warning'
  }
  return types[props.node.sglang_status] || 'info'
})

const statusText = computed(() => {
  const texts: Record<string, string> = {
    ONLINE: '在线',
    OFFLINE: '离线',
    ERROR: '错误',
    UNKNOWN: '未知',
    RESTARTING: '重启中',
    // 兼容小写
    online: '在线',
    offline: '离线',
    error: '错误',
    unknown: '未知',
    restarting: '重启中'
  }
  return texts[props.node.sglang_status] || props.node.sglang_status
})

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const checkStatus = async () => {
  checking.value = true
  try {
    const result = await mineruNodeApi.getSglangStatus(props.node.id)
    if (result.success) {
      ElMessage.success(`SGLang状态: ${result.healthy ? '健康' : '异常'}`)
      emit('refresh')
    } else {
      ElMessage.error(`SGLang检查失败: ${result.error}`)
    }
  } catch (error) {
    ElMessage.error('SGLang状态检查失败')
  } finally {
    checking.value = false
  }
}

const restartService = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要重启节点 "${props.node.name}" 的SGLang服务吗？`,
      '确认重启',
      { type: 'warning' }
    )
    
    restarting.value = true
    try {
      const result = await mineruNodeApi.restartSglang(props.node.id)
      if (result.success) {
        ElMessage.success('SGLang服务重启成功')
        emit('refresh')
      } else {
        ElMessage.error(`SGLang重启失败: ${result.error}`)
      }
    } catch (error) {
      ElMessage.error('SGLang服务重启失败')
    } finally {
      restarting.value = false
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('SGLang服务重启失败')
    }
  }
}
</script>

<style scoped>
.sglang-status-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  min-width: 80px;
  color: var(--el-text-color-regular);
}

.actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-light);
}

/* 状态颜色 */
.status-online {
  border-left: 4px solid var(--el-color-success);
}

.status-offline {
  border-left: 4px solid var(--el-color-danger);
}

.status-error {
  border-left: 4px solid var(--el-color-danger);
}

.status-restarting {
  border-left: 4px solid var(--el-color-warning);
}

.status-unknown {
  border-left: 4px solid var(--el-color-info);
}
</style>
