#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试新的SGLang重启逻辑
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.services.sglang_restart_service import SglangRestartService
from almond_parser.services.sglang_alert_service import SglangAlertService
from almond_parser.db.models import MinerUNode, SglangStatus


async def test_restart_logic():
    """测试重启逻辑"""
    logger.info("🔄 开始测试SGLang重启逻辑")
    
    async with get_async_session() as db:
        restart_service = SglangRestartService(db)
        alert_service = SglangAlertService(db)
        
        # 获取第一个启用的节点进行测试
        from sqlalchemy import select
        result = await db.execute(
            select(MinerUNode).where(MinerUNode.is_enabled == True).limit(1)
        )
        node = result.scalar_one_or_none()
        
        if not node:
            logger.warning("没有找到启用的节点")
            return
        
        logger.info(f"测试节点: {node.name} (ID: {node.id})")
        
        # 显示当前状态
        logger.info("📊 当前节点状态:")
        logger.info(f"  - SGLang状态: {node.sglang_status}")
        logger.info(f"  - 连续失败次数: {node.sglang_consecutive_failures}")
        logger.info(f"  - 当前周期重启次数: {node.sglang_current_cycle_restarts}")
        logger.info(f"  - 重启周期开始时间: {node.sglang_restart_cycle_start}")
        logger.info(f"  - 告警已发送: {node.sglang_alert_sent}")
        
        # 测试1: 检查重启需求
        logger.info("\n🔍 测试1: 检查重启需求")
        restart_needed = await restart_service.check_restart_needed(node)
        logger.info(f"是否需要重启: {restart_needed}")
        
        # 测试2: 检查告警需求
        logger.info("\n🚨 测试2: 检查告警需求")
        alert_needed = await restart_service.check_alert_needed(node)
        logger.info(f"是否需要告警: {alert_needed}")
        
        # 模拟不同的重启周期状态
        logger.info("\n🎭 模拟测试场景:")
        
        # 场景1: 模拟第一次失败
        logger.info("\n场景1: 模拟第一次失败")
        await simulate_failure_scenario(db, node, 1)
        
        # 场景2: 模拟第二次失败
        logger.info("\n场景2: 模拟第二次失败")
        await simulate_failure_scenario(db, node, 2)
        
        # 场景3: 模拟第三次失败（应该发送告警）
        logger.info("\n场景3: 模拟第三次失败（应该发送告警）")
        await simulate_failure_scenario(db, node, 3)
        
        # 场景4: 模拟恢复正常
        logger.info("\n场景4: 模拟恢复正常")
        await simulate_recovery_scenario(db, node)


async def simulate_failure_scenario(db, node, failure_count):
    """模拟失败场景"""
    restart_service = SglangRestartService(db)
    
    # 设置失败状态
    node.sglang_status = SglangStatus.OFFLINE
    node.sglang_consecutive_failures = 3  # 触发重启条件
    
    if failure_count == 1:
        # 第一次失败，重置重启周期
        node.sglang_current_cycle_restarts = 0
        node.sglang_restart_cycle_start = None
        node.sglang_alert_sent = False
        node.sglang_alert_sent_at = None
    elif failure_count == 2:
        # 第二次失败，已重启1次
        node.sglang_current_cycle_restarts = 1
        node.sglang_restart_cycle_start = datetime.now() - timedelta(minutes=10)
        node.sglang_last_restart = datetime.now() - timedelta(minutes=5)
    elif failure_count == 3:
        # 第三次失败，已重启2次
        node.sglang_current_cycle_restarts = 2
        node.sglang_restart_cycle_start = datetime.now() - timedelta(minutes=15)
        node.sglang_last_restart = datetime.now() - timedelta(minutes=5)
    
    await db.commit()
    
    # 检查重启需求
    restart_needed = await restart_service.check_restart_needed(node)
    logger.info(f"  重启需求: {restart_needed}")
    
    # 检查告警需求
    alert_needed = await restart_service.check_alert_needed(node)
    logger.info(f"  告警需求: {alert_needed}")
    
    # 显示当前状态
    logger.info(f"  当前周期重启次数: {node.sglang_current_cycle_restarts}")
    logger.info(f"  连续失败次数: {node.sglang_consecutive_failures}")


async def simulate_recovery_scenario(db, node):
    """模拟恢复场景"""
    restart_service = SglangRestartService(db)
    
    # 设置恢复状态
    node.sglang_status = SglangStatus.ONLINE
    node.sglang_consecutive_failures = 0
    
    # 模拟之前有重启周期
    node.sglang_current_cycle_restarts = 2
    node.sglang_restart_cycle_start = datetime.now() - timedelta(minutes=20)
    node.sglang_alert_sent = True
    node.sglang_alert_sent_at = datetime.now() - timedelta(minutes=5)
    
    await db.commit()
    
    logger.info("  设置为恢复状态...")
    
    # 重置重启周期
    await restart_service.reset_restart_cycle(node)
    
    # 刷新节点状态
    await db.refresh(node)
    
    logger.info(f"  重启周期已重置:")
    logger.info(f"    - 当前周期重启次数: {node.sglang_current_cycle_restarts}")
    logger.info(f"    - 重启周期开始时间: {node.sglang_restart_cycle_start}")
    logger.info(f"    - 告警已发送: {node.sglang_alert_sent}")


async def test_alert_logic():
    """测试告警逻辑"""
    logger.info("\n🚨 测试告警逻辑")
    
    async with get_async_session() as db:
        alert_service = SglangAlertService(db)
        
        # 执行告警检查
        result = await alert_service.check_and_send_alerts()
        
        if result["success"]:
            logger.success("✅ 告警检查成功:")
            logger.info(f"  - 需要告警节点数: {result.get('total_nodes', 0)}")
            logger.info(f"  - 发送告警数: {result.get('alert_count', 0)}")
            logger.info(f"  - 消息: {result.get('message', '')}")
            
            if result.get('failed_nodes'):
                logger.info("  异常节点列表:")
                for node_info in result['failed_nodes']:
                    logger.info(f"    - 节点{node_info['node_id']}: {node_info['name']}")
                    logger.info(f"      状态: {node_info['status']}")
                    logger.info(f"      连续失败: {node_info['failures']}次")
                    logger.info(f"      周期重启: {node_info.get('cycle_restarts', 0)}次")
        else:
            logger.error(f"❌ 告警检查失败: {result.get('error')}")


async def main():
    """主函数"""
    try:
        await test_restart_logic()
        await test_alert_logic()
        logger.success("🎉 重启逻辑测试完成")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
