#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
验证数据库字段名称的脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
from almond_parser.db.models.document import Document, DocumentStatus

def verify_model_fields():
    """验证模型字段"""
    print("=" * 50)
    print("验证 MinerUNode 模型字段")
    print("=" * 50)
    
    # 检查 MinerUNode 的字段
    node_fields = [attr for attr in dir(MinerUNode) if not attr.startswith('_')]
    print("MinerUNode 字段:")
    for field in sorted(node_fields):
        if hasattr(MinerUNode, field):
            attr = getattr(MinerUNode, field)
            if hasattr(attr, 'type'):
                print(f"  {field}: {attr.type}")
    
    print("\n" + "=" * 50)
    print("验证 NodeStatus 枚举值")
    print("=" * 50)
    
    print("NodeStatus 枚举值:")
    for status in NodeStatus:
        print(f"  {status.name}: {status.value}")
    
    print("\n" + "=" * 50)
    print("验证 DocumentStatus 枚举值")
    print("=" * 50)
    
    print("DocumentStatus 枚举值:")
    for status in DocumentStatus:
        print(f"  {status.name}: {status.value}")

if __name__ == "__main__":
    verify_model_fields()
