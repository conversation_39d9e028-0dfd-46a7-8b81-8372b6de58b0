# MineruAPI - 基于 LitServe 的 OCR 解析服务

## 📖 项目简介

MineruAPI 是一个基于 LitServe 框架构建的轻量级异步 OCR 解析服务，封装了 Mineru2.0 的解析功能，提供简单易用的 REST API 接口。

### ✨ 主要特性

- 🚀 **轻量级异步处理**: 使用 asyncio + threading，无需 Redis/Celery
- 📡 **回调机制**: 支持任务完成后的 HTTP 回调通知
- 🔄 **任务管理**: 内存队列管理，支持任务状态追踪
- 📊 **多格式输出**: 支持 Markdown、JSON、图片等多种输出格式
- 🌐 **RESTful API**: 标准的 REST API 接口设计
- 📝 **完整日志**: 详细的日志记录和错误追踪
- 📄 **多格式支持**: 支持 PDF、Word、PowerPoint、Excel、图像等多种文件格式

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端请求     │───▶│   LitServe API  │───▶│   任务管理器     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   回调通知      │◀───│   回调服务      │◀───│   OCR 解析服务   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                               ┌─────────────────┐
                                               │   文档转换服务   │
                                               │ (Word/PPT/图像) │
                                               └─────────────────┘
```

## 📄 支持的文件格式

| 格式类型 | 支持的扩展名 | 转换方式 |
|---------|-------------|----------|
| PDF | `.pdf` | 直接处理 |
| Word文档 | `.doc`, `.docx` | Office COM / LibreOffice |
| PowerPoint | `.ppt`, `.pptx` | Office COM / LibreOffice |
| Excel | `.xls`, `.xlsx` | Office COM / LibreOffice |
| 图像 | `.jpg`, `.jpeg`, `.png`, `.bmp`, `.tiff` | PIL转换 |

## 🔧 后端模式

| 后端模式 | 平台支持 | 性能 | 说明 |
|---------|---------|------|------|
| `pipeline` | Windows/Linux | 中等 | 传统模式，兼容性最好 |
| `vlm` | Linux | 高 | 自动映射到 `vlm-sglang-client` |
| `sglang` | Linux | 高 | 自动映射到 `vlm-sglang-client` |
| `vlm-sglang-client` | Linux | 高 | 需要 sglang 服务器 |
| `vlm-transformers` | Linux | 中等 | VLM 模式，无需额外服务 |

### 后端自动切换
- 当请求 `vlm`/`sglang` 后端时，系统会自动：
  1. 检查 sglang 服务是否运行
  2. 如果未运行，尝试自动启动 (Linux)
  3. 如果启动失败，自动回退到 `pipeline` 模式

## 🚀 快速开始

### 🎯 超简单安装

**前提**: 已安装 [uv](https://docs.astral.sh/uv/getting-started/installation/)

#### 两步搞定

```bash
# 1. 克隆项目
git clone <repository-url> && cd mineru_api

# 2. 一键启动 (Linux)
chmod +x bootstrap.sh && ./bootstrap.sh

# 2. 一键启动 (Windows)
bootstrap.bat
```

**就这么简单！** 脚本会：
- `uv sync` 自动创建环境和安装所有依赖 (包括 mineru)
- 创建必要目录
- 直接启动服务

### 🚀 后续启动

如果已经运行过 bootstrap 脚本，后续可以直接：

```bash
# Linux (自动管理 sglang)
uv run start-auto

# Windows (基础模式)
uv run start
```

### 🧪 测试服务

```bash
uv run test           # 基础功能测试
uv run test-multi     # 多格式文件测试
uv run test-backend   # 后端切换测试 (Linux)
```

### 📋 系统要求

#### 基础要求
- **无需预装 Python** (uv 自动管理)
- 8GB+ RAM (推荐 16GB)
- 10GB+ 磁盘空间

#### Windows 额外要求
- Microsoft Office 2016+ 或 LibreOffice
- 仅支持 `pipeline` 模式

#### Linux 额外要求
- LibreOffice: `sudo apt-get install libreoffice`
- 支持所有后端模式 (`pipeline`, `vlm`, `sglang`)

## 📚 API 文档

### 健康检查

```http
GET /health
```

### 文件上传解析

```http
POST /parse/upload
Content-Type: multipart/form-data

file: PDF文件
lang: 语言设置 (默认: ch)
backend: 后端类型 (默认: pipeline)
method: 解析方法 (默认: auto)
callback_url: 回调URL (可选)
```

### JSON 解析请求

```http
POST /predict
Content-Type: application/json

{
    "file_name": "document.pdf",
    "file_content": "base64编码的文件内容",
    "lang": "ch",
    "backend": "pipeline",
    "method": "auto",
    "callback_url": "http://your-callback-url.com/webhook"
}
```

### 查询任务状态

```http
GET /status/{task_id}
```

### 列出所有任务

```http
GET /tasks
```

### 运维接口

#### 查询任务历史
```http
GET /history?status=failed&days=7&limit=100
```

#### 获取单个任务详细历史
```http
GET /history/{task_id}
```

#### 获取统计信息
```http
GET /statistics?days=7
```

#### 清理历史记录
```http
POST /admin/cleanup?days=30
```

## 🔧 配置说明

主要配置项在 `config.py` 中：

- `HOST`: 服务监听地址
- `PORT`: 服务端口
- `MAX_CONCURRENT_TASKS`: 最大并发任务数
- `TASK_TIMEOUT`: 任务超时时间
- `CALLBACK_TIMEOUT`: 回调超时时间

## 📋 任务状态

- `pending`: 任务已创建，等待处理
- `processing`: 正在处理
- `completed`: 处理完成
- `failed`: 处理失败

## 🔔 回调机制

当任务完成时，系统会向指定的 `callback_url` 发送 POST 请求：

```json
{
    "task_id": "uuid",
    "status": "completed",
    "result": {
        "task_id": "uuid",
        "file_name": "document.pdf",
        "output_dir": "/path/to/output",
        "files": {
            "markdown": "/path/to/file.md",
            "content_list": "/path/to/file_content_list.json"
        }
    },
    "timestamp": "2024-01-01T12:00:00"
}
```

## 🛠️ 开发说明

### 项目结构

```
mineru_api/
├── main.py                 # LitServe 服务入口
├── config.py              # 配置文件
├── models.py              # 数据模型
├── services/              # 服务层
│   ├── ocr_service.py     # OCR 解析服务
│   ├── callback_service.py # 回调服务
│   ├── task_manager.py    # 任务管理器
│   └── history_service.py # 任务历史服务
├── utils/                 # 工具函数
│   └── async_utils.py     # 异步工具和结构化日志
├── tools/                 # 运维工具
│   ├── log_analyzer.py    # 日志分析工具
│   └── ops_tools.py       # 运维操作工具
├── test_client.py         # 测试客户端
└── task_history.db        # SQLite 历史数据库
```

### 🔍 日志和监控

#### 结构化日志
系统使用结构化日志记录，便于分析和查询：

- **任务事件日志**: `TASK_EVENT: {...}`
- **错误日志**: `TASK_ERROR: {...}`
- **性能日志**: `TASK_PERFORMANCE: {...}`

#### 日志分析工具
```bash
# 查看特定任务的完整日志
python tools/log_analyzer.py --task-id abc12345

# 生成7天分析报告
python tools/log_analyzer.py --report --days 7

# 性能分析
python tools/log_analyzer.py --performance

# 错误分析
python tools/log_analyzer.py --errors
```

#### 运维工具
```bash
# 健康检查
python tools/ops_tools.py health

# 查看当前状态
python tools/ops_tools.py status

# 查看错误摘要
python tools/ops_tools.py errors --days 7

# 性能报告
python tools/ops_tools.py performance --days 7

# 清理历史数据
python tools/ops_tools.py cleanup --days 30
```

### 扩展开发

1. **添加新的解析选项**: 修改 `OCRRequest` 模型
2. **自定义回调逻辑**: 扩展 `CallbackService`
3. **添加新的 API 端点**: 在 `main.py` 中添加路由

## 🐛 故障排除

1. **任务一直处于 pending 状态**: 检查线程池是否正常工作
2. **回调通知失败**: 检查网络连接和回调 URL 的可访问性
3. **OCR 解析失败**: 检查 demo 模块的依赖是否正确安装

## 📄 许可证

本项目遵循原 Mineru 项目的许可证。
