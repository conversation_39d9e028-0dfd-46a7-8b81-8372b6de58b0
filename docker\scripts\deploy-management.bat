@echo off
REM 部署管理平台的Windows批处理脚本（基础设施 + 杏仁解析 + Web前端）

setlocal enabledelayedexpansion

echo [INFO] 开始部署杏仁解析管理平台
echo [INFO] 包含服务: MySQL + Redis + 杏仁解析 + Web前端 + Nginx

REM 检查环境
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装
    pause
    exit /b 1
)

REM 检查镜像是否存在
set missing_images=
docker images | findstr "almond-parser" >nul || set missing_images=!missing_images! almond-parser
docker images | findstr "web-frontend" >nul || set missing_images=!missing_images! web-frontend

if not "!missing_images!"=="" (
    echo [ERROR] 缺少镜像: !missing_images!
    echo [ERROR] 请先运行 scripts\build.bat 构建镜像
    pause
    exit /b 1
)

echo [SUCCESS] 环境检查通过

REM 切换到docker目录
cd /d "%~dp0\.."

REM 创建必要的目录
echo [INFO] 创建数据目录
if not exist "data\mysql" mkdir data\mysql
if not exist "data\redis" mkdir data\redis
if not exist "data\uploads" mkdir data\uploads
if not exist "data\output" mkdir data\output
if not exist "logs\mysql" mkdir logs\mysql
if not exist "logs\redis" mkdir logs\redis
if not exist "logs\almond" mkdir logs\almond
if not exist "logs\nginx" mkdir logs\nginx

echo [SUCCESS] 目录创建完成

REM 部署管理平台
echo [INFO] 部署管理平台服务
docker-compose -f compose/management.yml up -d

if errorlevel 1 (
    echo [ERROR] 管理平台部署失败
    pause
    exit /b 1
)

echo [SUCCESS] 管理平台服务启动成功

REM 等待服务就绪
echo [INFO] 等待服务就绪...

REM 等待MySQL
echo [INFO] 等待 MySQL 启动...
set timeout=60
:wait_mysql
docker exec parserflow-mysql mysqladmin ping -h localhost --silent >nul 2>&1
if not errorlevel 1 goto mysql_ready
timeout /t 3 /nobreak >nul
set /a timeout-=3
if !timeout! gtr 0 goto wait_mysql
echo [ERROR] MySQL 启动超时
pause
exit /b 1
:mysql_ready
echo [SUCCESS] MySQL 已就绪

REM 等待Redis
echo [INFO] 等待 Redis 启动...
set timeout=30
:wait_redis
docker exec parserflow-redis redis-cli ping >nul 2>&1 | findstr "PONG" >nul
if not errorlevel 1 goto redis_ready
timeout /t 2 /nobreak >nul
set /a timeout-=2
if !timeout! gtr 0 goto wait_redis
echo [ERROR] Redis 启动超时
pause
exit /b 1
:redis_ready
echo [SUCCESS] Redis 已就绪

REM 等待杏仁解析服务
echo [INFO] 等待杏仁解析服务启动...
set timeout=120
:wait_almond
curl -f http://localhost:8000/health >nul 2>&1
if not errorlevel 1 goto almond_ready
timeout /t 3 /nobreak >nul
set /a timeout-=3
if !timeout! gtr 0 goto wait_almond
echo [ERROR] 杏仁解析服务启动超时
docker-compose -f compose/management.yml logs almond-parser --tail=20
pause
exit /b 1
:almond_ready
echo [SUCCESS] 杏仁解析服务已就绪

REM 等待Web前端
echo [INFO] 等待Web前端启动...
set timeout=60
:wait_web
curl -f http://localhost/ >nul 2>&1
if not errorlevel 1 goto web_ready
timeout /t 2 /nobreak >nul
set /a timeout-=2
if !timeout! gtr 0 goto wait_web
echo [WARNING] Web前端启动超时，但可能正常运行
:web_ready
echo [SUCCESS] Web前端已就绪

REM 初始化系统
echo [INFO] 初始化系统
timeout /t 10 /nobreak >nul

REM 检查数据库表是否创建
docker exec parserflow-mysql mysql -u parserflow -pparserflow123 parserflow -e "SHOW TABLES;" >nul 2>&1 | findstr "users" >nul
if not errorlevel 1 (
    echo [SUCCESS] 数据库初始化完成
) else (
    echo [INFO] 数据库正在初始化中...
    timeout /t 20 /nobreak >nul
)

REM 测试系统功能
echo [INFO] 测试系统功能

curl -f http://localhost:8000/health >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] API健康检查通过
) else (
    echo [WARNING] API健康检查失败
)

curl -f http://localhost/ >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] Web前端访问正常
) else (
    echo [WARNING] Web前端访问异常
)

curl -f http://localhost/api/health >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] API代理正常
) else (
    echo [WARNING] API代理异常
)

REM 显示服务状态
echo [INFO] === 管理平台服务状态 ===
docker-compose -f compose/management.yml ps

echo [INFO] === 服务访问地址 ===
echo Web管理平台:   http://localhost
echo 杏仁解析API:   http://localhost:8000
echo API文档:       http://localhost:8000/docs
echo MySQL:         localhost:3306
echo Redis:         localhost:6379

echo [INFO] === 默认账户信息 ===
echo 数据库用户:    parserflow
echo 数据库密码:    parserflow123
echo 数据库名称:    parserflow

echo [INFO] === 数据目录 ===
echo MySQL数据:     .\data\mysql
echo Redis数据:     .\data\redis
echo 上传文件:      .\data\uploads
echo 输出文件:      .\data\output
echo 日志文件:      .\logs\

echo [INFO] === 管理命令 ===
echo 查看日志:      docker-compose -f compose/management.yml logs -f
echo 重启服务:      docker-compose -f compose/management.yml restart
echo 停止服务:      docker-compose -f compose/management.yml down
echo 添加解析节点:  scripts\deploy-mineru-api.bat

echo [SUCCESS] 管理平台部署完成！
echo [INFO] 🎉 访问地址: http://localhost
echo [INFO] 📚 API文档: http://localhost:8000/docs
echo.
echo [INFO] 💡 下一步操作：
echo   1. 访问 Web 管理平台进行系统配置
echo   2. 运行 scripts\deploy-mineru-api.bat 添加解析节点
echo   3. 运行 scripts\scale-mineru.bat 3 扩展解析集群

pause
