#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
为 mineru_nodes 表添加 service_type 字段的迁移脚本
"""

import pymysql
import os
from pathlib import Path

def migrate():
    """执行迁移"""
    try:
        # 从环境变量获取数据库配置
        db_config = {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', '123456'),
            'database': os.getenv('MYSQL_DATABASE', 'almond_parser'),
            'charset': 'utf8mb4'
        }
        
        print(f"连接数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 连接数据库
        connection = pymysql.connect(**db_config)
        
        try:
            with connection.cursor() as cursor:
                # 检查 service_type 字段是否已存在
                check_column_sql = """
                SELECT COUNT(*) as count
                FROM information_schema.columns 
                WHERE table_name = 'mineru_nodes' 
                AND column_name = 'service_type'
                AND table_schema = %s
                """
                
                cursor.execute(check_column_sql, (db_config['database'],))
                result = cursor.fetchone()
                
                if result and result[0] > 0:
                    print("✅ service_type 字段已存在，跳过迁移")
                    return
                
                # 添加 service_type 字段
                add_column_sql = """
                ALTER TABLE mineru_nodes 
                ADD COLUMN service_type ENUM('document', 'knowledge_base', 'universal') 
                DEFAULT 'universal' 
                COMMENT '节点服务类型' 
                AFTER parse_mode
                """
                
                cursor.execute(add_column_sql)
                print("✅ 成功添加 service_type 字段到 mineru_nodes 表")
                
                # 为现有记录设置默认值（通用节点）
                update_existing_sql = """
                UPDATE mineru_nodes 
                SET service_type = 'universal' 
                WHERE service_type IS NULL
                """
                
                cursor.execute(update_existing_sql)
                print("✅ 成功为现有记录设置 service_type 默认值")
                
                # 提交事务
                connection.commit()
                print("✅ 迁移完成")
                
        finally:
            connection.close()
            
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        raise


if __name__ == "__main__":
    migrate()
