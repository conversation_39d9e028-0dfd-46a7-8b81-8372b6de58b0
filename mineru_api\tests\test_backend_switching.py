"""
后端切换测试脚本
"""
import asyncio
import platform
from pathlib import Path
from test_client import MineruAPIClient
from loguru import logger


async def test_sglang_management():
    """测试 sglang 服务管理"""
    if platform.system().lower() != "linux":
        logger.info("Windows 平台，跳过 sglang 管理测试")
        return True
    
    logger.info("=== 测试 sglang 服务管理 ===")
    
    client = MineruAPIClient("http://localhost:8000")
    
    try:
        # 检查状态
        response = await client.client.get(f"{client.base_url}/sglang/status")
        status = response.json()
        logger.info(f"sglang 状态: {status}")
        
        # 如果未运行，尝试启动
        if not status.get("health_check", False):
            logger.info("启动 sglang 服务...")
            response = await client.client.post(f"{client.base_url}/sglang/start")
            
            if response.status_code == 200:
                result = response.json()
                logger.success(f"✅ {result['message']}")
                
                # 等待服务启动
                await asyncio.sleep(5)
                
                # 再次检查状态
                response = await client.client.get(f"{client.base_url}/sglang/status")
                status = response.json()
                logger.info(f"启动后状态: {status}")
                
                return status.get("health_check", False)
            else:
                logger.error(f"❌ 启动失败: {response.text}")
                return False
        else:
            logger.success("✅ sglang 服务已运行")
            return True
            
    except Exception as e:
        logger.error(f"sglang 管理测试失败: {e}")
        return False
    finally:
        await client.close()


async def test_backend_modes():
    """测试不同后端模式"""
    logger.info("=== 测试后端模式切换 ===")
    
    client = MineruAPIClient("http://localhost:8000")
    
    # 查找测试文件
    demo_dir = Path(__file__).parent.parent / "demo" / "pdfs"
    test_files = list(demo_dir.glob("*.pdf"))
    
    if not test_files:
        logger.error("未找到测试PDF文件")
        return False
    
    test_file = test_files[0]
    logger.info(f"使用测试文件: {test_file.name}")
    
    # 定义测试后端
    backends_to_test = ["pipeline"]
    
    if platform.system().lower() == "linux":
        backends_to_test.extend(["vlm", "sglang", "vlm-sglang-client"])
    
    results = {}
    
    try:
        for backend in backends_to_test:
            logger.info(f"\n--- 测试后端: {backend} ---")
            
            try:
                # 提交任务
                result = await client.parse_file_upload(
                    test_file,
                    lang="ch",
                    backend=backend,
                    method="auto"
                )
                
                task_id = result["task_id"]
                logger.info(f"任务已提交: {task_id}")
                
                # 等待完成
                final_status = await client.wait_for_completion(task_id, timeout=300)
                
                if final_status and final_status["status"] == "completed":
                    metadata = final_status.get("result", {}).get("metadata", {})
                    actual_backend = metadata.get("backend", "unknown")
                    server_url = metadata.get("server_url")
                    
                    logger.success(f"✅ {backend} 模式成功")
                    logger.info(f"实际使用后端: {actual_backend}")
                    if server_url:
                        logger.info(f"服务器URL: {server_url}")
                    
                    results[backend] = {
                        "success": True,
                        "actual_backend": actual_backend,
                        "server_url": server_url
                    }
                else:
                    error_msg = final_status.get("error", "未知错误") if final_status else "任务超时"
                    logger.error(f"❌ {backend} 模式失败: {error_msg}")
                    
                    results[backend] = {
                        "success": False,
                        "error": error_msg
                    }
                
            except Exception as e:
                logger.error(f"❌ {backend} 模式异常: {e}")
                results[backend] = {
                    "success": False,
                    "error": str(e)
                }
        
        # 结果汇总
        logger.info("\n=== 后端测试结果汇总 ===")
        success_count = 0
        
        for backend, result in results.items():
            if result["success"]:
                success_count += 1
                actual = result.get("actual_backend", "unknown")
                status_icon = "✅"
                status_text = f"成功 (实际: {actual})"
            else:
                status_icon = "❌"
                status_text = f"失败 ({result.get('error', '未知错误')})"
            
            logger.info(f"{status_icon} {backend:20} - {status_text}")
        
        logger.info(f"\n总成功率: {success_count}/{len(backends_to_test)} ({success_count/len(backends_to_test)*100:.1f}%)")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"后端测试异常: {e}")
        return False
    finally:
        await client.close()


async def test_backend_fallback():
    """测试后端回退机制"""
    if platform.system().lower() != "linux":
        logger.info("Windows 平台，跳过后端回退测试")
        return True
    
    logger.info("=== 测试后端回退机制 ===")
    
    client = MineruAPIClient("http://localhost:8000")
    
    try:
        # 先停止 sglang 服务
        logger.info("停止 sglang 服务以测试回退...")
        response = await client.client.post(f"{client.base_url}/sglang/stop")
        
        if response.status_code == 200:
            logger.info("sglang 服务已停止")
        
        # 等待服务停止
        await asyncio.sleep(2)
        
        # 查找测试文件
        demo_dir = Path(__file__).parent.parent / "demo" / "pdfs"
        test_files = list(demo_dir.glob("*.pdf"))
        
        if not test_files:
            logger.error("未找到测试PDF文件")
            return False
        
        test_file = test_files[0]
        
        # 使用 vlm 后端（应该回退到 pipeline）
        logger.info("使用 vlm 后端（sglang 不可用，应回退到 pipeline）...")
        
        result = await client.parse_file_upload(
            test_file,
            lang="ch",
            backend="vlm",
            method="auto"
        )
        
        task_id = result["task_id"]
        logger.info(f"任务已提交: {task_id}")
        
        # 等待完成
        final_status = await client.wait_for_completion(task_id, timeout=300)
        
        if final_status and final_status["status"] == "completed":
            metadata = final_status.get("result", {}).get("metadata", {})
            actual_backend = metadata.get("backend", "unknown")
            original_backend = metadata.get("original_backend", "unknown")
            
            if actual_backend == "pipeline" and original_backend == "vlm":
                logger.success("✅ 后端回退机制工作正常")
                logger.info(f"请求后端: {original_backend} -> 实际后端: {actual_backend}")
                return True
            else:
                logger.error(f"❌ 后端回退异常: {original_backend} -> {actual_backend}")
                return False
        else:
            error_msg = final_status.get("error", "未知错误") if final_status else "任务超时"
            logger.error(f"❌ 回退测试失败: {error_msg}")
            return False
            
    except Exception as e:
        logger.error(f"后端回退测试异常: {e}")
        return False
    finally:
        await client.close()


async def main():
    """主函数"""
    logger.info("MineruAPI 后端切换测试")
    logger.info(f"平台: {platform.system()}")
    logger.info("请确保 MineruAPI 服务已启动")
    
    input("按 Enter 键开始测试...")
    
    # 健康检查
    client = MineruAPIClient("http://localhost:8000")
    try:
        health = await client.health_check()
        logger.info(f"服务状态: {health}")
    except Exception as e:
        logger.error(f"服务不可用: {e}")
        return
    finally:
        await client.close()
    
    # 测试 sglang 管理
    sglang_ok = await test_sglang_management()
    
    # 测试后端模式
    backend_ok = await test_backend_modes()
    
    # 测试回退机制
    fallback_ok = await test_backend_fallback()
    
    # 总结
    logger.info("\n" + "=" * 50)
    logger.info("📊 测试结果总结")
    logger.info("=" * 50)
    
    tests = [
        ("sglang 管理", sglang_ok),
        ("后端切换", backend_ok),
        ("回退机制", fallback_ok)
    ]
    
    success_count = 0
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name:15} - {status}")
        if result:
            success_count += 1
    
    logger.info(f"\n总体结果: {success_count}/{len(tests)} 通过")
    
    if success_count == len(tests):
        logger.success("🎉 所有测试通过！")
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置")


if __name__ == "__main__":
    asyncio.run(main())
