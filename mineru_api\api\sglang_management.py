"""
sglang 服务管理 API
提供 sglang 服务的启动、停止、重启、健康检查等功能
"""
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Header
from loguru import logger

from ..services.sglang_manager import sglang_manager
from ..services.sglang_health_monitor import sglang_health_monitor
from ..auth import verify_api_key

router = APIRouter(prefix="/sglang", tags=["sglang管理"])


@router.get("/status")
async def get_sglang_status(
    authorization: str = Header(None)
):
    """
    获取 sglang 服务状态
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        status = await sglang_manager.get_status()
        logger.info("查询 sglang 服务状态")
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 sglang 状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取服务状态失败")


@router.get("/health")
async def get_sglang_health(
    authorization: str = Header(None)
):
    """
    获取 sglang 服务健康状态（详细）
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        health_status = await sglang_manager.get_health_status()
        logger.info("查询 sglang 健康状态")
        return health_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 sglang 健康状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取健康状态失败")


@router.post("/start")
async def start_sglang_server(
    force_restart: bool = False,
    authorization: str = Header(None)
):
    """
    启动 sglang 服务
    
    - **force_restart**: 是否强制重启（如果已运行）
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        logger.info(f"启动 sglang 服务请求 (force_restart={force_restart})")
        
        success = await sglang_manager.start_server(force_restart=force_restart)
        
        if success:
            return {
                "success": True,
                "message": "sglang 服务启动成功",
                "url": sglang_manager.url
            }
        else:
            raise HTTPException(status_code=500, detail="sglang 服务启动失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动 sglang 服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动服务失败: {str(e)}")


@router.post("/stop")
async def stop_sglang_server(
    force_kill: bool = False,
    authorization: str = Header(None)
):
    """
    停止 sglang 服务
    
    - **force_kill**: 是否强制杀死所有相关进程
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        logger.info(f"停止 sglang 服务请求 (force_kill={force_kill})")
        
        success = await sglang_manager.stop_server(force_kill=force_kill)
        
        if success:
            return {
                "success": True,
                "message": "sglang 服务停止成功"
            }
        else:
            return {
                "success": False,
                "message": "sglang 服务停止失败或服务未运行"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止 sglang 服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止服务失败: {str(e)}")


@router.post("/restart")
async def restart_sglang_server(
    force_kill: bool = False,
    authorization: str = Header(None)
):
    """
    重启 sglang 服务
    
    - **force_kill**: 是否强制杀死所有相关进程后重启
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        logger.info(f"重启 sglang 服务请求 (force_kill={force_kill})")
        
        success = await sglang_manager.restart_server(force_kill=force_kill)
        
        if success:
            return {
                "success": True,
                "message": "sglang 服务重启成功",
                "url": sglang_manager.url
            }
        else:
            raise HTTPException(status_code=500, detail="sglang 服务重启失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重启 sglang 服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启服务失败: {str(e)}")


@router.post("/force-restart")
async def force_restart_sglang_server(
    authorization: str = Header(None)
):
    """
    强制重启 sglang 服务（杀死所有相关进程）
    
    这是解决 sglang 服务僵死问题的终极方案
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        logger.warning("🔥 强制重启 sglang 服务请求")
        
        success = await sglang_manager.force_restart_server()
        
        if success:
            return {
                "success": True,
                "message": "sglang 服务强制重启成功",
                "url": sglang_manager.url,
                "warning": "已杀死所有相关进程并重新启动"
            }
        else:
            raise HTTPException(status_code=500, detail="sglang 服务强制重启失败")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制重启 sglang 服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"强制重启失败: {str(e)}")


@router.post("/kill-all-processes")
async def kill_all_sglang_processes(
    authorization: str = Header(None)
):
    """
    杀死所有 sglang 相关进程（不重启）
    
    用于清理僵死的进程，解决进程过多的问题
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        logger.warning("🔥 杀死所有 sglang 进程请求")
        
        kill_result = await sglang_health_monitor.force_kill_all_sglang_processes()
        
        return {
            "success": True,
            "message": f"已杀死 {kill_result['total_killed']} 个进程",
            "details": kill_result,
            "warning": "服务已停止，如需使用请手动启动"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"杀死 sglang 进程失败: {e}")
        raise HTTPException(status_code=500, detail=f"杀死进程失败: {str(e)}")


@router.get("/processes")
async def list_sglang_processes(
    authorization: str = Header(None)
):
    """
    列出所有 sglang 相关进程
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        health_status = await sglang_health_monitor.get_detailed_status()
        process_info = health_status.get("health_check", {}).get("process_info", {})
        
        return {
            "main_process": process_info.get("main_process"),
            "child_processes": process_info.get("child_processes", []),
            "total_processes": process_info.get("total_processes", 0),
            "memory_usage_mb": process_info.get("memory_usage", 0),
            "cpu_usage_percent": process_info.get("cpu_usage", 0)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"列出 sglang 进程失败: {e}")
        raise HTTPException(status_code=500, detail="列出进程失败")


@router.get("/recommendations")
async def get_sglang_recommendations(
    authorization: str = Header(None)
):
    """
    获取 sglang 服务优化建议
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)
        
        health_status = await sglang_health_monitor.get_detailed_status()
        
        return {
            "recommendations": health_status.get("recommendations", []),
            "monitor_info": health_status.get("monitor_info", {}),
            "health_summary": {
                "healthy": health_status.get("health_check", {}).get("healthy", False),
                "consecutive_failures": health_status.get("monitor_info", {}).get("consecutive_failures", 0),
                "total_processes": health_status.get("health_check", {}).get("process_info", {}).get("total_processes", 0)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取建议失败: {e}")
        raise HTTPException(status_code=500, detail="获取建议失败")
