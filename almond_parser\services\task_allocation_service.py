# -*- encoding: utf-8 -*-
"""
任务分配服务 - 事件驱动的任务分配机制
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from loguru import logger

from almond_parser.db.models.document import Document, DocumentStatus
from almond_parser.db.models.mineru_node import ServiceType, MinerUNode, ParseMode, NodeStatus
from almond_parser.db.database import get_async_session


@dataclass
class NodeTypeCapacity:
    """节点类型容量信息"""
    parse_mode: ParseMode
    service_type: ServiceType
    total_capacity: int
    current_load: int
    available_capacity: int
    node_count: int
    priority: int

    @property
    def type_key(self) -> str:
        """节点类型唯一标识"""
        return f"{self.parse_mode.value}_{self.service_type.value}"


class TaskAllocationService:
    """任务分配服务 - 单例模式"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def allocate_pending_tasks_by_node_type(self, max_allocations: int = 10) -> Dict[str, Any]:
        """
        按节点类型分配任务 - 解决容量计算不准确的问题

        Args:
            max_allocations: 最大分配数量

        Returns:
            分配统计信息
        """
        try:
            async with get_async_session() as db:
                logger.info(f"🚀 开始按节点类型分配任务，最大分配数量: {max_allocations}")

                # 1. 获取所有节点类型的容量信息
                node_type_capacities = await self._get_node_type_capacities(db)

                if not node_type_capacities:
                    logger.info("📊 没有可用的节点类型")
                    return {"allocated": 0, "failed": 0, "total_pending": 0, "reason": "no_available_nodes"}

                # 2. 按优先级排序节点类型
                sorted_capacities = sorted(node_type_capacities, key=lambda x: x.priority, reverse=True)

                logger.info(f"📋 发现 {len(sorted_capacities)} 种节点类型:")
                for cap in sorted_capacities:
                    logger.info(f"  - {cap.type_key}: 容量={cap.available_capacity}/{cap.total_capacity}, 节点数={cap.node_count}, 优先级={cap.priority}")

                allocated_count = 0
                failed_count = 0
                total_pending = 0
                allocation_details = {}

                # 3. 按优先级顺序处理每种节点类型
                for node_type_info in sorted_capacities:
                    if allocated_count >= max_allocations:
                        logger.info(f"⏹️ 已达到最大分配数量限制: {max_allocations}")
                        break

                    if node_type_info.available_capacity <= 0:
                        logger.debug(f"⏸️ 节点类型 {node_type_info.type_key} 无可用容量")
                        continue

                    # 计算该类型可分配的任务数量
                    remaining_allocations = max_allocations - allocated_count
                    type_max_allocations = min(remaining_allocations, node_type_info.available_capacity)

                    logger.info(f"🎯 处理节点类型 {node_type_info.type_key}, 可分配数量: {type_max_allocations}")

                    # 4. 查询适合该节点类型的待分配任务
                    suitable_tasks = await self._get_tasks_for_node_type(db, node_type_info, type_max_allocations)

                    if not suitable_tasks:
                        logger.debug(f"📭 节点类型 {node_type_info.type_key} 没有适合的待分配任务")
                        continue

                    logger.info(f"📝 节点类型 {node_type_info.type_key} 找到 {len(suitable_tasks)} 个适合的任务")
                    total_pending += len(suitable_tasks)

                    # 5. 分配任务到该类型节点
                    type_allocated = 0
                    type_failed = 0

                    for task in suitable_tasks:
                        try:
                            success = await self._allocate_task_to_node_type(db, task, node_type_info)
                            if success:
                                type_allocated += 1
                                allocated_count += 1
                                logger.debug(f"✅ 任务分配成功: {task.document_id} -> {node_type_info.type_key}")
                            else:
                                type_failed += 1
                                failed_count += 1
                                logger.debug(f"❌ 任务分配失败: {task.document_id} -> {node_type_info.type_key}")

                        except Exception as e:
                            logger.error(f"分配任务异常: {task.document_id} -> {node_type_info.type_key}, 错误: {e}")
                            type_failed += 1
                            failed_count += 1

                    allocation_details[node_type_info.type_key] = {
                        "allocated": type_allocated,
                        "failed": type_failed,
                        "available_capacity": node_type_info.available_capacity,
                        "suitable_tasks": len(suitable_tasks)
                    }

                    logger.info(f"📊 节点类型 {node_type_info.type_key} 分配完成: 成功={type_allocated}, 失败={type_failed}")

                stats = {
                    "allocated": allocated_count,
                    "failed": failed_count,
                    "total_pending": total_pending,
                    "allocation_method": "by_node_type",
                    "node_type_details": allocation_details
                }

                logger.info(f"🎉 按节点类型分配完成: {stats}")
                return stats

        except Exception as e:
            logger.error(f"按节点类型分配任务失败: {e}")
            return {"error": str(e)}

    async def allocate_pending_tasks(self, max_allocations: int = 10) -> Dict[str, Any]:
        """
        分配pending状态的任务（带容量检查优化）

        Args:
            max_allocations: 最大分配数量

        Returns:
            分配统计信息
        """
        try:
            async with get_async_session() as db:
                # 1. 先检查系统总容量
                from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
                from sqlalchemy import func

                capacity_result = await db.execute(
                    select(
                        func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                        func.sum(MinerUNode.current_tasks).label("current_load")
                    ).where(
                        MinerUNode.is_enabled == True,
                        MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])
                    )
                )
                capacity_row = capacity_result.first()
                total_capacity = capacity_row.total_capacity or 0
                current_load = capacity_row.current_load or 0
                available_capacity = max(0, total_capacity - current_load)

                logger.info(f"📊 系统容量检查: 总容量={total_capacity}, 当前负载={current_load}, 可用={available_capacity}")

                # 如果没有可用容量，直接返回
                if available_capacity <= 0:
                    logger.info("⏸️  系统容量已满，跳过任务分配")
                    return {"allocated": 0, "failed": 0, "total_pending": 0, "reason": "capacity_full"}

                # 2. 查找pending状态的任务，按优先级排序
                pending_tasks = await self._get_pending_tasks(db, max_allocations)
                logger.info(f'合计pending任务为：{len(pending_tasks)}')
                if not pending_tasks:
                    logger.debug("没有pending状态的任务需要分配")
                    return {"allocated": 0, "failed": 0, "total_pending": 0}

                # 3. 调整分配数量不超过可用容量
                actual_max = min(max_allocations, available_capacity, len(pending_tasks))
                logger.info(f"🎯 调整分配数量: 计划={max_allocations}, 可用容量={available_capacity}, 等待任务={len(pending_tasks)}, 实际={actual_max}")

                allocated_count = 0
                failed_count = 0
                capacity_exhausted = False

                # 4. 🔑 关键优化：循环中每次分配前检查可用容量
                for i, document in enumerate(pending_tasks):
                    try:
                        success = await self._allocate_single_task(db, document)
                        if success:
                            allocated_count += 1
                            logger.debug(f"✅ 任务分配成功: {document.document_id} ({allocated_count}/{actual_max})")
                        else:
                            failed_count += 1
                            logger.debug(f"❌ 任务分配失败: {document.document_id}")

                    except Exception as e:
                        logger.error(f"分配任务失败: {document.document_id}, 错误: {e}")
                        failed_count += 1

                stats = {
                    "allocated": allocated_count,
                    "failed": failed_count,
                    "total_pending": len(pending_tasks),
                    "capacity_exhausted": capacity_exhausted,
                    "available_capacity": available_capacity
                }

                logger.info(f"任务分配完成: {stats}")
                return stats

        except Exception as e:
            logger.error(f"分配pending任务失败: {e}")
            return {"error": str(e)}

    async def allocate_pending_tasks_with_capacity_control(
        self,
        max_allocations: int = 10,
        available_capacity: int = None
    ) -> Dict[str, Any]:
        """
        带容量控制的任务分配 - 确保不超过节点实际容量

        Args:
            max_allocations: 最大分配数量
            available_capacity: 可用容量（如果提供，会进行二次验证）

        Returns:
            分配统计信息
        """
        try:
            async with get_async_session() as db:
                # 1. 实时验证节点容量
                from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
                from sqlalchemy import func

                capacity_result = await db.execute(
                    select(
                        func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                        func.sum(MinerUNode.current_tasks).label("current_load")
                    ).where(
                        MinerUNode.is_enabled == True,
                        MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])
                    )
                )
                capacity_row = capacity_result.first()
                total_capacity = capacity_row.total_capacity or 0
                current_load = capacity_row.current_load or 0
                real_available = total_capacity - current_load

                logger.info(f"🔍 实时容量检查: 总容量={total_capacity}, 当前负载={current_load}, 实际可用={real_available}")

                # 2. 如果没有实际可用容量，直接返回
                if real_available <= 0:
                    logger.warning("⚠️  实时检查发现没有可用容量，停止分配")
                    return {"allocated": 0, "failed": 0, "total_pending": 0, "reason": "no_capacity"}

                # 3. 调整分配数量为实际可用容量
                actual_max = min(max_allocations, real_available)
                logger.info(f"📊 调整分配数量: 计划={max_allocations}, 实际可用={real_available}, 最终={actual_max}")

                # 4. 查找待分配任务（只查找真正未开始的任务）
                pending_tasks = await self._get_unstarted_tasks(db, actual_max)

                if not pending_tasks:
                    logger.debug("没有未开始的任务需要分配")
                    return {"allocated": 0, "failed": 0, "total_pending": 0}

                logger.info(f"发现 {len(pending_tasks)} 个未开始任务，开始分配")

                allocated_count = 0
                failed_count = 0

                # 5. 逐个分配任务，每次分配前检查容量
                for document in pending_tasks:
                    # 再次检查当前容量
                    current_capacity_result = await db.execute(
                        select(func.sum(MinerUNode.current_tasks)).where(
                            MinerUNode.is_enabled == True,
                            MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])
                        )
                    )
                    current_load_now = current_capacity_result.scalar() or 0

                    if current_load_now >= total_capacity:
                        logger.warning(f"⚠️  容量已满 ({current_load_now}/{total_capacity})，停止分配")
                        break

                    try:
                        success = await self._allocate_single_task_with_capacity_check(db, document)
                        if success:
                            allocated_count += 1
                            logger.info(f"✅ 任务分配成功: {document.document_id} ({allocated_count}/{actual_max})")
                        else:
                            failed_count += 1
                            logger.warning(f"❌ 任务分配失败: {document.document_id}")

                    except Exception as e:
                        logger.error(f"分配任务异常: {document.document_id}, 错误: {e}")
                        failed_count += 1

                stats = {
                    "allocated": allocated_count,
                    "failed": failed_count,
                    "total_pending": len(pending_tasks),
                    "capacity_controlled": True
                }

                logger.info(f"容量控制分配完成: {stats}")
                return stats

        except Exception as e:
            logger.error(f"容量控制分配失败: {e}")
            return {"error": str(e)}

    async def _get_pending_tasks(self, db: AsyncSession, limit: int) -> List[Document]:
        """获取pending状态的任务"""
        try:
            from datetime import datetime
            current_time = datetime.now()  # 使用UTC时间保持一致性

            logger.debug(f"查询pending任务，当前时间: {current_time}")

            # 🔑 关键修复：包含卡住任务的查询条件
            # 查询条件：
            # 1. UPLOADED/PENDING状态且started_at为空（新任务或已清理的任务）
            # 2. PARSING状态但可能卡住的任务（node_id为空或started_at为空）
            # 3. 按创建时间排序（FIFO）
            result = await db.execute(
                select(Document)
                .where(
                    or_(
                        # 新任务和等待重新分配的任务
                        and_(
                            Document.status.in_([
                                DocumentStatus.UPLOADED,    # 新上传的任务
                                DocumentStatus.PENDING      # 等待处理的任务
                            ]),
                            Document.started_at.is_(None)   # 未提交到ARQ或已清理
                        ),
                        # 可能卡住的PARSING任务
                        and_(
                            Document.status == DocumentStatus.PARSING,
                            or_(
                                Document.node_id.is_(None),     # 没有分配节点
                                Document.started_at.is_(None)   # 或已被清理
                            )
                        )
                    )
                )
                .order_by(
                    # 优先处理新上传的文档，然后按创建时间排序
                    Document.status.asc(),  # UPLOADED < PENDING < PARSING
                    Document.created_at.asc()  # 先进先出
                )
                .limit(limit)
            )

            logger.debug(f"执行查询，限制数量: {limit}")

            tasks = result.scalars().all()

            if tasks:
                uploaded_count = sum(1 for t in tasks if t.status == DocumentStatus.UPLOADED)
                pending_count = sum(1 for t in tasks if t.status == DocumentStatus.PENDING)
                parsing_stuck_count = sum(1 for t in tasks if t.status == DocumentStatus.PARSING)
                pending_with_retry = sum(1 for t in tasks if t.status == DocumentStatus.PENDING and (t.retry_count or 0) > 0)
                logger.info(f"找到 {len(tasks)} 个可分配任务: {uploaded_count} 个新上传, {pending_count} 个等待处理, {parsing_stuck_count} 个卡住的解析任务 (其中 {pending_with_retry} 个重试任务)")

                for task in tasks:
                    logger.debug(
                        f"任务 {task.document_id}: 状态={task.status.value}, "
                        f"重试次数={task.retry_count}/{task.max_retries}, "
                        f"started_at={task.started_at}, "
                        f"node_id={task.node_id}, "
                        f"task_id={task.task_id}"
                    )

            return tasks

        except Exception as e:
            logger.error(f"获取pending任务失败: {e}")
            return []

    async def _get_unstarted_tasks(self, db: AsyncSession, limit: int) -> List[Document]:
        """获取真正未开始的任务（UPLOADED 或没有 task_id 的 RETRY_PENDING）"""
        try:
            logger.debug(f"查询未开始任务，限制数量: {limit}")

            # 查询条件：
            # 1. UPLOADED 状态且没有 task_id（全新任务）
            # 2. RETRY_PENDING 状态且没有 task_id（重试任务但已清理）
            result = await db.execute(
                select(Document)
                .where(
                    and_(
                        or_(
                            Document.status == DocumentStatus.UPLOADED,
                            Document.status == DocumentStatus.RETRY_PENDING
                        ),
                        Document.task_id.is_(None),  # 确保没有提交过
                    )
                )
                .order_by(
                    Document.status.asc(),  # UPLOADED 优先
                    Document.created_at.asc()  # 先进先出
                )
                .limit(limit)
            )

            tasks = result.scalars().all()

            if tasks:
                uploaded_count = sum(1 for t in tasks if t.status == DocumentStatus.UPLOADED)
                retry_count = sum(1 for t in tasks if t.status == DocumentStatus.RETRY_PENDING)
                logger.info(f"找到 {len(tasks)} 个未开始任务: {uploaded_count} 个新上传, {retry_count} 个重试")

                for task in tasks:
                    logger.debug(f"未开始任务: {task.document_id}, 状态: {task.status.value}, task_id: {task.task_id}")

            return tasks

        except Exception as e:
            logger.error(f"获取未开始任务失败: {e}")
            return []

    async def _allocate_single_task(self, db: AsyncSession, document: Document) -> bool:
        """分配单个任务 - 只负责调度到队列，让ARQ任务处理容量控制"""
        try:
            # 🔒 智能幂等性检查：区分正常处理和卡住的任务
            if document.status == DocumentStatus.COMPLETED:
                logger.info(f"🔄 文档 {document.document_id} 已完成，跳过分配")
                return True
            elif document.status == DocumentStatus.PARSING:
                # PARSING状态需要进一步检查是否真的在处理中
                if document.node_id and document.started_at:
                    # 有节点ID和开始时间，说明正在正常处理，跳过
                    logger.info(f"🔄 文档 {document.document_id} 正在正常处理中 (node_id: {document.node_id})，跳过分配")
                    return True
                else:
                    # 没有节点ID或开始时间，说明可能卡住了，允许重新处理
                    logger.info(f"🔧 检测到卡住的PARSING任务，允许重新处理: {document.document_id} (node_id: {document.node_id}, started_at: {document.started_at})")
                    # 继续执行分配逻辑

            # 如果是重新分配的任务，记录日志
            # old_task_id = document.task_id
            # if old_task_id:
            #     logger.info(f"🔄 重新分配任务: {document.document_id}, 旧task_id: {old_task_id}")

            # 提交任务到ARQ队列，让enhanced_process_document处理容量检查和节点分配
            job_id = await self._submit_task_to_queue(document)

            if job_id:
                # 更新任务ID和状态为PENDING
                # document.status = DocumentStatus.PENDING
                # document.task_id = job_id
                # document.next_retry_at = None

                # await db.commit()

                logger.info(f"✅ 任务调度成功: {document.document_id}, job_id: {job_id}")
                return True
            else:
                logger.warning(f"任务调度失败: {document.document_id}")
                return False

        except Exception as e:
            logger.error(f"分配单个任务失败: {document.document_id}, 错误: {e}")
            return False

    async def _allocate_single_task_with_capacity_check(self, db: AsyncSession, document: Document) -> bool:
        """带容量检查的单任务分配"""
        try:
            # 确定服务类型
            service_type = self._determine_service_type(document)

            # 1. 检查系统容量（不预分配节点）
            from almond_parser.db.models.mineru_node import NodeStatus
            capacity_result = await db.execute(
                select(
                    func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                    func.sum(MinerUNode.current_tasks).label("current_load")
                ).where(
                    MinerUNode.is_enabled == True,
                    MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])
                )
            )
            capacity_row = capacity_result.first()
            total_capacity = capacity_row.total_capacity or 0
            current_load = capacity_row.current_load or 0
            available_capacity = max(0, total_capacity - current_load)

            if available_capacity <= 0:
                logger.info(f"系统容量已满，跳过任务分配: {document.document_id}")
                return False

            # 2. 提交任务到ARQ队列（让ARQ任务自己分配节点）
            job_id = await self._submit_task_to_queue(document)

            if job_id:
                # 3. 更新文档状态（不设置node_id，让ARQ任务分配）
                document.status = DocumentStatus.PENDING
                document.started_at = datetime.now()  # 记录提交时间防冲突
                document.next_retry_at = None

                await db.commit()

                logger.info(f"✅ 任务分配成功: {document.document_id}, job_id: {job_id}")
                return True
            else:
                logger.warning(f"任务提交失败: {document.document_id}")
                return False

        except Exception as e:
            logger.error(f"带容量检查的任务分配失败: {document.document_id}, 错误: {e}")
            return False

    def _determine_service_type(self, document: Document) -> ServiceType:
        """确定文档的服务类型"""
        # 这里可以根据文档的元数据、文件类型等来推断服务类型
        # 目前简单返回UNIVERSAL，让节点选择器自动处理
        return ServiceType.UNIVERSAL

    async def _submit_task_to_queue(self, document: Document) -> Optional[str]:
        """提交任务到ARQ队列"""
        try:
            # 延迟导入避免循环依赖
            from almond_parser.tasks.arq_app import arq_manager

            # 使用增强的文档处理任务
            job_id = await arq_manager.enqueue_task(
                "enhanced_process_document",
                document_id=document.document_id,
                user_id=document.user_id,
                service_type="auto",  # 让系统自动处理
                parse_mode=document.current_parse_mode or "auto",
                config=document.result_data or {},
                max_retries=document.max_retries or 2
            )

            return job_id

        except Exception as e:
            logger.error(f"提交任务到队列失败: {document.document_id}, 错误: {e}")
            return None

    async def _get_node_type_capacities(self, db: AsyncSession) -> List[NodeTypeCapacity]:
        """获取所有节点类型的容量信息"""
        try:
            # 按 parse_mode 和 service_type 分组查询节点容量
            result = await db.execute(
                select(
                    MinerUNode.parse_mode,
                    MinerUNode.service_type,
                    func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                    func.sum(MinerUNode.current_tasks).label("current_load"),
                    func.count(MinerUNode.id).label("node_count")
                ).where(
                    MinerUNode.is_enabled == True,
                    MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY])
                ).group_by(
                    MinerUNode.parse_mode,
                    MinerUNode.service_type
                )
            )

            capacities = []
            for row in result:
                parse_mode = row.parse_mode
                service_type = row.service_type
                total_capacity = row.total_capacity or 0
                current_load = row.current_load or 0
                available_capacity = max(0, total_capacity - current_load)
                node_count = row.node_count or 0

                # 计算优先级：专用节点 > 通用节点，SGLANG > PIPELINE
                service_priority = {
                    ServiceType.DOCUMENT: 30,
                    ServiceType.KNOWLEDGE_BASE: 20,
                    ServiceType.UNIVERSAL: 10
                }.get(service_type, 0)

                parse_priority = {
                    ParseMode.SGLANG: 2,
                    ParseMode.PIPELINE: 1,
                    ParseMode.AUTO: 0
                }.get(parse_mode, 0)

                priority = service_priority + parse_priority

                capacity_info = NodeTypeCapacity(
                    parse_mode=parse_mode,
                    service_type=service_type,
                    total_capacity=total_capacity,
                    current_load=current_load,
                    available_capacity=available_capacity,
                    node_count=node_count,
                    priority=priority
                )

                capacities.append(capacity_info)

            return capacities

        except Exception as e:
            logger.error(f"获取节点类型容量失败: {e}")
            return []

    async def _get_tasks_for_node_type(
        self,
        db: AsyncSession,
        node_type_info: NodeTypeCapacity,
        limit: int
    ) -> List[Document]:
        """查询适合特定节点类型的待分配任务"""
        try:
            # 构建查询条件 - 简化条件，优先处理UPLOADED状态的任务
            conditions = [
                # 基础状态条件：主要查询UPLOADED状态的新任务
                Document.status == DocumentStatus.UPLOADED,
                Document.status == DocumentStatus.PENDING,
                # Document.status == DocumentStatus.RETRY_PENDING
            ]

            # 根据节点类型添加任务匹配条件
            if node_type_info.service_type == ServiceType.DOCUMENT:
                # 文档解析专用节点：优先处理文档解析任务
                # 这里可以根据文档类型、来源等进一步筛选
                pass  # 暂时不添加额外限制，让所有任务都可以被文档节点处理

            elif node_type_info.service_type == ServiceType.KNOWLEDGE_BASE:
                # 知识库专用节点：优先处理知识库相关任务
                # 可以根据文档的用途、标签等筛选
                pass  # 暂时不添加额外限制

            # UNIVERSAL 节点可以处理所有类型的任务

            # 解析模式匹配：暂时简化，让所有节点都可以处理所有任务


            # 执行查询
            result = await db.execute(
                select(Document)
                .where(or_(*conditions))
                .order_by(
                    # 优先级排序
                    Document.status.desc(),      # UPLOADED < PENDING < PARSING
                    Document.created_at.asc()   # 先进先出
                )
                .limit(limit)
            )

            tasks = result.scalars().all()

            logger.debug(f"节点类型 {node_type_info.type_key} 查询到 {len(tasks)} 个适合的任务")

            return tasks

        except Exception as e:
            logger.error(f"查询节点类型 {node_type_info.type_key} 的适合任务失败: {e}")
            return []

    async def _allocate_task_to_node_type(
        self,
        db: AsyncSession,
        document: Document,
        node_type_info: NodeTypeCapacity
    ) -> bool:
        """将任务分配给特定类型的节点"""
        try:
            # 1. 再次检查该节点类型的可用容量（防止并发冲突）
            capacity_result = await db.execute(
                select(
                    func.sum(MinerUNode.max_concurrent_tasks).label("total_capacity"),
                    func.sum(MinerUNode.current_tasks).label("current_load")
                ).where(
                    MinerUNode.is_enabled == True,
                    MinerUNode.status.in_([NodeStatus.ONLINE, NodeStatus.BUSY]),
                    MinerUNode.parse_mode == node_type_info.parse_mode,
                    MinerUNode.service_type == node_type_info.service_type
                )
            )

            capacity_row = capacity_result.first()
            if capacity_row:
                total_capacity = capacity_row.total_capacity or 0
                current_load = capacity_row.current_load or 0
                available_capacity = max(0, total_capacity - current_load)

                if available_capacity <= 0:
                    logger.warning(f"节点类型 {node_type_info.type_key} 容量已满，跳过任务: {document.document_id}")
                    return False

            # 2. 提交任务到ARQ队列
            job_id = await self._submit_task_to_queue_with_node_type(document, node_type_info)

            if job_id:
                # 3. 更新文档状态
                document.status = DocumentStatus.PENDING
                document.started_at = datetime.now()
                document.next_retry_at = None

                # 记录分配的节点类型信息（用于调试和监控）
                if not document.result_data:
                    document.result_data = {}
                document.result_data["allocated_node_type"] = {
                    "parse_mode": node_type_info.parse_mode.value,
                    "service_type": node_type_info.service_type.value,
                    "allocated_at": datetime.now().isoformat()
                }

                await db.commit()

                logger.debug(f"✅ 任务分配成功: {document.document_id} -> {node_type_info.type_key}, job_id: {job_id}")
                return True
            else:
                logger.warning(f"任务提交失败: {document.document_id} -> {node_type_info.type_key}")
                return False

        except Exception as e:
            logger.error(f"分配任务到节点类型失败: {document.document_id} -> {node_type_info.type_key}, 错误: {e}")
            return False

    async def _submit_task_to_queue_with_node_type(
        self,
        document: Document,
        node_type_info: NodeTypeCapacity
    ) -> Optional[str]:
        """提交任务到ARQ队列（带节点类型信息）"""
        try:
            # 延迟导入避免循环依赖
            from almond_parser.tasks.arq_app import arq_manager

            # 确定服务类型字符串
            service_type_str = {
                ServiceType.DOCUMENT: "document",
                ServiceType.KNOWLEDGE_BASE: "knowledge_base",
                ServiceType.UNIVERSAL: "auto"
            }.get(node_type_info.service_type, "auto")

            # 确定解析模式字符串
            parse_mode_str = document.current_parse_mode or "auto"
            if parse_mode_str == "auto":
                # 根据节点类型推荐解析模式
                if node_type_info.parse_mode == ParseMode.SGLANG:
                    parse_mode_str = "sglang"
                elif node_type_info.parse_mode == ParseMode.PIPELINE:
                    parse_mode_str = "pipeline"

            # 使用增强的文档处理任务
            job_id = await arq_manager.enqueue_task(
                "enhanced_process_document",
                document_id=document.document_id,
                user_id=document.user_id,
                service_type=service_type_str,
                parse_mode=parse_mode_str,
                config=document.result_data or {},
                max_retries=document.max_retries or 2
            )

            return job_id

        except Exception as e:
            logger.error(f"提交任务到队列失败: {document.document_id}, 错误: {e}")
            return None

    async def handle_task_completion(
        self,
        document_id: str,
        success: bool,
        node_id: Optional[int] = None
    ):
        """
        处理任务完成事件（简化版本）- 使用按节点类型分配

        Args:
            document_id: 文档ID
            success: 任务是否成功
            node_id: 节点ID
        """
        try:
            # 使用新的按节点类型分配方法（节点释放在回调中已处理）
            await self.allocate_pending_tasks_by_node_type(max_allocations=3)

        except Exception as e:
            logger.error(f"处理任务完成事件失败: {document_id}, 错误: {e}")

    async def schedule_retry_task(
        self,
        document_id: str,
        retry_delay_minutes: int = 5
    ):
        """
        安排任务重试

        Args:
            document_id: 文档ID
            retry_delay_minutes: 重试延迟（分钟）
        """
        try:
            async with get_async_session() as db:
                result = await db.execute(
                    select(Document).where(Document.document_id == document_id)
                )
                document = result.scalar_one_or_none()

                if not document:
                    logger.error(f"文档不存在: {document_id}")
                    return

                # 设置重试时间
                from datetime import timedelta
                document.next_retry_at = datetime.now() + timedelta(minutes=retry_delay_minutes)
                document.status = DocumentStatus.RETRY_PENDING
                document.retry_count += 1

                await db.commit()

                logger.info(f"任务已安排重试: {document_id}, 延迟 {retry_delay_minutes} 分钟")

        except Exception as e:
            logger.error(f"安排任务重试失败: {document_id}, 错误: {e}")


# 全局实例
task_allocation_service = TaskAllocationService()
