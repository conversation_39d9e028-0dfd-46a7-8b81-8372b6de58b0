# -*- encoding: utf-8 -*-
"""
@File   :test_pdf_start.py
@Time   :2025/9/15 09:28
<AUTHOR>
"""


def extract_pdf_bytes(file_bytes: bytes):
    """
    检查文件字节流是否包含PDF数据，并提取它。
    这可以处理标准PDF和包含PJL头的PDF。

    参数:
        file_bytes (bytes): 文件的完整字节内容。

    返回:
        bytes: 从 '%PDF' 开始的PDF实际内容字节流。

    异常:
        ValueError: 如果在字节流中找不到 '%PDF-' 签名。
    """
    # 1. 检查是否为标准PDF文件
    if file_bytes.startswith(b'%PDF'):
        print("检测到标准PDF文件。")
        return file_bytes

    # 2. 如果不是，则搜索PDF文件头签名 b'%PDF-'
    #    这个签名比 b'%PDF' 更具唯一性，可以避免误判
    print("文件不是以 '%PDF' 开头，正在尝试搜索PJL包装的PDF...")
    pdf_start_index = file_bytes.find(b'%PDF-')

    if pdf_start_index != -1:
        print(f"在索引 {pdf_start_index} 处找到PDF文件头。")
        # 3. 如果找到，返回从该位置开始的所有数据
        return file_bytes[pdf_start_index:]

    # 4. 如果两种方法都失败，则抛出异常
    raise ValueError("文件不是有效的PDF格式，或未包含可识别的PDF数据。")


def  extract_pdf_data(file_path):
    with  open(file_path, 'rb') as file:
        file_bytes = file.read()
        return file_bytes

path1 = r'D:\WXWork\1688857100340944\Cache\File\2025-09\【曹操出行-60.15元-1个行程】高德打车电子发票.pdf'
path2 = r'D:\WXWork\1688857100340944\Cache\File\2025-09\电子银行回函-农业银行.pdf'


file_paths = [path2]

for i in file_paths:
    file_bytes = extract_pdf_data(i)
    extract_pdf_bytes(file_bytes)
