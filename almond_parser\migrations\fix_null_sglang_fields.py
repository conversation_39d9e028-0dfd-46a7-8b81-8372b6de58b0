#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
修复SGLang相关字段的NULL值
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger
from sqlalchemy import text

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.db.database import get_async_session


async def fix_null_sglang_fields():
    """修复SGLang相关字段的NULL值"""
    
    # SQL语句列表
    sql_statements = [
        # 修复整数字段的NULL值
        """
        UPDATE mineru_nodes 
        SET sglang_consecutive_failures = 0 
        WHERE sglang_consecutive_failures IS NULL
        """,
        
        """
        UPDATE mineru_nodes 
        SET sglang_restart_count = 0 
        WHERE sglang_restart_count IS NULL
        """,
        
        """
        UPDATE mineru_nodes 
        SET sglang_current_cycle_restarts = 0 
        WHERE sglang_current_cycle_restarts IS NULL
        """,
        
        # 修复布尔字段的NULL值
        """
        UPDATE mineru_nodes 
        SET sglang_alert_sent = FALSE 
        WHERE sglang_alert_sent IS NULL
        """,
        
        # 设置字段为NOT NULL（如果需要的话）
        """
        ALTER TABLE mineru_nodes 
        MODIFY COLUMN sglang_consecutive_failures INT NOT NULL DEFAULT 0
        """,
        
        """
        ALTER TABLE mineru_nodes 
        MODIFY COLUMN sglang_restart_count INT NOT NULL DEFAULT 0
        """,
        
        """
        ALTER TABLE mineru_nodes 
        MODIFY COLUMN sglang_current_cycle_restarts INT NOT NULL DEFAULT 0
        """,
        
        """
        ALTER TABLE mineru_nodes 
        MODIFY COLUMN sglang_alert_sent BOOLEAN NOT NULL DEFAULT FALSE
        """
    ]
    
    try:
        async with get_async_session() as db:
            logger.info("开始修复SGLang字段NULL值...")
            
            # 先查看当前NULL值情况
            check_sql = """
            SELECT 
                COUNT(*) as total_nodes,
                SUM(CASE WHEN sglang_consecutive_failures IS NULL THEN 1 ELSE 0 END) as null_consecutive_failures,
                SUM(CASE WHEN sglang_restart_count IS NULL THEN 1 ELSE 0 END) as null_restart_count,
                SUM(CASE WHEN sglang_current_cycle_restarts IS NULL THEN 1 ELSE 0 END) as null_cycle_restarts,
                SUM(CASE WHEN sglang_alert_sent IS NULL THEN 1 ELSE 0 END) as null_alert_sent
            FROM mineru_nodes
            """
            
            result = await db.execute(text(check_sql))
            stats = result.fetchone()
            
            logger.info(f"📊 当前NULL值统计:")
            logger.info(f"  - 总节点数: {stats.total_nodes}")
            logger.info(f"  - sglang_consecutive_failures为NULL: {stats.null_consecutive_failures}")
            logger.info(f"  - sglang_restart_count为NULL: {stats.null_restart_count}")
            logger.info(f"  - sglang_current_cycle_restarts为NULL: {stats.null_cycle_restarts}")
            logger.info(f"  - sglang_alert_sent为NULL: {stats.null_alert_sent}")
            
            if (stats.null_consecutive_failures == 0 and 
                stats.null_restart_count == 0 and 
                stats.null_cycle_restarts == 0 and 
                stats.null_alert_sent == 0):
                logger.success("✅ 没有发现NULL值，无需修复")
                return
            
            # 执行修复SQL
            for i, sql in enumerate(sql_statements, 1):
                try:
                    logger.info(f"执行修复SQL {i}/{len(sql_statements)}")
                    result = await db.execute(text(sql.strip()))
                    
                    # 对于UPDATE语句，显示影响的行数
                    if sql.strip().upper().startswith('UPDATE'):
                        logger.info(f"  影响行数: {result.rowcount}")
                    
                    await db.commit()
                    logger.success(f"✅ SQL语句 {i} 执行成功")
                    
                except Exception as e:
                    if "Unknown column" in str(e):
                        logger.warning(f"⚠️ 字段不存在，跳过: {e}")
                    elif "Duplicate column name" in str(e):
                        logger.warning(f"⚠️ 字段已存在，跳过: {e}")
                    else:
                        logger.error(f"❌ SQL语句 {i} 执行失败: {e}")
                        # 对于ALTER语句失败，继续执行后续语句
                        if not sql.strip().upper().startswith('ALTER'):
                            raise
            
            # 再次检查修复结果
            result = await db.execute(text(check_sql))
            stats_after = result.fetchone()
            
            logger.info(f"📊 修复后NULL值统计:")
            logger.info(f"  - sglang_consecutive_failures为NULL: {stats_after.null_consecutive_failures}")
            logger.info(f"  - sglang_restart_count为NULL: {stats_after.null_restart_count}")
            logger.info(f"  - sglang_current_cycle_restarts为NULL: {stats_after.null_cycle_restarts}")
            logger.info(f"  - sglang_alert_sent为NULL: {stats_after.null_alert_sent}")
            
            logger.success("🎉 SGLang字段NULL值修复完成")
            
    except Exception as e:
        logger.error(f"修复SGLang字段NULL值失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        await fix_null_sglang_fields()
        logger.success("数据库修复完成")
    except Exception as e:
        logger.error(f"数据库修复失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
