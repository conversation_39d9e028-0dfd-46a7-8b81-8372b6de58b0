<template>
  <div class="doc-document-list">
    <div class="page-header">
      <h2 class="page-title">DOC文档</h2>
    </div>

    <!-- 服务器选择tabs移到header下面 -->
    <el-tabs
      v-model="activeTab"
      type="card"
      class="server-tabs"
      @tab-change="handleTabChange"
      v-if="serverList.length > 0"
    >
        <el-tab-pane
          v-for="server in serverList"
          :key="server.name"
          :label="server.label"
          :name="server.name"
        >
          <template #label>
            <div class="tab-label">
              <el-icon><Monitor /></el-icon>
              <span>{{ server.label }}</span>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>

      <div v-else class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载服务器配置中...</span>
      </div>

    <!-- 内容区域移到tabs外面 -->
    <div class="content-container" v-if="serverList.length > 0">
      <!-- 数据源切换 -->
      <div class="source-type-switch">
        <div class="source-switch-label">数据源类型</div>
        <el-radio-group
          v-model="queryParams.source_type"
          @change="handleSourceTypeChange"
          size="default"
          class="source-radio-group"
        >
          <el-radio-button label="document">
            <el-icon><Files /></el-icon>
            <span>多文档</span>
          </el-radio-button>
          <el-radio-button label="knowledge">
            <el-icon><Collection /></el-icon>
            <span>知识库</span>
          </el-radio-button>
        </el-radio-group>
      </div>

      <div class="tab-content">
        <div class="filter-card">
          <DOCDocumentFilter
            :query-params="queryParams"
            :source-type="queryParams.source_type || 'document'"
            @search="handleSearch"
            @reset="handleReset"
          />
        </div>

        <div class="table-card">
          <DOCDocumentTable
            :loading="loading"
            :documents="documentList"
            :total="total"
            :current-page="queryParams.page"
            :page-size="queryParams.page_size"
            @retry="handleRetry"
            @view-details="handleViewDetails"
            @view-logs="handleViewLogs"
            @download="handleDownload"
            @update:current-page="handleCurrentChange"
            @update:page-size="handleSizeChange"
            @sort-change="handleSortChange"
          />
        </div>
      </div>
    </div>

    <DOCDocumentDetails
      v-model="detailsVisible"
      :document="currentDocument"
    />

    <DOCDocumentLogs
      v-model:visible="logsVisible"
      :server="activeTab"
      :document-id="currentLogDocumentId"
      @close="handleCloseLog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Monitor, Loading, Files, Collection } from '@element-plus/icons-vue'
import {
  getServerList,
  queryDOCDocuments,
  retryDOCDocument,
  downloadDOCOriginalFile
} from '@/api/docDocument'
import type {
  DOCDocument,
  DOCQueryParams,
  ServerConfig
} from '@/types/docDocument'
import DOCDocumentFilter from '@/components/document/DOCDocumentFilter.vue'
import DOCDocumentTable from '@/components/document/DOCDocumentTable.vue'
import DOCDocumentDetails from '@/components/document/DOCDocumentDetails.vue'
import DOCDocumentLogs from '@/components/document/DOCDocumentLogs.vue'

// 数据状态
const loading = ref(false)
const documentList = ref<DOCDocument[]>([])
const total = ref(0)
const serverList = ref<ServerConfig[]>([])
const activeTab = ref('')
const detailsVisible = ref(false)
const currentDocument = ref<DOCDocument | null>(null)
const logsVisible = ref(false)
const currentLogDocumentId = ref('')

// 查询参数
const queryParams = ref<DOCQueryParams>({
  page: 1,
  page_size: 10,
  server: '',
  source_type: 'document',  // 默认查询多文档
  username: '',
  file_name: '',
  status: '',
  kb_type: undefined,
  parse_type: undefined,
  batch_id: '',
  document_id: '',
  sort_by: '',
  sort_order: 'desc'
})

// 获取服务器列表
const getServers = async () => {
  try {
    const servers = await getServerList()
    serverList.value = servers
    if (servers.length > 0 && !activeTab.value) {
      activeTab.value = servers[0].name
      queryParams.value.server = servers[0].name
    }
  } catch (error) {
    console.error('获取服务器列表失败:', error)
    ElMessage.error('获取服务器列表失败')
  }
}

// 获取文档列表
const getDocumentList = async () => {
  if (!activeTab.value) return

  loading.value = true
  try {
    queryParams.value.server = activeTab.value
    const response = await queryDOCDocuments(queryParams.value)
    documentList.value = response.items || []
    total.value = response.pagination?.total || 0
  } catch (error) {
    console.error('获取DOC文档列表失败:', error)
    ElMessage.error('获取DOC文档列表失败')
  } finally {
    loading.value = false
  }
}

// Tab切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  queryParams.value.page = 1
  queryParams.value.server = tabName
  getDocumentList()
}

// 重试处理
const handleRetry = async (document: DOCDocument) => {
  try {
    await ElMessageBox.confirm(
      '确认要重试处理该文档吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await retryDOCDocument(activeTab.value, document.document_id)
    ElMessage.success('重试任务已提交')
    getDocumentList()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('重试失败: ' + error.message)
    }
  }
}

// 查看详情
const handleViewDetails = (document: DOCDocument) => {
  currentDocument.value = document
  detailsVisible.value = true
}

// 查看日志
const handleViewLogs = (document: DOCDocument) => {
  currentLogDocumentId.value = document.document_id
  logsVisible.value = true
}

// 关闭日志对话框
const handleCloseLog = () => {
  logsVisible.value = false
}

// 数据源类型切换
const handleSourceTypeChange = () => {
  queryParams.value.page = 1  // 重置页码
  getDocumentList()
}

// 下载原始文件
const handleDownload = async (document: DOCDocument) => {
  try {
    await downloadDOCOriginalFile(
      activeTab.value,
      document.document_id,
      document.file_name,
      queryParams.value.source_type || 'document',
      document
    )
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

// 搜索处理
const handleSearch = () => {
  queryParams.value.page = 1
  getDocumentList()
}

// 重置查询
const handleReset = () => {
  const currentSourceType = queryParams.value.source_type
  queryParams.value = {
    page: 1,
    page_size: 10,
    server: activeTab.value,
    source_type: currentSourceType,  // 保留当前数据源类型
    username: '',
    file_name: '',
    status: '',
    kb_type: undefined,
    parse_type: undefined,
    batch_id: '',
    document_id: '',
    sort_by: '',
    sort_order: 'desc'
  }
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  queryParams.value.page_size = val
  getDocumentList()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  queryParams.value.page = val
  getDocumentList()
}

// 排序改变
const handleSortChange = ({ prop, order }: { prop: string, order: string | null }) => {
  queryParams.value.sort_by = order ? prop : ''
  queryParams.value.sort_order = order === 'ascending' ? 'asc' : 'desc'
  getDocumentList()
}

// 监听Tab变化
watch(activeTab, () => {
  if (activeTab.value) {
    getDocumentList()
  }
})

// 初始化
onMounted(() => {
  getServers()
})
</script>

<style scoped>
.doc-document-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow: visible;
}

.page-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.content-container {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.server-tabs {
  flex-shrink: 0;
  margin-bottom: 12px;
}

.tab-content{
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0;
  overflow: visible;
}

.filter-card {
  flex-shrink: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.table-card {
  background: transparent;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  overflow: visible;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #94a3b8;
  gap: 12px;
}

.loading-container .el-icon {
  font-size: 24px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.tab-label .el-icon {
  font-size: 16px;
}

.source-type-switch {
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 14px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
}

.source-switch-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  flex-shrink: 0;
}

.source-radio-group {
  display: flex;
  gap: 4px;
}

.source-radio-group :deep(.el-radio-button) {
  margin: 0;
}

.source-radio-group :deep(.el-radio-button__inner) {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f9fafb;
  color: #6b7280;
}

.source-radio-group :deep(.el-radio-button__inner:hover) {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
  color: #10b981;
}

.source-radio-group :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: #10b981;
  color: #fff;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.source-radio-group :deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 6px;
}

.source-radio-group :deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 6px;
}

:deep(.el-tabs__header) {
  margin: 0;
  background: #fff;
  border-radius: 12px;
  padding: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__item) {
  border: none !important;
  border-radius: 8px !important;
  margin-right: 8px;
  padding: 12px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  background: transparent;
  color: #64748b;
}

:deep(.el-tabs__item:hover) {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

:deep(.el-tabs__item.is-active) {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: #fff !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

:deep(.el-tabs__content){
  flex: 1;
  overflow: visible;     /* ← 原来是 hidden */
  padding: 0;
}
:deep(.el-tab-pane){
  height: auto !important;     /* ← 原来是 100% */
  min-height: 0 !important;
  overflow: visible !important;/* ← 原来是 hidden */
}
</style>
