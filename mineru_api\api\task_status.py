"""
任务状态查询API - 新增的状态查询接口
让 almond_parser 可以主动获取任务状态
"""
from typing import Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Header
from loguru import logger

from ..services.task_manager import task_manager
from ..services.callback_tracker import callback_tracker
from ..models import TaskStatusResponse, TaskStatus
from ..auth import verify_api_key

router = APIRouter(prefix="/tasks", tags=["任务状态"])


@router.get("/{task_id}/status", response_model=TaskStatusResponse)
async def get_task_status(
    task_id: str,
    authorization: str = Header(None)
):
    """
    获取任务状态

    - **task_id**: 任务ID
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)

        task_status = task_manager.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        logger.info(f"查询任务状态: {task_id} -> {task_status.status}")
        return task_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {task_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail="查询任务状态失败")


@router.get("/{task_id}/result")
async def get_task_result(
    task_id: str,
    authorization: str = Header(None)
):
    """
    获取任务结果

    - **task_id**: 任务ID
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)

        # 先检查任务是否存在
        task_status = task_manager.get_task_status(task_id)
        if not task_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 获取任务结果
        task_result = task_manager.get_task_result(task_id)
        
        response = {
            "task_id": task_id,
            "status": task_status.status,
            "message": task_status.message,
            "result": task_result.dict() if task_result else None,
            "error": task_status.error
        }
        
        logger.info(f"查询任务结果: {task_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务结果失败: {task_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail="查询任务结果失败")


@router.get("/{task_id}/callback-status")
async def get_callback_status(
    task_id: str,
    authorization: str = Header(None)
):
    """
    获取回调状态

    - **task_id**: 任务ID
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)

        callback_status = callback_tracker.get_callback_status(task_id)
        
        if not callback_status:
            # 检查任务是否存在
            task_status = task_manager.get_task_status(task_id)
            if not task_status:
                raise HTTPException(status_code=404, detail="任务不存在")
            else:
                return {
                    "task_id": task_id,
                    "status": "no_callback",
                    "message": "该任务没有配置回调"
                }
        
        logger.info(f"查询回调状态: {task_id} -> {callback_status['status']}")
        return callback_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询回调状态失败: {task_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail="查询回调状态失败")


@router.post("/{task_id}/retry-callback")
async def retry_callback(
    task_id: str,
    authorization: str = Header(None)
):
    """
    手动重试回调

    - **task_id**: 任务ID
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)

        # 检查任务是否存在
        task_status = task_manager.get_task_status(task_id)
        if not task_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 检查任务是否已完成
        if task_status.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
            raise HTTPException(status_code=400, detail="只能重试已完成或失败的任务的回调")
        
        # 检查是否有回调配置
        callback_status = callback_tracker.get_callback_status(task_id)
        if not callback_status:
            raise HTTPException(status_code=400, detail="该任务没有配置回调")
        
        # 重置回调状态为待重试
        with callback_tracker._lock:
            if task_id in callback_tracker._records:
                record = callback_tracker._records[task_id]
                record.status = "failed"
                record.attempts = 0  # 重置尝试次数
                record.last_error = None
                callback_tracker._save_records()
        
        logger.info(f"手动重试回调: {task_id}")
        return {
            "task_id": task_id,
            "message": "回调重试已安排",
            "status": "scheduled"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试回调失败: {task_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail="重试回调失败")


@router.get("/stats")
async def get_task_stats(
    authorization: str = Header(None)
):
    """
    获取任务统计信息
    """
    try:
        # 简单的认证检查
        api_key = authorization.replace("Bearer ", "") if authorization else None
        verify_api_key(api_key)

        stats = {
            "total_tasks": len(task_manager.tasks),
            "pending_tasks": 0,
            "processing_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "callback_stats": {
                "pending": 0,
                "success": 0,
                "failed": 0,
                "abandoned": 0
            }
        }
        
        # 统计任务状态
        for task in task_manager.tasks.values():
            if task.status == TaskStatus.PENDING:
                stats["pending_tasks"] += 1
            elif task.status == TaskStatus.PROCESSING:
                stats["processing_tasks"] += 1
            elif task.status == TaskStatus.COMPLETED:
                stats["completed_tasks"] += 1
            elif task.status == TaskStatus.FAILED:
                stats["failed_tasks"] += 1
        
        # 统计回调状态
        with callback_tracker._lock:
            for record in callback_tracker._records.values():
                if record.status == "pending":
                    stats["callback_stats"]["pending"] += 1
                elif record.status == "success":
                    stats["callback_stats"]["success"] += 1
                elif record.status == "failed":
                    stats["callback_stats"]["failed"] += 1
                elif record.status == "abandoned":
                    stats["callback_stats"]["abandoned"] += 1
        
        return stats
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务统计失败")


@router.get("/health")
async def health_check():
    """
    健康检查接口（无需认证）
    """
    try:
        # 检查任务管理器状态
        task_count = len(task_manager.tasks)
        
        # 检查回调跟踪器状态
        callback_count = len(callback_tracker._records)
        
        # 检查重试工作线程状态
        retry_worker_running = callback_tracker._running
        
        return {
            "status": "healthy",
            "timestamp": task_manager.tasks and max(task.created_at for task in task_manager.tasks.values()) or None,
            "task_count": task_count,
            "callback_count": callback_count,
            "retry_worker_running": retry_worker_running
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
