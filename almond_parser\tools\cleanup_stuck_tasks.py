#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
清理卡住的任务
"""
import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.db.models.document import Document, DocumentStatus
from sqlalchemy import select, and_


async def cleanup_stuck_tasks(timeout_minutes: int = 15, dry_run: bool = True):
    """
    清理卡住的任务
    
    Args:
        timeout_minutes: 超时时间（分钟）
        dry_run: 是否只是预览，不实际执行
    """
    print("=" * 60)
    print(f"清理卡住的任务 (超时: {timeout_minutes}分钟)")
    print(f"模式: {'预览模式' if dry_run else '执行模式'}")
    print("=" * 60)
    
    async with get_async_session() as db:
        cutoff_time = datetime.now() - timedelta(minutes=timeout_minutes)
        print(f"截止时间: {cutoff_time}")
        
        # 查找卡住的任务
        stuck_tasks_result = await db.execute(
            select(Document).where(
                and_(
                    Document.status.in_([
                        DocumentStatus.PENDING,
                        DocumentStatus.PARSING,
                        DocumentStatus.RETRY_PENDING
                    ]),
                    Document.task_id.isnot(None),  # 有task_id但可能已经失效
                    Document.updated_at < cutoff_time  # 超过超时时间未更新
                )
            ).order_by(Document.updated_at.asc())
        )
        
        stuck_tasks = stuck_tasks_result.scalars().all()
        
        if not stuck_tasks:
            print("✅ 没有发现卡住的任务")
            return
        
        print(f"🔍 发现 {len(stuck_tasks)} 个卡住的任务:")
        
        for i, task in enumerate(stuck_tasks, 1):
            print(f"\n{i}. 文档ID: {task.document_id}")
            print(f"   文件名: {task.file_name}")
            print(f"   状态: {task.status.value}")
            print(f"   任务ID: {task.task_id}")
            print(f"   重试次数: {task.retry_count}/{task.max_retries}")
            print(f"   更新时间: {task.updated_at}")
            print(f"   卡住时长: {datetime.now() - task.updated_at}")
            
            if not dry_run:
                # 清理任务：清除task_id，状态改为RETRY_PENDING
                print(f"   🔧 清理任务...")
                task.task_id = None
                task.status = DocumentStatus.RETRY_PENDING
                task.updated_at = datetime.now()
                
                # 如果重试次数已达上限，标记为失败
                if task.retry_count >= task.max_retries:
                    task.status = DocumentStatus.FAILED
                    task.error_message = f"任务卡住超时，已清理 (超时{timeout_minutes}分钟)"
                    task.completed_at = datetime.now()
                    print(f"   ❌ 重试次数已达上限，标记为失败")
                else:
                    print(f"   ♻️  重置为RETRY_PENDING状态")
        
        if not dry_run:
            await db.commit()
            print(f"\n✅ 已清理 {len(stuck_tasks)} 个卡住的任务")
        else:
            print(f"\n📋 预览完成，发现 {len(stuck_tasks)} 个需要清理的任务")
            print("💡 使用 --execute 参数执行实际清理")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="清理卡住的任务")
    parser.add_argument("--timeout", type=int, default=15, help="超时时间（分钟），默认15分钟")
    parser.add_argument("--execute", action="store_true", help="执行实际清理（默认只预览）")
    
    args = parser.parse_args()
    
    await cleanup_stuck_tasks(
        timeout_minutes=args.timeout,
        dry_run=not args.execute
    )


if __name__ == "__main__":
    asyncio.run(main())
