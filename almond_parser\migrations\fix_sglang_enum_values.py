#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
修复SGLang枚举值：将小写转换为大写
"""
import asyncio
import sys
from pathlib import Path
from sqlalchemy import text
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.db.database import get_async_session


async def fix_sglang_enum_values():
    """修复SGLang枚举值"""
    
    try:
        async with get_async_session() as db:
            logger.info("开始修复SGLang枚举值...")
            
            # 检查是否存在sglang_status字段
            result = await db.execute(text("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'mineru_nodes' 
                AND COLUMN_NAME = 'sglang_status'
            """))
            
            if result.scalar() == 0:
                logger.warning("sglang_status字段不存在，请先运行add_sglang_monitoring.py")
                return
            
            # 方案1：如果字段已存在但枚举值不对，先删除再重新添加
            try:
                # 删除现有字段
                await db.execute(text("ALTER TABLE mineru_nodes DROP COLUMN sglang_status"))
                await db.commit()
                logger.info("删除现有sglang_status字段")
                
                # 重新添加字段
                await db.execute(text("""
                    ALTER TABLE mineru_nodes 
                    ADD COLUMN sglang_status ENUM('ONLINE', 'OFFLINE', 'ERROR', 'UNKNOWN', 'RESTARTING') 
                    DEFAULT 'UNKNOWN' 
                    COMMENT 'SGLang服务状态'
                """))
                await db.commit()
                logger.success("重新添加sglang_status字段，使用正确的枚举值")
                
            except Exception as e:
                logger.error(f"修复枚举值失败: {e}")
                
                # 方案2：如果删除失败，尝试直接更新数据
                logger.info("尝试直接更新现有数据...")
                
                # 更新小写值为大写值
                update_mappings = [
                    ("'online'", "'ONLINE'"),
                    ("'offline'", "'OFFLINE'"),
                    ("'error'", "'ERROR'"),
                    ("'unknown'", "'UNKNOWN'"),
                    ("'restarting'", "'RESTARTING'")
                ]
                
                for old_val, new_val in update_mappings:
                    try:
                        result = await db.execute(text(f"""
                            UPDATE mineru_nodes 
                            SET sglang_status = {new_val}
                            WHERE sglang_status = {old_val}
                        """))
                        affected_rows = result.rowcount
                        if affected_rows > 0:
                            logger.info(f"更新 {old_val} -> {new_val}: {affected_rows} 行")
                    except Exception as update_error:
                        logger.warning(f"更新 {old_val} -> {new_val} 失败: {update_error}")
                
                await db.commit()
            
            logger.success("SGLang枚举值修复完成")
            
    except Exception as e:
        logger.error(f"修复SGLang枚举值失败: {e}")
        raise


async def verify_fix():
    """验证修复结果"""
    try:
        async with get_async_session() as db:
            logger.info("验证修复结果...")
            
            # 检查枚举值
            result = await db.execute(text("""
                SELECT COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'mineru_nodes' 
                AND COLUMN_NAME = 'sglang_status'
            """))
            
            column_type = result.scalar()
            logger.info(f"sglang_status字段类型: {column_type}")
            
            # 检查数据分布
            result = await db.execute(text("""
                SELECT sglang_status, COUNT(*) as count
                FROM mineru_nodes 
                GROUP BY sglang_status
            """))
            
            status_counts = result.fetchall()
            logger.info("SGLang状态分布:")
            for status, count in status_counts:
                logger.info(f"  {status}: {count} 个节点")
            
            return True
            
    except Exception as e:
        logger.error(f"验证修复结果失败: {e}")
        return False


async def main():
    """主函数"""
    try:
        logger.info("🔧 开始修复SGLang枚举值")
        
        # 1. 修复枚举值
        await fix_sglang_enum_values()
        
        # 2. 验证结果
        if await verify_fix():
            logger.success("🎉 SGLang枚举值修复完成")
        else:
            logger.error("❌ 修复验证失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"修复失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
