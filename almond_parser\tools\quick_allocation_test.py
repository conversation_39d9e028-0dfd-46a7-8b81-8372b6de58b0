#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
快速测试任务分配功能
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from almond_parser.services.task_allocation_service import TaskAllocationService


async def test_allocation():
    """快速测试任务分配"""
    print("🚀 快速测试任务分配...")
    
    try:
        task_service = TaskAllocationService()
        
        # 执行按节点类型分配
        print("📊 执行按节点类型分配...")
        stats = await task_service.allocate_pending_tasks_by_node_type(max_allocations=10)
        
        print("📋 分配结果:")
        print(f"  成功分配: {stats.get('allocated', 0)}")
        print(f"  分配失败: {stats.get('failed', 0)}")
        print(f"  等待任务: {stats.get('total_pending', 0)}")
        print(f"  分配方法: {stats.get('allocation_method', 'unknown')}")
        
        if 'error' in stats:
            print(f"  ❌ 错误: {stats['error']}")
            return False
        
        # 显示详细信息
        details = stats.get('node_type_details', {})
        if details:
            print("\n📋 节点类型分配详情:")
            for node_type, detail in details.items():
                print(f"  {node_type}:")
                print(f"    分配成功: {detail.get('allocated', 0)}")
                print(f"    分配失败: {detail.get('failed', 0)}")
                print(f"    可用容量: {detail.get('available_capacity', 0)}")
                print(f"    适合任务: {detail.get('suitable_tasks', 0)}")
        
        if stats.get('allocated', 0) > 0:
            print("\n✅ 任务分配成功！")
            return True
        elif stats.get('total_pending', 0) == 0:
            print("\n✅ 没有待分配的任务")
            return True
        else:
            print("\n❌ 有待分配任务但分配失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def test_old_allocation():
    """测试旧的分配方法作为对比"""
    print("\n🔄 测试旧的分配方法...")
    
    try:
        task_service = TaskAllocationService()
        
        # 执行旧的分配方法
        stats = await task_service.allocate_pending_tasks(max_allocations=10)
        
        print("📋 旧方法分配结果:")
        print(f"  成功分配: {stats.get('allocated', 0)}")
        print(f"  分配失败: {stats.get('failed', 0)}")
        print(f"  等待任务: {stats.get('total_pending', 0)}")
        
        if 'error' in stats:
            print(f"  ❌ 错误: {stats['error']}")
            return False
        
        return stats.get('allocated', 0) > 0 or stats.get('total_pending', 0) == 0
        
    except Exception as e:
        print(f"❌ 旧方法测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🧪 快速任务分配测试\n")
    
    # 测试新方法
    new_result = await test_allocation()
    
    # 测试旧方法作为对比
    old_result = await test_old_allocation()
    
    print("\n📊 测试总结:")
    print(f"  新方法 (按节点类型): {'✅ 成功' if new_result else '❌ 失败'}")
    print(f"  旧方法 (总容量): {'✅ 成功' if old_result else '❌ 失败'}")
    
    if new_result:
        print("\n🎉 新的按节点类型分配方法工作正常！")
    elif old_result:
        print("\n⚠️ 新方法有问题，但旧方法正常。建议临时回滚。")
    else:
        print("\n❌ 两种方法都有问题，需要检查系统配置。")


if __name__ == "__main__":
    asyncio.run(main())
