# -*- encoding: utf-8 -*-
"""
节点并发控制管理器 - 确保原子性操作和超时管理
"""
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger
from sqlalchemy.dialects import mysql

from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus, ServiceType
from almond_parser.db.models.document import Document, DocumentStatus


class NodeConcurrencyManager:
    """节点并发控制管理器"""

    def __init__(self):
        self.task_timeout_minutes = 30  # 任务超时时间：30分钟
        self.reservation_timeout_minutes = 5  # 预留超时时间：5分钟

    def _map_api_mode_to_db_enum(self, api_mode: str) -> str:
        """
        将API层面的解析模式映射回数据库枚举值

        Args:
            api_mode: API层面的解析模式 (如 'vlm-sglang-client', 'vlm', 'sglang' 等)

        Returns:
            数据库枚举对应的模式值
        """
        if not api_mode:
            return "auto"

        api_mode = api_mode.lower().strip()

        # API模式到数据库枚举的映射
        mode_mapping = {
            "vlm-sglang-client": "sglang",
            "vlm": "sglang",
            "sglang": "sglang",
            "pipeline": "pipeline",
            "auto": "auto"
        }

        mapped_mode = mode_mapping.get(api_mode, "auto")
        logger.debug(f"模式映射: {api_mode} -> {mapped_mode}")

        return mapped_mode

    async def allocate_node_with_lock(
        self,
        db: AsyncSession,
        service_type: ServiceType,
        parse_mode: Optional[str] = None,
        document_id: Optional[str] = None
    ) -> Optional[MinerUNode]:
        """
        原子性分配节点并预留任务槽位

        Args:
            db: 数据库会话
            service_type: 服务类型
            parse_mode: 解析模式
            document_id: 文档ID（用于日志）

        Returns:
            分配的节点或None
        """
        try:
            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行操作
                # 1. 查找可用节点（使用行锁防止并发）
                node = await self._find_available_node_with_lock(
                    db, service_type, parse_mode
                )

                if not node:
                    logger.warning(f"没有可用节点处理 {service_type.value} 类型任务")
                    return None

                # 2. 原子性预留任务槽位
                node.reserved_tasks += 1
                node.last_task_assigned_at = datetime.now()
                await db.flush()  # 确保更新立即生效

                logger.info(
                    f"✅ 节点分配成功: {node.name} "
                    f"(当前: {node.current_tasks}, 预留: {node.reserved_tasks}, "
                    f"最大: {node.max_concurrent_tasks})"
                )

                return node
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    # 1. 查找可用节点（使用行锁防止并发）
                    node = await self._find_available_node_with_lock(
                        db, service_type, parse_mode
                    )

                    if not node:
                        logger.warning(f"没有可用节点处理 {service_type.value} 类型任务")
                        return None

                    # 2. 原子性预留任务槽位
                    node.reserved_tasks += 1
                    node.last_task_assigned_at = datetime.now()
                    await db.flush()  # 确保更新立即生效

                    logger.info(
                        f"✅ 节点分配成功: {node.name} "
                        f"(当前: {node.current_tasks}, 预留: {node.reserved_tasks}, "
                        f"最大: {node.max_concurrent_tasks})"
                    )

                    return node

        except Exception as e:
            logger.error(f"节点分配失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return None

    async def _find_available_node_with_lock(
        self,
        db: AsyncSession,
        service_type: ServiceType,
        parse_mode: Optional[str] = None
    ) -> Optional[MinerUNode]:
        """查找可用节点并加锁"""

        # 构建查询条件
        conditions = [
            MinerUNode.is_enabled == True,
            MinerUNode.status == NodeStatus.ONLINE,
            # 关键：检查总任务数（当前+预留）不超过最大并发数
            (MinerUNode.current_tasks + MinerUNode.reserved_tasks) < MinerUNode.max_concurrent_tasks
        ]

        # 添加服务类型条件
        if service_type == ServiceType.UNIVERSAL:
            # 如果请求通用类型，可以使用任何类型的节点
            # 不添加服务类型限制，让所有节点都可以被选择
            pass
        else:
            # 否则查找专用节点或通用节点
            conditions.append(
                or_(
                    MinerUNode.service_type == service_type,
                    MinerUNode.service_type == ServiceType.UNIVERSAL
                )
            )

        # 添加解析模式条件（如果指定）
        if parse_mode:
            from almond_parser.db.models.mineru_node import ParseMode
            # 将API模式映射为数据库枚举值
            db_mode = self._map_api_mode_to_db_enum(parse_mode)
            parse_mode_enum = getattr(ParseMode, db_mode.upper(), None)
            if parse_mode_enum:
                conditions.append(MinerUNode.parse_mode == parse_mode_enum)

        # 执行查询（使用行锁）
        stmt = (
            select(MinerUNode)
            .where(and_(*conditions))
            .with_for_update()
            .order_by(
                (MinerUNode.current_tasks + MinerUNode.reserved_tasks).asc(),
                MinerUNode.priority.desc()
            )
            .limit(1)
        )

        # 打印 SQL 和参数（适用于调试）
        compiled = stmt.compile(dialect=mysql.dialect(), compile_kwargs={"literal_binds": True})
        print("最终SQL语句:\n", compiled)

        # 执行
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def confirm_task_start(
        self,
        db: AsyncSession,
        node_id: int,
        task_id: str,
        document_id: str
    ) -> bool:
        """
        确认任务开始，将预留转为实际任务

        Args:
            db: 数据库会话
            node_id: 节点ID
            task_id: 任务ID
            document_id: 文档ID

        Returns:
            是否成功
        """
        try:
            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行操作
                # 获取节点并加锁
                result = await db.execute(
                    select(MinerUNode)
                    .where(MinerUNode.id == node_id)
                    .with_for_update()
                )
                node = result.scalar_one_or_none()

                if not node:
                    logger.error(f"节点不存在: {node_id}")
                    return False

                # 预留转为实际任务
                if node.reserved_tasks > 0:
                    node.reserved_tasks -= 1
                    node.current_tasks += 1
                    node.total_tasks += 1
                    await db.flush()

                    logger.info(
                        f"✅ 任务开始确认: {node.name} - {task_id} "
                        f"(当前: {node.current_tasks}, 预留: {node.reserved_tasks})"
                    )
                    return True
                else:
                    logger.warning(f"节点 {node.name} 没有预留任务可确认")
                    return False
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    # 获取节点并加锁
                    result = await db.execute(
                        select(MinerUNode)
                        .where(MinerUNode.id == node_id)
                        .with_for_update()
                    )
                    node = result.scalar_one_or_none()

                    if not node:
                        logger.error(f"节点不存在: {node_id}")
                        return False

                    # 预留转为实际任务
                    if node.reserved_tasks > 0:
                        node.reserved_tasks -= 1
                        node.current_tasks += 1
                        node.total_tasks += 1
                        await db.flush()

                        logger.info(
                            f"✅ 任务开始确认: {node.name} - {task_id} "
                            f"(当前: {node.current_tasks}, 预留: {node.reserved_tasks})"
                        )
                        return True
                    else:
                        logger.warning(f"节点 {node.name} 没有预留任务可确认")
                        return False

        except Exception as e:
            logger.error(f"确认任务开始失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return False

    async def release_task_slot(
        self,
        db: AsyncSession,
        node_id: int,
        task_id: str,
        success: bool = True,
        reason: str = "任务完成"
    ) -> bool:
        """
        释放任务槽位

        Args:
            db: 数据库会话
            node_id: 节点ID
            task_id: 任务ID
            success: 是否成功
            reason: 释放原因

        Returns:
            是否成功
        """
        try:
            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行操作
                # 获取节点并加锁
                result = await db.execute(
                    select(MinerUNode)
                    .where(MinerUNode.id == node_id)
                    .with_for_update()
                )
                node = result.scalar_one_or_none()

                if not node:
                    logger.error(f"节点不存在: {node_id}")
                    return False

                # 释放任务槽位
                if node.current_tasks > 0:
                    node.current_tasks -= 1
                    if success:
                        node.success_tasks += 1
                    else:
                        node.failed_tasks += 1

                    await db.flush()

                    logger.info(
                        f"✅ 任务槽位释放: {node.name} - {task_id} "
                        f"(原因: {reason}, 当前: {node.current_tasks})"
                    )
                    return True
                else:
                    logger.warning(f"节点 {node.name} 当前任务数为0，无需释放")
                    return False
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    # 获取节点并加锁
                    result = await db.execute(
                        select(MinerUNode)
                        .where(MinerUNode.id == node_id)
                        .with_for_update()
                    )
                    node = result.scalar_one_or_none()

                    if not node:
                        logger.error(f"节点不存在: {node_id}")
                        return False

                    # 释放任务槽位
                    if node.current_tasks > 0:
                        node.current_tasks -= 1
                        if success:
                            node.success_tasks += 1
                        else:
                            node.failed_tasks += 1

                        await db.flush()

                        logger.info(
                            f"✅ 任务槽位释放: {node.name} - {task_id} "
                            f"(原因: {reason}, 当前: {node.current_tasks})"
                        )
                        return True
                    else:
                        logger.warning(f"节点 {node.name} 当前任务数为0，无需释放")
                        return False

        except Exception as e:
            logger.error(f"释放任务槽位失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return False

    async def release_reserved_slot(
        self,
        db: AsyncSession,
        node_id: int,
        reason: str = "预留槽位释放"
    ) -> bool:
        """
        释放预留的任务槽位（用于任务分配成功但API调用失败的情况）

        Args:
            db: 数据库会话
            node_id: 节点ID
            reason: 释放原因

        Returns:
            是否成功
        """
        try:
            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行操作
                # 获取节点并加锁
                result = await db.execute(
                    select(MinerUNode)
                    .where(MinerUNode.id == node_id)
                    .with_for_update()
                )
                node = result.scalar_one_or_none()

                if not node:
                    logger.error(f"节点不存在: {node_id}")
                    return False

                # 释放预留槽位
                if node.reserved_tasks > 0:
                    node.reserved_tasks -= 1
                    await db.flush()

                    logger.info(
                        f"✅ 预留槽位释放: {node.name} "
                        f"(原因: {reason}, 预留: {node.reserved_tasks})"
                    )
                    return True
                else:
                    logger.warning(f"节点 {node.name} 没有预留槽位可释放")
                    return False
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    # 获取节点并加锁
                    result = await db.execute(
                        select(MinerUNode)
                        .where(MinerUNode.id == node_id)
                        .with_for_update()
                    )
                    node = result.scalar_one_or_none()

                    if not node:
                        logger.error(f"节点不存在: {node_id}")
                        return False

                    # 释放预留槽位
                    if node.reserved_tasks > 0:
                        node.reserved_tasks -= 1
                        await db.flush()

                        logger.info(
                            f"✅ 预留槽位释放: {node.name} "
                            f"(原因: {reason}, 预留: {node.reserved_tasks})"
                        )
                        return True
                    else:
                        logger.warning(f"节点 {node.name} 没有预留槽位可释放")
                        return False

        except Exception as e:
            logger.error(f"释放预留槽位失败: {e}")
            if not db.in_transaction():
                await db.rollback()
            return False

    async def cleanup_expired_reservations(self, db: AsyncSession) -> Dict[str, Any]:
        """
        清理过期的预留任务

        Returns:
            清理统计信息
        """
        cleanup_stats = {
            "cleaned_nodes": 0,
            "released_reservations": 0,
            "errors": []
        }

        try:
            cutoff_time = datetime.now() - timedelta(minutes=self.reservation_timeout_minutes)

            # 检查是否已经在事务中
            if db.in_transaction():
                # 如果已经在事务中，直接执行操作
                # 查找有过期预留的节点
                result = await db.execute(
                    select(MinerUNode)
                    .where(
                        and_(
                            MinerUNode.reserved_tasks > 0,
                            MinerUNode.last_task_assigned_at < cutoff_time
                        )
                    )
                    .with_for_update()
                )

                nodes = result.scalars().all()

                for node in nodes:
                    released_count = node.reserved_tasks
                    node.reserved_tasks = 0

                    cleanup_stats["cleaned_nodes"] += 1
                    cleanup_stats["released_reservations"] += released_count

                    logger.warning(
                        f"🧹 清理过期预留: {node.name} "
                        f"(释放 {released_count} 个预留槽位)"
                    )

                await db.flush()
            else:
                # 如果不在事务中，创建新事务
                async with db.begin():
                    # 查找有过期预留的节点
                    result = await db.execute(
                        select(MinerUNode)
                        .where(
                            and_(
                                MinerUNode.reserved_tasks > 0,
                                MinerUNode.last_task_assigned_at < cutoff_time
                            )
                        )
                        .with_for_update()
                    )

                    nodes = result.scalars().all()

                    for node in nodes:
                        released_count = node.reserved_tasks
                        node.reserved_tasks = 0

                        cleanup_stats["cleaned_nodes"] += 1
                        cleanup_stats["released_reservations"] += released_count

                        logger.warning(
                            f"🧹 清理过期预留: {node.name} "
                            f"(释放 {released_count} 个预留槽位)"
                        )

                    await db.flush()

        except Exception as e:
            error_msg = f"清理过期预留失败: {e}"
            logger.error(error_msg)
            cleanup_stats["errors"].append(error_msg)
            if not db.in_transaction():
                await db.rollback()

        return cleanup_stats

    async def cleanup_timeout_tasks(self, db: AsyncSession) -> Dict[str, Any]:
        """
        清理超时任务（30分钟）

        Returns:
            清理统计信息
        """
        cleanup_stats = {
            "timeout_tasks": 0,
            "released_slots": 0,
            "errors": []
        }

        try:
            cutoff_time = datetime.now() - timedelta(minutes=self.task_timeout_minutes)

            # 查找超时的解析任务
            result = await db.execute(
                select(Document)
                .where(
                    and_(
                        Document.status == DocumentStatus.PARSING,
                        Document.started_at < cutoff_time,
                        Document.node_id.isnot(None)
                    )
                )
            )

            timeout_documents = result.scalars().all()

            for document in timeout_documents:
                try:
                    # 释放节点槽位
                    released = await self.release_task_slot(
                        db, document.node_id, document.task_id or "unknown",
                        success=False, reason="任务超时"
                    )

                    if released:
                        cleanup_stats["released_slots"] += 1

                    # 更新文档状态 - 超时任务直接失败，不重试
                    document.status = DocumentStatus.FAILED
                    document.error_message = f"任务超时失败（超过{self.task_timeout_minutes}分钟）"
                    document.completed_at = datetime.now()

                    cleanup_stats["timeout_tasks"] += 1

                    logger.warning(
                        f"⏰ 任务超时清理: {document.document_id} "
                        f"(节点: {document.node_id}, 开始时间: {document.started_at})"
                    )

                except Exception as e:
                    error_msg = f"清理超时任务失败 {document.document_id}: {e}"
                    logger.error(error_msg)
                    cleanup_stats["errors"].append(error_msg)

            await db.commit()

        except Exception as e:
            error_msg = f"清理超时任务失败: {e}"
            logger.error(error_msg)
            cleanup_stats["errors"].append(error_msg)
            await db.rollback()

        return cleanup_stats


# 全局管理器实例
node_concurrency_manager = NodeConcurrencyManager()
