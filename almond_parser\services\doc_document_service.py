# -*- encoding: utf-8 -*-
"""
DOC文档服务
"""
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, desc, asc, or_
from loguru import logger

from almond_parser.db.doc_database_manager import doc_db_manager
from almond_parser.db.models.doc_document import (
    ParseTask,
    DocReaderTask,
    create_doc_document_from_parse_task,
    create_doc_document_from_doc_reader_task
)
from almond_parser.schemas.doc_document import (
    DOCDocumentQueryParams,
    DOCDocumentResponse,
    ServerConfigResponse
)


class DOCDocumentService:
    """DOC文档服务"""

    @staticmethod
    async def get_server_list() -> List[ServerConfigResponse]:
        """获取服务器列表"""
        try:
            # 确保数据库管理器已初始化
            if not doc_db_manager._initialized:
                await doc_db_manager.initialize()

            server_configs = doc_db_manager.get_server_list()
            return [ServerConfigResponse(**config) for config in server_configs]

        except Exception as e:
            logger.error(f"获取服务器列表失败: {e}")
            return []

    @staticmethod
    async def query_documents(
        params: DOCDocumentQueryParams
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """查询DOC文档列表"""
        if not params.server:
            return [], {"page": 1, "page_size": 10, "total": 0, "pages": 0}

        try:
            # 获取数据库会话
            session = await doc_db_manager.get_session(params.server)

            async with session:
                # 根据数据源类型查询不同的表
                # 默认查询多文档表(doc_reader_tasks)，参数切换知识库表(parse_tasks)
                if params.source_type == "knowledge":
                    # 查询知识库表(parse_tasks)
                    documents, total = await DOCDocumentService._query_parse_tasks(
                        session, params
                    )
                else:
                    # 默认查询多文档表(doc_reader_tasks)
                    documents, total = await DOCDocumentService._query_doc_reader_tasks(
                        session, params
                    )

                # 分页信息
                pagination = {
                    "page": params.page,
                    "page_size": params.page_size,
                    "total": total,
                    "pages": (total + params.page_size - 1) // params.page_size
                }

                return documents, pagination

        except Exception as e:
            logger.error(f"查询DOC文档列表失败: {e}")
            return [], {"page": 1, "page_size": 10, "total": 0, "pages": 0}

    @staticmethod
    async def _query_parse_tasks(
        session: AsyncSession,
        params: DOCDocumentQueryParams
    ) -> Tuple[List[Dict[str, Any]], int]:
        """查询parse_tasks表"""
        try:
            # 构建查询条件
            conditions = []

            # 过滤掉task_type为ocr的记录
            conditions.append(or_(
                ParseTask.task_type != "ocr",
                ParseTask.task_type.is_(None)
            ))

            if params.username:
                conditions.append(ParseTask.username.like(f"%{params.username}%"))
            if params.file_name:
                conditions.append(ParseTask.file_name.like(f"%{params.file_name}%"))
            if params.status:
                conditions.append(ParseTask.status.like(f"%{params.status}%"))
            if params.kb_type:
                conditions.append(ParseTask.kb_type == params.kb_type.value)
            if params.batch_id:
                conditions.append(ParseTask.batch_id.like(f"%{params.batch_id}%"))
            if params.document_id:
                conditions.append(ParseTask.doc_id.like(f"%{params.document_id}%"))

            # 查询总数
            count_query = select(func.count(ParseTask.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))

            count_result = await session.execute(count_query)
            total = count_result.scalar() or 0

            # 查询数据
            query = select(ParseTask)
            if conditions:
                query = query.where(and_(*conditions))

            # 排序
            if params.sort_by and hasattr(ParseTask, params.sort_by):
                sort_column = getattr(ParseTask, params.sort_by)
                if params.sort_order == "desc":
                    query = query.order_by(desc(sort_column))
                else:
                    query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(ParseTask.created_at))

            # 分页
            query = query.offset((params.page - 1) * params.page_size).limit(params.page_size)

            result = await session.execute(query)
            tasks = result.scalars().all()

            # 转换为统一格式
            documents = [create_doc_document_from_parse_task(task) for task in tasks]

            return documents, total

        except Exception as e:
            logger.error(f"查询parse_tasks表失败: {e}")
            return [], 0

    @staticmethod
    async def _query_doc_reader_tasks(
        session: AsyncSession,
        params: DOCDocumentQueryParams
    ) -> Tuple[List[Dict[str, Any]], int]:
        """查询doc_reader_tasks表"""
        try:
            # 构建查询条件
            conditions = [DocReaderTask.parse_type != "OCR"]

            # 过滤掉parse_type为OCR的记录

            if params.username:
                conditions.append(DocReaderTask.username.like(f"%{params.username}%"))
            if params.file_name:
                conditions.append(DocReaderTask.filename.like(f"%{params.file_name}%"))
            if params.status:
                conditions.append(DocReaderTask.status.like(f"%{params.status.lower()}%"))
            if params.parse_type:
                conditions.append(DocReaderTask.parse_type == params.parse_type.value)
            if params.batch_id:
                conditions.append(DocReaderTask.batch_id.like(f"%{params.batch_id}%"))
            if params.document_id:
                conditions.append(DocReaderTask.doc_id.like(f"%{params.document_id}%"))

            # 查询总数
            count_query = select(func.count(DocReaderTask.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))

            count_result = await session.execute(count_query)
            total = count_result.scalar() or 0

            # 查询数据
            query = select(DocReaderTask)
            if conditions:
                query = query.where(and_(*conditions))

            # 排序
            if params.sort_by and hasattr(DocReaderTask, params.sort_by):
                sort_column = getattr(DocReaderTask, params.sort_by)
                if params.sort_order == "desc":
                    query = query.order_by(desc(sort_column))
                else:
                    query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(DocReaderTask.created_at))

            # 分页
            query = query.offset((params.page - 1) * params.page_size).limit(params.page_size)

            result = await session.execute(query)
            tasks = result.scalars().all()

            # 转换为统一格式
            documents = [create_doc_document_from_doc_reader_task(task) for task in tasks]

            return documents, total

        except Exception as e:
            logger.error(f"查询doc_reader_tasks表失败: {e}")
            return [], 0

    @staticmethod
    async def get_document_detail(
        server: str,
        document_id: str
    ) -> Optional[Dict[str, Any]]:
        """获取DOC文档详情"""
        try:
            session = await doc_db_manager.get_session(server)

            async with session:
                # 先在parse_tasks表中查找
                parse_task_query = select(ParseTask).where(
                    or_(
                        ParseTask.doc_id == document_id,
                        ParseTask.id == int(document_id) if document_id.isdigit() else False
                    )
                )

                result = await session.execute(parse_task_query)
                parse_task = result.scalar_one_or_none()

                if parse_task:
                    return create_doc_document_from_parse_task(parse_task)

                # 在doc_reader_tasks表中查找
                doc_reader_task_query = select(DocReaderTask).where(
                    or_(
                        DocReaderTask.doc_id == document_id,
                        DocReaderTask.task_id == document_id,
                        DocReaderTask.id == int(document_id) if document_id.isdigit() else False
                    )
                )

                result = await session.execute(doc_reader_task_query)
                doc_reader_task = result.scalar_one_or_none()

                if doc_reader_task:
                    return create_doc_document_from_doc_reader_task(doc_reader_task)

                return None

        except Exception as e:
            logger.error(f"获取DOC文档详情失败: {e}")
            return None

    @staticmethod
    async def get_document_logs(
        server: str,
        document_id: str
    ) -> List[str]:
        """获取DOC文档日志"""
        try:
            # 这里可以根据实际情况实现日志获取逻辑
            # 目前返回模拟数据
            return [
                f"[{server}] 文档 {document_id} 开始处理",
                f"[{server}] 正在解析文档内容...",
                f"[{server}] 文档处理完成"
            ]

        except Exception as e:
            logger.error(f"获取DOC文档日志失败: {e}")
            return []

    @staticmethod
    async def retry_document(
        server: str,
        document_id: str
    ) -> bool:
        """重试DOC文档处理"""
        try:
            # 这里可以根据实际情况实现重试逻辑
            # 目前返回成功状态
            logger.info(f"重试DOC文档处理: server={server}, document_id={document_id}")
            return True

        except Exception as e:
            logger.error(f"重试DOC文档处理失败: {e}")
            return False
