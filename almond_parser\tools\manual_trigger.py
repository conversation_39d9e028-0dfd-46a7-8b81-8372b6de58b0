#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
手动触发任务分配测试
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))


async def manual_trigger():
    """手动触发任务分配"""
    try:
        print("🚀 手动触发任务分配...")
        
        # 导入必要的模块
        from almond_parser.tasks.arq_app import allocate_pending_tasks_cron
        
        # 模拟ARQ上下文
        ctx = {"job_id": "manual_test", "job_try": 1}
        
        # 执行任务分配
        result = await allocate_pending_tasks_cron(ctx)
        
        print(f"✅ 任务分配完成，结果: {result}")
        
    except Exception as e:
        print(f"❌ 手动触发失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(manual_trigger())
