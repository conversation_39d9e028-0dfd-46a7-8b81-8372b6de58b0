# SGLang重启逻辑优化

## 🎯 优化目标

解决SGLang服务无限重启的问题，实现智能重启策略：
- **最多重启2次**
- **重启间隔3-5分钟**
- **2次重启失败后发送企微告警**
- **避免无效的重复重启**

## 📊 问题分析

### 原有问题
1. **无限重启**：连续失败次数达到3次就重启，没有重启次数限制
2. **频繁重启**：10分钟内可能多次重启，浪费资源
3. **告警延迟**：需要多次失败才发送告警，延误处理时机

### 优化策略
```
失败检测 → 第1次重启 → 等待4分钟 → 仍失败 → 第2次重启 → 等待4分钟 → 仍失败 → 发送告警
```

## 🔧 技术实现

### 1. 数据库字段扩展

新增字段来跟踪重启周期：

```sql
-- 当前重启周期的重启次数
ALTER TABLE mineru_nodes ADD COLUMN sglang_current_cycle_restarts INT DEFAULT 0;

-- 重启周期开始时间
ALTER TABLE mineru_nodes ADD COLUMN sglang_restart_cycle_start DATETIME;

-- 是否已发送告警
ALTER TABLE mineru_nodes ADD COLUMN sglang_alert_sent BOOLEAN DEFAULT FALSE;

-- 告警发送时间
ALTER TABLE mineru_nodes ADD COLUMN sglang_alert_sent_at DATETIME;
```

### 2. 重启逻辑优化

#### **重启条件检查**
```python
async def check_restart_needed(self, node: MinerUNode) -> bool:
    # 检查条件：
    # 1. 节点启用
    # 2. SGLang连续失败次数 >= 3
    # 3. SGLang状态为OFFLINE或ERROR
    # 4. 当前重启周期内重启次数 < 2
    # 5. 如果已重启过，需要等待4分钟再次检测
    
    if not node.is_enabled:
        return False
    
    if node.sglang_consecutive_failures < 3:
        return False
    
    if node.sglang_status not in [SglangStatus.OFFLINE, SglangStatus.ERROR]:
        return False
    
    # 检查当前重启周期的重启次数
    if node.sglang_current_cycle_restarts >= 2:
        return False  # 已经重启2次了，不再重启
    
    # 如果已经重启过，检查是否需要等待
    if node.sglang_last_restart:
        time_since_restart = datetime.now() - node.sglang_last_restart
        if time_since_restart < timedelta(minutes=4):
            return False  # 还需要等待
    
    return True
```

#### **重启执行逻辑**
```python
async def restart_node_sglang(self, node: MinerUNode):
    # 初始化重启周期（如果是第一次重启）
    now = datetime.now()
    if node.sglang_current_cycle_restarts == 0:
        node.sglang_restart_cycle_start = now
    
    # 更新重启信息
    node.sglang_last_restart = now
    node.sglang_restart_count += 1
    node.sglang_current_cycle_restarts += 1
    node.sglang_status = SglangStatus.RESTARTING
    
    # 执行重启...
```

### 3. 告警逻辑优化

#### **告警条件检查**
```python
async def check_alert_needed(self, node: MinerUNode) -> bool:
    # 检查条件：
    # 1. 节点启用
    # 2. SGLang连续失败次数 >= 3
    # 3. SGLang状态为OFFLINE或ERROR
    # 4. 当前重启周期内已重启2次
    # 5. 最后一次重启后等待了4分钟
    # 6. 还没有发送过告警
    
    if not node.is_enabled:
        return False
    
    if node.sglang_consecutive_failures < 3:
        return False
    
    if node.sglang_status not in [SglangStatus.OFFLINE, SglangStatus.ERROR]:
        return False
    
    if node.sglang_current_cycle_restarts < 2:
        return False  # 还没重启够2次
    
    if node.sglang_alert_sent:
        return False  # 已经发送过告警了
    
    # 检查最后一次重启后是否等待了足够时间
    if node.sglang_last_restart:
        time_since_restart = datetime.now() - node.sglang_last_restart
        if time_since_restart < timedelta(minutes=4):
            return False
    
    return True
```

### 4. 恢复逻辑

当SGLang恢复正常时，重置重启周期：

```python
async def reset_restart_cycle(self, node: MinerUNode):
    if node.sglang_current_cycle_restarts > 0 or node.sglang_alert_sent:
        node.sglang_current_cycle_restarts = 0
        node.sglang_restart_cycle_start = None
        node.sglang_alert_sent = False
        node.sglang_alert_sent_at = None
```

## 📋 重启流程图

```mermaid
graph TD
    A[SGLang失败检测] --> B{连续失败>=3次?}
    B -->|否| A
    B -->|是| C{当前周期重启次数<2?}
    C -->|否| D{已发送告警?}
    C -->|是| E{距离上次重启>=4分钟?}
    E -->|否| A
    E -->|是| F[执行重启]
    F --> G[重启次数+1]
    G --> H[等待4分钟]
    H --> I{SGLang恢复?}
    I -->|是| J[重置重启周期]
    I -->|否| C
    D -->|是| A
    D -->|否| K{距离上次重启>=4分钟?}
    K -->|否| A
    K -->|是| L[发送企微告警]
    L --> M[标记告警已发送]
    M --> A
    J --> A
```

## 🎯 优化效果

### 1. 重启次数控制
- **优化前**：可能无限重启，浪费资源
- **优化后**：每个周期最多重启2次，避免无效重启

### 2. 重启间隔优化
- **优化前**：10分钟间隔，可能过于频繁
- **优化后**：4分钟间隔，给服务足够恢复时间

### 3. 告警及时性
- **优化前**：需要多次失败才告警
- **优化后**：2次重启失败后立即告警

### 4. 资源使用优化
- **减少无效重启**：避免对已经无法恢复的服务反复重启
- **降低系统负载**：减少不必要的API调用和系统操作
- **提高稳定性**：避免频繁重启对系统造成的冲击

## 📊 监控数据

### 新增监控指标
- `sglang_current_cycle_restarts`: 当前周期重启次数
- `sglang_restart_cycle_start`: 重启周期开始时间
- `sglang_alert_sent`: 告警发送状态
- `sglang_alert_sent_at`: 告警发送时间

### 告警内容优化
```
**重启周期信息**：
- 重启失败原因: 已重启2次仍无法恢复
- 当前周期重启次数: 2/2
- 周期开始时间: 2024-01-15 14:30:00
- 最后重启时间: 2024-01-15 14:38:00
- 历史总重启次数: 15

**处理说明**：
系统已按照重启策略自动重启2次，但SGLang服务仍无法恢复正常。

⚠️ **自动重启已达上限，需要人工介入处理！**
```

## 🚀 部署步骤

### 1. 数据库迁移
```bash
python almond_parser/migrations/add_sglang_restart_cycle_fields.py
```

### 2. 重启服务
```bash
python almond_parser/start_services.py
```

### 3. 验证功能
```bash
python almond_parser/scripts/test_restart_logic.py
```

## 🔍 测试验证

### 测试场景
1. **第一次失败**：应该执行第一次重启
2. **第二次失败**：等待4分钟后执行第二次重启
3. **第三次失败**：等待4分钟后发送告警，不再重启
4. **服务恢复**：重置重启周期，清除告警状态

### 验证指标
- ✅ 重启次数不超过2次
- ✅ 重启间隔至少4分钟
- ✅ 2次重启失败后发送告警
- ✅ 服务恢复后重置周期
- ✅ 告警内容包含周期信息

## 📈 预期收益

### 1. 运维效率提升
- **减少无效重启**：避免对无法恢复的服务反复重启
- **及时告警**：2次重启失败后立即通知运维人员
- **明确状态**：清楚显示重启周期和告警状态

### 2. 系统稳定性提升
- **降低系统负载**：减少频繁的重启操作
- **避免资源浪费**：不再对已损坏的服务无限重启
- **提高可靠性**：更智能的故障处理策略

### 3. 用户体验改善
- **减少服务中断**：避免频繁重启导致的服务波动
- **快速故障响应**：及时的告警通知确保快速处理
- **透明的状态信息**：清楚的重启和告警状态展示

---

通过这次优化，SGLang重启逻辑变得更加智能和高效，既避免了无效的重复重启，又确保了及时的故障告警！
