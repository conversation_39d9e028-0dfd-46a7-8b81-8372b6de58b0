#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
开发环境启动脚本
"""
import os
import sys
import subprocess
import signal
import time
from pathlib import Path
from loguru import logger


def start_service(name: str, command: list, cwd: str = None):
    """启动服务"""
    try:
        logger.info(f"🚀 启动 {name}...")
        
        process = subprocess.Popen(
            command,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        logger.info(f"✅ {name} 已启动 (PID: {process.pid})")
        return process
        
    except Exception as e:
        logger.error(f"❌ 启动 {name} 失败: {e}")
        return None


def main():
    """主函数"""
    logger.info("🌟 启动杏仁解析服务开发环境")
    
    # 检查当前目录
    current_dir = Path.cwd()
    if current_dir.name != "almond_parser":
        logger.error("请在 almond_parser 目录下运行此脚本")
        sys.exit(1)
    
    processes = []
    
    try:
        # 1. 启动 FastAPI 服务
        web_process = start_service(
            "FastAPI Web 服务",
            [sys.executable, "-m", "almond_parser.main"],
            cwd=str(current_dir.parent)
        )
        if web_process:
            processes.append(("FastAPI Web 服务", web_process))
        
        # 等待 Web 服务启动
        time.sleep(3)
        
        # 2. 启动 ARQ Worker
        worker_process = start_service(
            "ARQ Worker",
            [sys.executable, "-m", "almond_parser.worker"],
            cwd=str(current_dir.parent)
        )
        if worker_process:
            processes.append(("ARQ Worker", worker_process))
        
        if not processes:
            logger.error("❌ 没有成功启动任何服务")
            sys.exit(1)
        
        logger.info("🎉 所有服务启动完成!")
        logger.info("📍 Web 服务: http://localhost:8010")
        logger.info("📖 API 文档: http://localhost:8010/docs")
        logger.info("🛑 按 Ctrl+C 停止所有服务")
        
        # 等待中断信号
        try:
            while True:
                # 检查进程状态
                for name, process in processes:
                    if process.poll() is not None:
                        logger.warning(f"⚠️ {name} 已停止 (退出码: {process.returncode})")
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("🛑 收到停止信号，正在关闭服务...")
    
    finally:
        # 停止所有进程
        for name, process in processes:
            try:
                logger.info(f"🛑 停止 {name}...")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=5)
                    logger.info(f"✅ {name} 已停止")
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️ {name} 未在5秒内停止，强制终止...")
                    process.kill()
                    process.wait()
                    logger.info(f"✅ {name} 已强制停止")
                    
            except Exception as e:
                logger.error(f"❌ 停止 {name} 时出错: {e}")
        
        logger.info("👋 所有服务已停止")


if __name__ == "__main__":
    main()
