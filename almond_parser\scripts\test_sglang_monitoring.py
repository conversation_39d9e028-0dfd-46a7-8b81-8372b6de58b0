#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试SGLang监控功能
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.db.database import get_async_session
from almond_parser.services.sglang_monitor_service import SglangMonitorService
from almond_parser.services.sglang_restart_service import SglangRestartService
from almond_parser.services.sglang_alert_service import SglangAlertService


async def test_single_node_monitoring():
    """测试单个节点监控"""
    logger.info("🔍 测试单个节点SGLang监控...")
    
    async with get_async_session() as db:
        monitor_service = SglangMonitorService(db)
        
        # 获取第一个节点进行测试
        from almond_parser.db.models import MinerUNode
        from sqlalchemy import select
        
        result = await db.execute(
            select(MinerUNode).where(MinerUNode.is_enabled == True).limit(1)
        )
        node = result.scalar_one_or_none()
        
        if not node:
            logger.warning("没有找到启用的节点")
            return
        
        logger.info(f"测试节点: {node.name} ({node.base_url})")
        
        # 测试监控
        monitor_result = await monitor_service.monitor_node_sglang(node.id)
        
        if monitor_result["success"]:
            logger.success(f"✅ 监控成功:")
            logger.info(f"  - 节点状态: {monitor_result['sglang_status']}")
            logger.info(f"  - 健康状态: {'健康' if monitor_result['healthy'] else '异常'}")
            logger.info(f"  - 响应时间: {monitor_result.get('response_time', 'N/A')}s")
            logger.info(f"  - 连续失败: {monitor_result['consecutive_failures']}次")
            if monitor_result.get('error'):
                logger.warning(f"  - 错误信息: {monitor_result['error']}")
        else:
            logger.error(f"❌ 监控失败: {monitor_result.get('error')}")


async def test_batch_monitoring():
    """测试批量监控"""
    logger.info("🔍 测试批量SGLang监控...")
    
    async with get_async_session() as db:
        monitor_service = SglangMonitorService(db)
        
        # 批量监控
        result = await monitor_service.monitor_all_nodes_sglang()
        
        if result["success"]:
            logger.success(f"✅ 批量监控成功:")
            logger.info(f"  - 总节点数: {result['total_nodes']}")
            logger.info(f"  - 成功监控: {result['success_count']}")
            logger.info(f"  - 健康节点: {result['healthy_count']}")
            logger.info(f"  - 异常节点: {result['error_count']}")
        else:
            logger.error(f"❌ 批量监控失败: {result.get('error')}")


async def test_restart_service():
    """测试重启服务"""
    logger.info("🔄 测试SGLang重启服务...")
    
    async with get_async_session() as db:
        restart_service = SglangRestartService(db)
        
        # 获取需要重启的节点
        nodes_to_restart = await restart_service.get_nodes_need_restart()
        
        if nodes_to_restart:
            logger.info(f"发现 {len(nodes_to_restart)} 个节点需要重启")
            for node in nodes_to_restart:
                logger.info(f"  - 节点 {node.id} ({node.name}): 连续失败 {node.sglang_consecutive_failures} 次")
        else:
            logger.info("没有节点需要重启")
        
        # 测试自动重启逻辑（不实际执行重启）
        logger.info("测试自动重启逻辑...")
        result = await restart_service.auto_restart_failed_nodes()
        
        if result["success"]:
            logger.success(f"✅ 自动重启检查完成:")
            logger.info(f"  - 检查节点数: {result['total_checked']}")
            logger.info(f"  - 需要重启: {result['restart_count']}")
            if result['restart_count'] > 0:
                logger.info(f"  - 重启成功: {result['success_count']}")
                logger.info(f"  - 重启失败: {result['error_count']}")
        else:
            logger.error(f"❌ 自动重启检查失败: {result.get('error')}")


async def test_alert_service():
    """测试告警服务"""
    logger.info("🚨 测试SGLang告警服务...")
    
    async with get_async_session() as db:
        alert_service = SglangAlertService(db)
        
        # 检查告警
        result = await alert_service.check_and_send_alerts()
        
        if result["success"]:
            logger.success(f"✅ 告警检查完成:")
            logger.info(f"  - 需要告警节点: {result.get('total_nodes', 0)}")
            logger.info(f"  - 发送告警数: {result.get('alert_count', 0)}")
            
            if result.get('failed_nodes'):
                logger.warning("异常节点列表:")
                for node_info in result['failed_nodes']:
                    logger.warning(f"  - 节点{node_info['node_id']}: {node_info['name']} ({node_info['status']})")
        else:
            logger.error(f"❌ 告警检查失败: {result.get('error')}")


async def test_mineru_api_connectivity():
    """测试MinerU-API连接性"""
    logger.info("🔗 测试MinerU-API连接性...")
    
    async with get_async_session() as db:
        from almond_parser.db.models import MinerUNode
        from sqlalchemy import select
        import aiohttp
        
        result = await db.execute(
            select(MinerUNode).where(MinerUNode.is_enabled == True)
        )
        nodes = result.scalars().all()
        
        if not nodes:
            logger.warning("没有找到启用的节点")
            return
        
        for node in nodes:
            logger.info(f"测试节点: {node.name} ({node.base_url})")
            
            try:
                timeout = aiohttp.ClientTimeout(total=10)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 测试基本连接
                    async with session.get(f"{node.base_url}/health") as response:
                        if response.status == 200:
                            logger.success(f"  ✅ MinerU-API连接正常")
                        else:
                            logger.warning(f"  ⚠️ MinerU-API响应异常: HTTP {response.status}")
                    
                    # 测试SGLang状态接口
                    async with session.get(f"{node.base_url}/sglang/status") as response:
                        if response.status == 200:
                            status_data = await response.json()
                            running = status_data.get("running", False)
                            logger.success(f"  ✅ SGLang状态接口正常: {'运行中' if running else '未运行'}")
                        else:
                            logger.warning(f"  ⚠️ SGLang状态接口异常: HTTP {response.status}")
                    
                    # 测试SGLang健康接口
                    async with session.get(f"{node.base_url}/sglang/health") as response:
                        if response.status == 200:
                            health_data = await response.json()
                            healthy = health_data.get("health_check", {}).get("healthy", False)
                            logger.success(f"  ✅ SGLang健康接口正常: {'健康' if healthy else '异常'}")
                        else:
                            logger.warning(f"  ⚠️ SGLang健康接口异常: HTTP {response.status}")
                            
            except Exception as e:
                logger.error(f"  ❌ 连接失败: {e}")


async def main():
    """主函数"""
    logger.info("🚀 开始SGLang监控功能测试")
    
    try:
        # 1. 测试MinerU-API连接性
        await test_mineru_api_connectivity()
        
        print("\n" + "="*60 + "\n")
        
        # 2. 测试单个节点监控
        await test_single_node_monitoring()
        
        print("\n" + "="*60 + "\n")
        
        # 3. 测试批量监控
        await test_batch_monitoring()
        
        print("\n" + "="*60 + "\n")
        
        # 4. 测试重启服务
        await test_restart_service()
        
        print("\n" + "="*60 + "\n")
        
        # 5. 测试告警服务
        await test_alert_service()
        
        logger.success("🎉 SGLang监控功能测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
