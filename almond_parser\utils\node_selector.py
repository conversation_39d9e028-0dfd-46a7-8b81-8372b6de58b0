# -*- encoding: utf-8 -*-
"""
节点选择工具 - 支持解析模式兼容性匹配
"""
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_
from loguru import logger

from almond_parser.db.models import MinerUNode, NodeStatus, ServiceType, ParseMode
from almond_parser.utils.parse_mode_compatibility import ParseModeCompatibility


class NodeSelector:
    """节点选择器 - 支持解析模式兼容性匹配"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.compatibility = ParseModeCompatibility()

    def _map_api_mode_to_db_enum(self, api_mode: str) -> str:
        """
        将API层面的解析模式映射回数据库枚举值

        Args:
            api_mode: API层面的解析模式 (如 'vlm-sglang-client', 'vlm', 'sglang' 等)

        Returns:
            数据库枚举对应的模式值
        """
        if not api_mode:
            return "auto"

        api_mode = api_mode.lower().strip()

        # API模式到数据库枚举的映射
        mode_mapping = {
            "vlm-sglang-client": "sglang",
            "vlm": "sglang",
            "sglang": "sglang",
            "pipeline": "pipeline",
            "auto": "auto"
        }

        mapped_mode = mode_mapping.get(api_mode, "auto")
        logger.debug(f"模式映射: {api_mode} -> {mapped_mode}")

        return mapped_mode
    
    async def select_node(
        self,
        service_type: str = "auto",
        parse_mode: Optional[str] = None,
        file_type: Optional[str] = None,
        use_compatibility: bool = True
    ) -> Optional[MinerUNode]:
        """
        智能选择最佳节点 - 支持兼容性匹配

        Args:
            service_type: 服务类型 ("auto", "document", "knowledge_base")
            parse_mode: 解析模式 (可选)
            file_type: 文件类型，用于自动推断服务类型
            use_compatibility: 是否使用兼容性匹配

        Returns:
            最佳节点或 None
        """
        try:
            # 特殊处理：当服务类型为"auto"时，使用智能全局节点选择
            if service_type == "auto":
                return await self._select_auto_service_node(parse_mode, file_type, use_compatibility)

            # 智能推断服务类型（非auto情况）
            inferred_service_type = self._infer_service_type(file_type) if file_type else service_type
            if inferred_service_type != service_type:
                logger.info(f"基于文件类型推断服务类型: {inferred_service_type} (原始: {service_type}, 文件类型: {file_type})")
                service_type = inferred_service_type

            # 映射服务类型
            service_type_enum = {
                "document": ServiceType.DOCUMENT,
                "knowledge_base": ServiceType.KNOWLEDGE_BASE,
                "universal": ServiceType.UNIVERSAL
            }.get(service_type)

            if not service_type_enum:
                logger.error(f"不支持的服务类型: {service_type}")
                return None

            # 使用兼容性匹配选择节点
            if use_compatibility and parse_mode:
                node, execution_mode = await self._find_compatible_node(service_type_enum, parse_mode)
                if node:
                    logger.info(f"选择兼容节点: {node.name} (服务类型: {service_type}, 请求模式: {parse_mode}, 执行模式: {execution_mode})")
                    # 将执行模式信息附加到节点对象（用于后续API调用）
                    node._execution_mode = execution_mode
                    return node

            # 回退到传统匹配方式
            return await self.select_best_node_legacy(service_type, parse_mode, file_type)

        except Exception as e:
            logger.error(f"选择节点失败: {e}")
            return None

    async def select_best_node_legacy(
        self,
        service_type: str = "auto",
        parse_mode: Optional[str] = None,
        file_type: Optional[str] = None
    ) -> Optional[MinerUNode]:
        """
        传统节点选择方法（严格匹配）

        Args:
            service_type: 服务类型 ("auto", "document", "knowledge_base")
            parse_mode: 解析模式 (可选)
            file_type: 文件类型，用于自动推断服务类型

        Returns:
            最佳节点或 None
        """
        try:
            # 智能推断服务类型
            if service_type == "auto":
                service_type = self._infer_service_type(file_type)
                logger.info(f"自动推断服务类型: {service_type} (基于文件类型: {file_type})")

            # 映射服务类型
            service_type_enum = {
                "document": ServiceType.DOCUMENT,
                "knowledge_base": ServiceType.KNOWLEDGE_BASE,
                "universal": ServiceType.UNIVERSAL
            }.get(service_type)

            if not service_type_enum:
                logger.error(f"不支持的服务类型: {service_type}")
                return None

            # 如果指定了具体类型，优先查找专用节点
            if service_type in ["document", "knowledge_base"]:
                # 第一优先级：查找专用节点
                node = await self._find_dedicated_node(service_type_enum, parse_mode)
                if node:
                    logger.info(f"选择专用节点: {node.name} (类型: {service_type})")
                    return node

            # 第二优先级：查找通用节点
            node = await self._find_universal_node(parse_mode)
            if node:
                logger.info(f"选择通用节点: {node.name} (处理 {service_type} 类型任务)")
                return node

            logger.warning(f"没有找到可用的节点处理 {service_type} 类型任务")
            return None

        except Exception as e:
            logger.error(f"选择节点失败: {e}")
            return None

    async def _select_auto_service_node(
        self,
        parse_mode: Optional[str] = None,
        file_type: Optional[str] = None,
        use_compatibility: bool = True
    ) -> Optional[MinerUNode]:
        """
        服务类型为"auto"时的智能节点选择

        策略：
        1. 将所有在线节点都视为可用的通用节点
        2. 解析模式为"auto"时，优先分配sglang节点使用vlm模式
        3. 如果没有sglang节点或sglang节点忙碌，使用pipeline节点

        Args:
            parse_mode: 解析模式
            file_type: 文件类型（用于推断优先级）
            use_compatibility: 是否使用兼容性匹配

        Returns:
            选择的节点或None
        """
        try:
            logger.info(f"自动服务类型节点选择: 解析模式={parse_mode}, 文件类型={file_type}")

            # 如果使用兼容性匹配且指定了解析模式
            if use_compatibility and parse_mode and parse_mode != "auto":
                # 直接使用兼容性匹配，将所有节点视为通用节点
                node, execution_mode = await self._find_compatible_node(ServiceType.UNIVERSAL, parse_mode)
                if node:
                    logger.info(f"自动选择兼容节点: {node.name} (请求模式: {parse_mode}, 执行模式: {execution_mode})")
                    node._execution_mode = execution_mode
                    return node

            # 解析模式为"auto"时的智能选择策略
            if not parse_mode or parse_mode == "auto":
                # 优先尝试sglang节点（用于vlm模式）
                sglang_node = await self._find_any_available_sglang_node()
                if sglang_node:
                    logger.info(f"自动选择sglang节点: {sglang_node.name} (执行vlm模式)")
                    sglang_node._execution_mode = "vlm"
                    return sglang_node

                # 如果没有可用的sglang节点，选择pipeline节点
                pipeline_node = await self._find_any_available_pipeline_node()
                if pipeline_node:
                    logger.info(f"自动选择pipeline节点: {pipeline_node.name} (执行pipeline模式)")
                    pipeline_node._execution_mode = "pipeline"
                    return pipeline_node

            # 最后尝试查找任何可用的通用节点
            universal_node = await self._find_universal_node(parse_mode)
            if universal_node:
                logger.info(f"自动选择通用节点: {universal_node.name}")
                return universal_node

            logger.warning("自动服务类型：没有找到任何可用节点")
            return None

        except Exception as e:
            logger.error(f"自动服务类型节点选择失败: {e}")
            return None

    # 保持向后兼容
    async def select_best_node(self, *args, **kwargs) -> Optional[MinerUNode]:
        """向后兼容的方法，默认使用兼容性匹配"""
        return await self.select_node(*args, **kwargs)

    async def _find_compatible_node(
        self,
        service_type: ServiceType,
        request_mode: str
    ) -> Tuple[Optional[MinerUNode], Optional[str]]:
        """
        查找兼容的节点 - 支持资源保护策略

        策略：
        1. pipe请求：优先分配给纯pipeline节点，保护sglang节点资源
        2. vlm/sglang请求：直接分配给sglang节点

        Args:
            service_type: 服务类型
            request_mode: 请求的解析模式

        Returns:
            (节点, 执行模式) 或 (None, None)
        """
        try:
            # 直接使用原始请求模式进行判断，避免标准化影响资源分配策略
            original_mode = request_mode.lower().strip() if request_mode else "auto"

            logger.debug(f"查找兼容节点: 请求模式={request_mode}, 原始模式={original_mode}, 服务类型={service_type}")

            # 如果是pipe请求，优先查找纯pipeline节点，保护sglang节点资源
            if original_mode in ["pipeline", "pipe"]:
                return await self._find_pipeline_optimized_node(service_type, request_mode)

            # 如果是vlm/sglang请求，查找sglang节点
            else:
                return await self._find_vlm_optimized_node(service_type, request_mode)

        except Exception as e:
            logger.error(f"查找兼容节点失败: {e}")
            return None, None

    async def _find_dedicated_node_by_mode(
        self,
        service_type: ServiceType,
        node_mode: str
    ) -> Optional[MinerUNode]:
        """根据模式查找专用节点"""
        try:
            # 转换为枚举
            parse_mode_enum = getattr(ParseMode, node_mode.upper(), None)
            if not parse_mode_enum:
                return None

            conditions = [
                MinerUNode.is_enabled == True,
                MinerUNode.status == NodeStatus.ONLINE,
                MinerUNode.service_type == service_type,
                MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks,
                MinerUNode.parse_mode == parse_mode_enum
            ]

            result = await self.db.execute(
                select(MinerUNode)
                .where(*conditions)
                .order_by(
                    MinerUNode.current_tasks.asc(),  # 当前任务数最少
                    MinerUNode.priority.desc()       # 优先级最高
                )
                .limit(1)
            )

            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"查找专用节点失败: {e}")
            return None

    async def _find_universal_node_by_mode(
        self,
        node_mode: str
    ) -> Optional[MinerUNode]:
        """根据模式查找通用节点"""
        try:
            # 将API模式映射为数据库枚举值
            db_mode = self._map_api_mode_to_db_enum(node_mode)
            # 转换为枚举
            parse_mode_enum = getattr(ParseMode, db_mode.upper(), None)
            if not parse_mode_enum:
                return None

            conditions = [
                MinerUNode.is_enabled == True,
                MinerUNode.status == NodeStatus.ONLINE,
                MinerUNode.service_type == ServiceType.UNIVERSAL,
                MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks,
                MinerUNode.parse_mode == parse_mode_enum
            ]

            result = await self.db.execute(
                select(MinerUNode)
                .where(*conditions)
                .order_by(
                    MinerUNode.current_tasks.asc(),  # 当前任务数最少
                    MinerUNode.priority.desc()       # 优先级最高
                )
                .limit(1)
            )

            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"查找通用节点失败: {e}")
            return None

    async def _find_pipeline_optimized_node(
        self,
        service_type: ServiceType,
        request_mode: str
    ) -> Tuple[Optional[MinerUNode], Optional[str]]:
        """
        优化的pipeline节点查找 - 优先使用纯pipeline节点，保护sglang节点资源

        查找策略：
        1. 优先查找纯pipeline节点（CPU节点）
        2. 如果没有，再查找sglang节点作为降级
        """
        try:
            logger.info(f"查找pipeline优化节点: 服务类型={service_type}, 请求模式={request_mode}")

            # 第一优先级：查找纯pipeline节点
            node = await self._find_pure_pipeline_node(service_type)
            if node:
                execution_mode = "pipeline"  # 纯pipeline节点只能执行pipeline模式
                logger.info(f"找到纯pipeline节点: {node.name} (节点模式: pipeline, 执行模式: {execution_mode})")
                return node, execution_mode

            # 第二优先级：查找sglang节点作为降级（sglang节点支持pipeline模式）
            node = await self._find_sglang_node(service_type)
            if node:
                execution_mode = "pipeline"  # 在sglang节点上执行pipeline模式
                logger.info(f"降级到sglang节点: {node.name} (节点模式: sglang, 执行模式: {execution_mode})")
                return node, execution_mode

            logger.warning(f"未找到可用的pipeline节点: 服务类型={service_type}")
            return None, None

        except Exception as e:
            logger.error(f"查找pipeline优化节点失败: {e}")
            return None, None

    async def _find_vlm_optimized_node(
        self,
        service_type: ServiceType,
        request_mode: str
    ) -> Tuple[Optional[MinerUNode], Optional[str]]:
        """
        优化的VLM节点查找 - 直接查找sglang节点

        查找策略：
        1. 直接查找sglang节点（GPU节点）
        2. 使用请求的执行模式
        """
        try:
            logger.info(f"查找VLM优化节点: 服务类型={service_type}, 请求模式={request_mode}")

            # 查找sglang节点
            node = await self._find_sglang_node(service_type)
            if node:
                execution_mode = self.compatibility.get_best_execution_mode("sglang", request_mode)
                logger.info(f"找到sglang节点: {node.name} (节点模式: sglang, 执行模式: {execution_mode})")
                return node, execution_mode

            logger.warning(f"未找到可用的sglang节点: 服务类型={service_type}")
            return None, None

        except Exception as e:
            logger.error(f"查找VLM优化节点失败: {e}")
            return None, None

    def _infer_service_type(self, file_type: Optional[str]) -> str:
        """
        根据文件类型智能推断服务类型

        Args:
            file_type: 文件扩展名

        Returns:
            推断的服务类型
        """
        if not file_type:
            return "document"  # 默认为文档解析

        # 文档类型映射
        document_types = {"pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx"}
        image_types = {"jpg", "jpeg", "png", "bmp", "tiff"}

        file_type_lower = file_type.lower()

        if file_type_lower in document_types or file_type_lower in image_types:
            return "document"
        else:
            # 其他类型默认为文档解析
            return "document"
    
    async def _find_dedicated_node(
        self, 
        service_type: ServiceType, 
        parse_mode: Optional[str] = None
    ) -> Optional[MinerUNode]:
        """查找专用节点"""
        conditions = [
            MinerUNode.is_enabled == True,
            MinerUNode.status == NodeStatus.ONLINE,
            MinerUNode.service_type == service_type,
            MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks
        ]
        
        if parse_mode:
            from almond_parser.db.models import ParseMode
            parse_mode_enum = getattr(ParseMode, parse_mode.upper(), None)
            if parse_mode_enum:
                conditions.append(MinerUNode.parse_mode == parse_mode_enum)
        
        result = await self.db.execute(
            select(MinerUNode)
            .where(*conditions)
            .order_by(
                MinerUNode.current_tasks.asc(),  # 当前任务数最少
                MinerUNode.priority.desc()       # 优先级最高
            )
            .limit(1)
        )
        
        return result.scalar_one_or_none()
    
    async def _find_universal_node(
        self, 
        parse_mode: Optional[str] = None
    ) -> Optional[MinerUNode]:
        """查找通用节点"""
        conditions = [
            MinerUNode.is_enabled == True,
            MinerUNode.status == NodeStatus.ONLINE,
            MinerUNode.service_type == ServiceType.UNIVERSAL,
            MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks
        ]
        
        if parse_mode:
            from almond_parser.db.models import ParseMode
            # 将API模式映射为数据库枚举值
            db_mode = self._map_api_mode_to_db_enum(parse_mode)
            parse_mode_enum = getattr(ParseMode, db_mode.upper(), None)
            if parse_mode_enum:
                conditions.append(MinerUNode.parse_mode == parse_mode_enum)
        
        result = await self.db.execute(
            select(MinerUNode)
            .where(*conditions)
            .order_by(
                MinerUNode.current_tasks.asc(),  # 当前任务数最少
                MinerUNode.priority.desc()       # 优先级最高
            )
            .limit(1)
        )
        
        return result.scalar_one_or_none()
    
    async def get_nodes_by_service_type(
        self, 
        service_type: str,
        include_universal: bool = True
    ) -> List[MinerUNode]:
        """
        根据服务类型获取节点列表
        
        Args:
            service_type: 服务类型
            include_universal: 是否包含通用节点
        
        Returns:
            节点列表
        """
        try:
            service_type_enum = {
                "document": ServiceType.DOCUMENT,
                "knowledge_base": ServiceType.KNOWLEDGE_BASE
            }.get(service_type)

            if not service_type_enum:
                return []

            conditions = [
                MinerUNode.is_enabled == True,
                MinerUNode.status == NodeStatus.ONLINE
            ]
            
            if include_universal:
                conditions.append(
                    or_(
                        MinerUNode.service_type == service_type_enum,
                        MinerUNode.service_type == ServiceType.UNIVERSAL
                    )
                )
            else:
                conditions.append(MinerUNode.service_type == service_type_enum)
            
            result = await self.db.execute(
                select(MinerUNode)
                .where(*conditions)
                .order_by(
                    MinerUNode.service_type.asc(),  # 专用节点优先
                    MinerUNode.current_tasks.asc(),
                    MinerUNode.priority.desc()
                )
            )
            
            return result.scalars().all()

        except Exception as e:
            logger.error(f"获取节点列表失败: {e}")
            return []
    
    async def get_service_type_stats(self) -> dict:
        """获取各服务类型的节点统计"""
        try:
            result = await self.db.execute(
                select(
                    MinerUNode.service_type,
                    MinerUNode.status
                )
                .where(MinerUNode.is_enabled == True)
            )
            
            nodes = result.all()
            stats = {
                "document": {"total": 0, "online": 0, "offline": 0},
                "knowledge_base": {"total": 0, "online": 0, "offline": 0},
                "universal": {"total": 0, "online": 0, "offline": 0}
            }
            
            for node in nodes:
                service_type = node.service_type.value
                status = node.status.value
                
                if service_type in stats:
                    stats[service_type]["total"] += 1
                    if status == "online":
                        stats[service_type]["online"] += 1
                    else:
                        stats[service_type]["offline"] += 1
            
            return stats

        except Exception as e:
            logger.error(f"获取服务类型统计失败: {e}")
            return {}

    async def _find_pure_pipeline_node(self, service_type: ServiceType) -> Optional[MinerUNode]:
        """
        查找纯pipeline节点（CPU节点）

        Args:
            service_type: 服务类型

        Returns:
            纯pipeline节点或None
        """
        try:
            # 先尝试专用节点
            if service_type in [ServiceType.DOCUMENT, ServiceType.KNOWLEDGE_BASE]:
                node = await self._find_dedicated_node_by_mode(service_type, "pipeline")
                if node:
                    logger.debug(f"找到专用pipeline节点: {node.name}")
                    return node

            # 再尝试通用节点
            node = await self._find_universal_node_by_mode("pipeline")
            if node:
                logger.debug(f"找到通用pipeline节点: {node.name}")
                return node

            return None

        except Exception as e:
            logger.error(f"查找纯pipeline节点失败: {e}")
            return None

    async def _find_sglang_node(self, service_type: ServiceType) -> Optional[MinerUNode]:
        """
        查找sglang节点（GPU节点）

        Args:
            service_type: 服务类型

        Returns:
            sglang节点或None
        """
        try:
            # 先尝试专用节点
            if service_type in [ServiceType.DOCUMENT, ServiceType.KNOWLEDGE_BASE]:
                # 尝试查找sglang节点
                node = await self._find_dedicated_node_by_mode(service_type, "sglang")
                if node:
                    logger.debug(f"找到专用sglang节点: {node.name}")
                    return node

                # 尝试查找vlm节点（兼容sglang）
                node = await self._find_dedicated_node_by_mode(service_type, "vlm")
                if node:
                    logger.debug(f"找到专用vlm节点: {node.name}")
                    return node

            # 再尝试通用节点
            node = await self._find_universal_node_by_mode("sglang")
            if node:
                logger.debug(f"找到通用sglang节点: {node.name}")
                return node

            node = await self._find_universal_node_by_mode("vlm")
            if node:
                logger.debug(f"找到通用vlm节点: {node.name}")
                return node

            return None

        except Exception as e:
            logger.error(f"查找sglang节点失败: {e}")
            return None

    async def _find_any_available_sglang_node(self) -> Optional[MinerUNode]:
        """查找任何可用的sglang节点（忽略服务类型限制）"""
        try:
            result = await self.db.execute(
                select(MinerUNode)
                .where(
                    MinerUNode.is_enabled == True,
                    MinerUNode.status == NodeStatus.ONLINE,
                    MinerUNode.parse_mode == ParseMode.SGLANG,
                    MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks
                )
                .order_by(
                    MinerUNode.current_tasks.asc(),  # 负载最低
                    MinerUNode.priority.desc()       # 优先级最高
                )
                .limit(1)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"查找任何可用sglang节点失败: {e}")
            return None

    async def _find_any_available_pipeline_node(self) -> Optional[MinerUNode]:
        """查找任何可用的pipeline节点（忽略服务类型限制）"""
        try:
            result = await self.db.execute(
                select(MinerUNode)
                .where(
                    MinerUNode.is_enabled == True,
                    MinerUNode.status == NodeStatus.ONLINE,
                    MinerUNode.parse_mode == ParseMode.PIPELINE,
                    MinerUNode.current_tasks < MinerUNode.max_concurrent_tasks
                )
                .order_by(
                    MinerUNode.current_tasks.asc(),  # 负载最低
                    MinerUNode.priority.desc()       # 优先级最高
                )
                .limit(1)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"查找任何可用pipeline节点失败: {e}")
            return None


# 便捷函数
async def select_document_node(db: AsyncSession, parse_mode: Optional[str] = None) -> Optional[MinerUNode]:
    """选择文档解析节点"""
    selector = NodeSelector(db)
    return await selector.select_best_node("document", parse_mode)


async def select_knowledge_base_node(db: AsyncSession, parse_mode: Optional[str] = None) -> Optional[MinerUNode]:
    """选择知识库解析节点"""
    selector = NodeSelector(db)
    return await selector.select_best_node("knowledge_base", parse_mode)
